import { Injectable } from '@nestjs/common';
import { UserServiceV4 } from './user.service.v4';
import {
  kBasicDetailsScreen,
  kLspBasicDetailsScreen,
} from 'src/v4/serverDriverUI/basicDetailsScreen';
import { MiscServiceV4 } from '../misc/misc.service.v4';
import { BankingSharedService } from 'src/shared/banking.service';

@Injectable()
export class DynamicRouteServiceV4 {
  constructor(
    private readonly userService: UserServiceV4,
    private readonly miscService: MiscServiceV4,
    private readonly sharedBanking: BankingSharedService,
  ) {}

  async dynamicViewData(reqData) {
    const appType = reqData?.appType;
    const userData = await this.userService.getUserData(reqData, [
      'communicationLanguage',
      'kycId',
    ]);
    if (userData?.message) return userData;

    const masterData = userData?.masterData;
    const statusData = masterData?.status;
    const selfieId = userData?.selfieId;
    let communicationLanguage = false;
    const language = userData?.communicationLanguage;
    if (language == 1 || language == 2) communicationLanguage = true;

    const options = await this.userService.getPurposeList();
    const bankOptions = await this.miscService.getAvailableBankList();
    const purposeList = options.map((option) => ({
      id: option.id,
      value: option.purposeName,
    }));
    const bankStatementDate =
      await this.sharedBanking.getLatestMonthBankStatement(reqData);
    let { title = '' } = bankStatementDate || {};
    title = title.replace(
      /([A-Z]+)\s(\d{4})/g,
      (_, m, y) => `${m[0]}${m.slice(1).toLowerCase()} ${y.slice(-2)}`,
    );

    const dateRange = {
      title,
      consistingTitle: title ? `Consisting of ${title}` : '',
    };
    const bankList = [
      ...bankOptions.map(({ id, bankName, bankImg }) => ({
        id,
        value: bankName,
        bankImg: bankImg,
      })),
      {
        id: -1,
        value: 'Other',
        bankImg:
          'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BANK-IMAGES%2FCOMMON.webp',
        subFieldKey: 'bankStatement',
        subField: {
          type: 8,
          key: 'bankStatement',
          padding: { top: 15 },
          isBinary: true,
          isVertical: false,
          labelText: 'Upload 4 months Bank Statements in a single PDF',
          title: dateRange.consistingTitle,
          description: '',
          buttonLabel: 'choose',
          directoryName: 'APP_OTHER_BANK_STATEMENTS',
          allowedExtensions: ['pdf', 'PDF'],
          mime: ['application/pdf'],
        },
      },
    ];

    const lspBankIcons = [
      ...bankOptions.map(({ id, bankName, lspBankImg }) => ({
        id,
        value: bankName,
        lspBankImg: lspBankImg,
      })),
      {
        id: -1,
        value: 'Other',
        lspBankImg:
          'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmr6e2h4phul/b/backend-ntw8xe0cg9d9/o/BANK-ICONS%2FCOMMON.webp',
        subFieldKey: 'bankStatement',
        subField: {
          type: 6,
          key: 'bankStatement',
          padding: { top: 15 },
          isBinary: true,
          isVertical: false,
          labelText: 'Upload 4 months Bank Statements in a single PDF',
          title: dateRange.title,
          description: 'Valid format: PDF',
          buttonLabel: 'Select file',
          directoryName: 'APP_OTHER_BANK_STATEMENTS',
          allowedExtensions: ['pdf', 'PDF'],
        },
      },
    ];

    const kycId = userData?.kycId;
    if (
      !kycId ||
      statusData?.basic !== 1 ||
      statusData?.professional !== 1 ||
      statusData?.personal !== 1
    ) {
      const basicScreen =
        appType == '1'
          ? kBasicDetailsScreen(communicationLanguage, purposeList, bankList)
          : kLspBasicDetailsScreen(
              communicationLanguage,
              purposeList,
              lspBankIcons,
            );
      return basicScreen;
    }
    return {};
  }
}
