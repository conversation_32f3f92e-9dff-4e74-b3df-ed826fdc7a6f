// Imports
import { DigiTapService } from './digitap.service';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';

@Controller('thirdParty/digitap')
export class DigiTapController {
  constructor(private readonly service: DigiTapService) {}

  @Post('generateUrl')
  async funGenerateUrl(@Body() body, @Res() res) {
    try {
      const data = await this.service.generateUrl(body);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('pullAadhaar')
  async funPullAadhaar(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.pullAadhaar(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('invitation')
  async funInvitation(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.invitation(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
}
