// Imports
import { Sequelize } from 'sequelize-typescript';
import { EnvConfig } from 'src/configs/env.config';
import {
  notificationModels,
  smsModels,
  whatsappModels,
} from 'src/constants/mailTracker';
import { EPFODetailsEntity } from 'src/entities/EPFODetails';
import { ExotelCallHistory } from 'src/entities/ExotelCallHistory.entity';
import { InstallAppEntity } from 'src/entities/InstallApp.entities';
import { ReasonsEntity } from 'src/entities/Reasons.entity';
import { AccessOfListEntity } from 'src/entities/access_of_list.entity';
import { AccessOfRoleEntity } from 'src/entities/access_of_role.entity';
import { accountSectionSummary } from 'src/entities/accountSectionSummary.entity';
import { ActiveLoanAddressesEntity } from 'src/entities/activeLoanAddress.entity';
import { AddressesEntity } from 'src/entities/addresses.entity';
import { admin } from 'src/entities/admin.entity';
import { AdminRole } from 'src/entities/admin.role.entity';
import { AdminEligibilityEntity } from 'src/entities/adminEligibility.entity';
import { AgentCallHistoryEntity } from 'src/entities/agentCallHistory.entity';
import { AAEntity } from 'src/entities/aggregator.entity';
import { APIAccessListEntity } from 'src/entities/api.list.entity';
import { assetsClassification } from 'src/entities/assetsClassification.entity';
import { BankList } from 'src/entities/bank.entity';
import { BankingEntity } from 'src/entities/banking.entity';
import { OthersBankStatementEntity } from 'src/entities/othersBankStatment.Entity';
import { banks } from 'src/entities/banks.entity';
import { bannerEntity } from 'src/entities/banner.entity';
import { BatchCibilDataEntity } from 'src/entities/batchCibilData.entity';
import { BatchCibilFileTrackingEntity } from 'src/entities/batchCibilFileTracking.entity';
import { BlacklistCompanyEntity } from 'src/entities/blacklistCompanies.entity';
import { BlockUserHistoryEntity } from 'src/entities/block.user.history.entity';
import { branches } from 'src/entities/branches.entity';
import { CacheEntity } from 'src/entities/cache_entity';
import { ChangeLogsEntity } from 'src/entities/change.logs.entity';
import { CIBILEntity } from 'src/entities/cibil.entity';
import { CibilScoreEntity } from 'src/entities/cibil.score.entity';
import { cibilTriggerEntity } from 'src/entities/cibilTrigger.entity';
import { CollectionPerformanceReport } from 'src/entities/collectionPerformanceReport.entity';
import { GoogleCompanyResultEntity } from 'src/entities/company.entity';
import { complianceTracker } from 'src/entities/complianceTracker.entity';
import { Configuration } from 'src/entities/configuration.entity';
import { contactDetailEntity } from 'src/entities/contact.entity';
import { ContactLogEntity } from 'src/entities/contact.log.entity';
import { crmActivity } from 'src/entities/crm.entity';
import { CrmReasonEntity } from 'src/entities/crm.reasons.entity';
import { crmDisposition } from 'src/entities/crmDisposition.entity';
import { crmStatus } from 'src/entities/crmStatus.entity';
import { crmTitle } from 'src/entities/crmTitle.entity';
import { CronTrakingEntity } from 'src/entities/cron.track.entity';
import { DefaulterOnlineEntity } from 'src/entities/defaulterOnline.entity';
import { Department } from 'src/entities/department.entity';
import { employmentDesignation } from 'src/entities/designation.entity';
import { device } from 'src/entities/device.entity';
import { DeviceInfoAndInstallAppEntity } from 'src/entities/device.info.entity';
import { DeviceSIM } from 'src/entities/deviceSIM.entity';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import { DownloaAppTrack } from 'src/entities/downloads.app.entity';
import { DynamicEntity } from 'src/entities/dynamic.entity';
import { EmiEntity } from 'src/entities/emi.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { EmploymentHistoryDetailsEntity } from 'src/entities/employment.history.entity';
import { employmentType } from 'src/entities/employment.type';
import { esignEntity } from 'src/entities/esign.entity';
import { ExotelCategoryEntity } from 'src/entities/exotelCategory.entity';
import { ExperianScoreEntity } from 'src/entities/experianScore.entity';
import { fcmTokenEntity } from 'src/entities/fcmTokenEntity.entity';
import { FetchDataEntity } from 'src/entities/fetchData.entity';
import { FinvuEntity } from 'src/entities/finvu.entity';
import { FlowEntity } from 'src/entities/flow.entity';
import { CallKaroEntity } from 'src/entities/callkaro.entity';
import { FormUsers } from 'src/entities/formUsers.entity';
import { FramesEntity } from 'src/entities/frames_entity';
import { GoogleCoordinatesEntity } from 'src/entities/googleCoordinates.entity';
import { GoogleFileEntity } from 'src/entities/google_file_entity';
import { HashPhoneEntity } from 'src/entities/hashPhone.entity';
import { HealthDataEntity } from 'src/entities/healthData.entity';
import { HypothecationEntity } from 'src/entities/hypothecation.entity';
import { HypothecationHistoryEntity } from 'src/entities/hypothecationHistory.entity';
import { HypothecationPaymentsEntity } from 'src/entities/hypothecationPayment.entity';
import { InsuranceEntity } from 'src/entities/insurance.entity';
import { ipMasterEntity } from 'src/entities/ipMaster.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { LCREntity } from 'src/entities/lcr.entity';
import { LeadTrackingEntity } from 'src/entities/leadTracking.entity';
import { LeadTrackingFileEntity } from 'src/entities/leadTrackingFile.entity';
import { LeadTrackingIp } from 'src/entities/leadTrackingIp.entities';
import { LeadEntitiy } from 'src/entities/leads.entity';
import { LegalCollectionEntity } from 'src/entities/legal.collection.entity';
import { LegalConsigment } from 'src/entities/legal.consignment.entity';
import { userLoanDecline } from 'src/entities/loan.decline.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { loanPurpose } from 'src/entities/loan.purpose.entity';
import { LoanDetails } from 'src/entities/loan_details.entity';
import { LocationEntity } from 'src/entities/location.entity';
import { MailTrackerArchive } from 'src/entities/maiTracker.archive.entity';
import { MailTrackerEntity } from 'src/entities/mail.tracker.entity';
import { mandateEntity } from 'src/entities/mandate.entity';
import { ManualVerifiedCompanyEntity } from 'src/entities/manual.verified.company.entity';
import { ManualVerifiedWorkEmailEntity } from 'src/entities/manualVerifiedWorkEmail.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { ChatDocumentEntity } from 'src/entities/media.entity';
import { MetricsEntity } from 'src/entities/metrics.entity';
import { missMatchLogs } from 'src/entities/missMatchLogs.entity';
import { MsgEntity } from 'src/entities/msg.entity';
import { userNetBankingDetails } from 'src/entities/netBanking.entity';
import { LegalNoticeEntity } from 'src/entities/notice.entity';
import { OtherPermissionDataEntity } from 'src/entities/otherPermissionData.entity';
import { PaymentLinkEntity } from 'src/entities/paymentLink.entity';
import { PeriodicEntity } from 'src/entities/periodic.entity';
import { PhoneEntity } from 'src/entities/phone.entity';
import { PredictionEntity } from 'src/entities/prediction.entity';
import { PromoCodeEntity } from 'src/entities/promocode.entity';
import { ProteanEntity } from 'src/entities/protean.entity';
import { QualityParameterEntity } from 'src/entities/qualityParameter.entity';
import { RBIGuidelineEntity } from 'src/entities/rbiGuideline.entity';
import { ReportListEntity } from 'src/entities/report.list.entity';
import { ReportHistoryEntity } from 'src/entities/reportHistory.entity';
import { RequestSupportEntity } from 'src/entities/request_support.entity';
import { ResponseEntity } from 'src/entities/response.entity';
import { AdminRoleModuleEntity } from 'src/entities/role.module.entity';
import { AdminSubRoleModuleEntity } from 'src/entities/role.sub.module.entity';
import { SalarySlipEntity } from 'src/entities/salarySlip.entity';
import { employmentSector } from 'src/entities/sector.entity';
import { SettlementEntity } from 'src/entities/settlement.entity';
import { shiftPlanEntity } from 'src/entities/shiftPlan.entity';
import { stamp } from 'src/entities/stamp.entity';
import { StateEligibilty } from 'src/entities/stateEligibility.entity';
import { StaticConfigEntity } from 'src/entities/static.config.entity';
import { SubScriptionEntity } from 'src/entities/subscription.entity';
import { SystemTraceEntity } from 'src/entities/system_trace.entity';
import { TallyIPReversal } from 'src/entities/tallyIPReversal.entity';
import { TemplateEntity } from 'src/entities/template.entity';
import { ThirdPartyServiceEntities } from 'src/entities/thirdParty.service.entities';
import { ThirdPartyProvider } from 'src/entities/thirdpartyProviders.entities';
import { TrackErrorMetrics } from 'src/entities/trackErrorsMetrics.entity';
import { TrackStepCategory } from 'src/entities/trackStepCategory.entity';
import { TrackStepMetrics } from 'src/entities/trackStepMetrics.entity';
import { TrackUserAttempts } from 'src/entities/trackUserAttempts.entity';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { TransactionInitializedArchiveEntity } from 'src/entities/transactionInitializedArchive.entity';
import { uniqueContactEntity } from 'src/entities/unique.contact.entity';
import { UniqueContactLogEntity } from 'src/entities/unique.contact.log.entity';
import { UserActivityEntity } from 'src/entities/user.activity.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { UsersFlowDataEntity } from 'src/entities/user.flow.entity';
import { userHistory } from 'src/entities/user.history.entity';
import { UserSelfieEntity } from 'src/entities/user.selfie.entity';
import { UserDelete } from 'src/entities/userDelete.entity';
import { UserLogTracker } from 'src/entities/userLogTracker.entity';
import { UserLoginTrackerEntity } from 'src/entities/userLoginTracker.entity';
import { UserPermissionEntity } from 'src/entities/userPermission.entity';
import { userSmsEntity } from 'src/entities/userSms.entity';
import {
  acceptedLeadModels,
  rejectedLeadModels,
} from 'src/constants/leadTracking';
import { UserRatingsEntity } from 'src/entities/user_ratings_entity';
import { WaiverEntity } from 'src/entities/waiver.entity';
import { whatsappMSGEntity } from 'src/entities/whatsappMSGEntity';
import { WorkMailEntity } from 'src/entities/workMail.entity';
import { YearOutstandingEntity } from 'src/entities/year.outstanding.entity';
import { databaseConfig } from './db.config';
import { TestEntity } from 'src/entities/test.entity';
import { APILogsEntity } from 'src/entities/apilog.entity';
import { AgentUserLatestCallInfo } from 'src/entities/agentUserLatestCallInfo.entity';
import { CibilScoreArchive } from 'src/entities/cibilScoreArchive.entity';
import { CollectionTeamPlanEntity } from 'src/entities/colletionTeamPlan.entity';

function getReplicationConfigs(config, database) {
  if (EnvConfig.database.postgresql.isReplicaOn) {
    const masterHost = EnvConfig.database.postgresql.host;
    const username = EnvConfig.database.postgresql.username;
    const password = EnvConfig.database.postgresql.password;
    const read = [];
    const replicaHostA = EnvConfig.database.postgresql.replicaHostA;
    if (replicaHostA) {
      read.push({ host: replicaHostA, username, password, database });
    }
    const replicaHostB = EnvConfig.database.postgresql.replicaHostB;
    if (replicaHostB) {
      read.push({ host: replicaHostB, username, password, database });
    }
    config.replication = {
      read,
      write: { host: masterHost, username, password, database },
    };
  }
}

export const baseProvider = [
  // Litt database
  {
    provide: 'SEQUELIZE',
    useFactory: async () => {
      const config: any = databaseConfig.user;
      getReplicationConfigs(config, config.database);
      const sequelize = new Sequelize(config);
      const entities: any = [
        LoanDetails,
        admin,
        crmActivity,
        crmTitle,
        ChatDocumentEntity,
        disbursementEntity,
        InstallAppEntity,
        EmiEntity,
        employmentDetails,
        employmentDesignation,
        EmploymentHistoryDetailsEntity,
        employmentSector,
        employmentType,
        esignEntity,
        ExotelCallHistory,
        ExotelCategoryEntity,
        KYCEntity,
        LegalNoticeEntity,
        loanTransaction,
        loanPurpose,
        LocationEntity,
        MasterEntity,
        mandateEntity,
        Configuration,
        PredictionEntity,
        registeredUsers,
        DeviceInfoAndInstallAppEntity,
        InstallAppEntity,
        stamp,
        SubScriptionEntity,
        userHistory,
        userLoanDecline,
        BankingEntity,
        OthersBankStatementEntity,
        GoogleCoordinatesEntity,
        device,
        TransactionEntity,
        ManualVerifiedCompanyEntity,
        ManualVerifiedWorkEmailEntity,
        BlockUserHistoryEntity,
        UserLogTracker,
        SalarySlipEntity,
        WorkMailEntity,
        AddressesEntity,
        Department,
        LegalConsigment,
        UserSelfieEntity,
        YearOutstandingEntity,
        crmStatus,
        ReasonsEntity,
        UserActivityEntity,
        UserDelete,
        CIBILEntity,
        CrmReasonEntity,
        GoogleCompanyResultEntity,
        DefaulterOnlineEntity,
        crmDisposition,
        LegalCollectionEntity,
        AAEntity,
        InsuranceEntity,
        QualityParameterEntity,
        CibilScoreEntity,
        SettlementEntity,
        ipMasterEntity,
        complianceTracker,
        WaiverEntity,
        UserLoginTrackerEntity,
        HashPhoneEntity,
        shiftPlanEntity,
        BatchCibilDataEntity,
        BatchCibilFileTrackingEntity,
        fcmTokenEntity,
        GoogleFileEntity,
        CollectionPerformanceReport,
        AdminEligibilityEntity,
        CollectionTeamPlanEntity,
      ];
      sequelize.addModels(entities);
      await sequelize.sync();
      return sequelize;
    },
  },
  {
    provide: 'SEQUELIZENETBANKINGDB',
    useFactory: async () => {
      const config: any = databaseConfig.netbanking;
      getReplicationConfigs(config, config.database);
      const sequelize = new Sequelize(config);
      const entities = [
        DynamicEntity,
        LeadEntitiy,
        PaymentLinkEntity,
        APILogsEntity,
        FinvuEntity,
        ProteanEntity,
        FetchDataEntity,
        FramesEntity,
        SystemTraceEntity,
        userNetBankingDetails,
        FormUsers,
        PhoneEntity,
        ChangeLogsEntity,
        PeriodicEntity,
        whatsappMSGEntity,
        ExperianScoreEntity,
        EPFODetailsEntity,
        CallKaroEntity,
      ];
      sequelize.addModels(entities);
      await sequelize.sync();
      return sequelize;
    },
  },
  // Contact database
  {
    provide: 'SEQUELIZECONTACTDB',
    useFactory: async () => {
      const config: any = databaseConfig.contact;
      getReplicationConfigs(config, config.database);
      const sequelize = new Sequelize(config);
      const entities = [
        Configuration,
        contactDetailEntity,
        TestEntity,
        uniqueContactEntity,
        missMatchLogs,
        HealthDataEntity,
        OtherPermissionDataEntity,
        UniqueContactLogEntity,
        ContactLogEntity,
      ];
      sequelize.addModels(entities);
      await sequelize.sync();
      return sequelize;
    },
  },
  // IndBank database
  {
    provide: 'SEQUELIZEINDBANKDB',
    useFactory: async () => {
      const config: any = databaseConfig.indbank;
      getReplicationConfigs(config, config.database);
      const sequelize = new Sequelize(config);
      const entities = [
        BankList,
        CacheEntity,
        MetricsEntity,
        UsersFlowDataEntity,
        FlowEntity,
        MailTrackerEntity,
        StaticConfigEntity,
        StateEligibilty,
        TemplateEntity,
        AdminRoleModuleEntity,
        AdminRole,
        AdminSubRoleModuleEntity,
        AccessOfListEntity,
        AccessOfRoleEntity,
        APIAccessListEntity,
        UserPermissionEntity,
        DeviceSIM,
        BlacklistCompanyEntity,
        ReportListEntity,
        DownloaAppTrack,
        banks,
        branches,
        ThirdPartyProvider,
        ThirdPartyServiceEntities,
        CronTrakingEntity,
        cibilTriggerEntity,
        UserRatingsEntity,
        RBIGuidelineEntity,
        LCREntity,
        ActiveLoanAddressesEntity,
        TallyIPReversal,
        HypothecationEntity,
        HypothecationPaymentsEntity,
        HypothecationHistoryEntity,

        // Lead Tracking
        LeadTrackingFileEntity,
        LeadTrackingEntity,
        LeadTrackingIp,
        ...acceptedLeadModels,
        ...rejectedLeadModels,

        // Third party
        ResponseEntity,
        ReportHistoryEntity,
        PromoCodeEntity,
        RequestSupportEntity,
        bannerEntity,
        assetsClassification,
        MsgEntity,
        TrackErrorMetrics,
        TrackStepCategory,
        TrackStepMetrics,
        TrackUserAttempts,
        accountSectionSummary,
        ...smsModels,
        ...notificationModels,
        ...whatsappModels,
      ];
      sequelize.addModels(entities);
      await sequelize.sync();
      return sequelize;
    },
  },
  // ARCHIVED database
  {
    provide: 'SEQUELIZEARCHIVEDDB',
    useFactory: async () => {
      const config: any = databaseConfig.archived;
      getReplicationConfigs(config, config.database);
      const sequelize = new Sequelize(config);
      const entities = [
        MailTrackerArchive,
        TransactionInitializedArchiveEntity,
        CibilScoreArchive,
      ];
      sequelize.addModels(entities);
      await sequelize.sync();
      return sequelize;
    },
  },
  // SMS database
  {
    provide: 'SEQUELIZESMS',
    useFactory: async () => {
      const config: any = databaseConfig.sms;
      getReplicationConfigs(config, config.database);
      const sequelize = new Sequelize(config);
      const entities = [
        userSmsEntity,
        AgentCallHistoryEntity,
        AgentUserLatestCallInfo,
      ];
      sequelize.addModels(entities);
      await sequelize.sync();
      return sequelize;
    },
  },
];

export const DatabaseProvider = baseProvider;
