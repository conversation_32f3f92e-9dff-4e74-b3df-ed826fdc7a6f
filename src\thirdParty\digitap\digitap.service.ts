// Imports
import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { gIsPROD, HOST_URL, Latest_Version } from 'src/constants/globals';
import { APIService } from 'src/utils/api.service';
import { EnvConfig } from 'src/configs/env.config';
import { regAadhaar } from 'src/constants/validation';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
  kParamsMissing,
} from 'src/constants/responses';
import { MetricsSharedService } from 'src/shared/metrics.shared.service';
import { FetchDataEntity } from 'src/entities/fetchData.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';

@Injectable()
export class DigiTapService {
  constructor(
    private readonly api: APIService,
    private readonly sharedMetrics: MetricsSharedService,
    private readonly repoManager: RepositoryManager,
  ) {}

  async generateUrl(body) {
    const redirectionUrl = body?.redirectionUrl;
    const uniqueId = body?.uniqueId;
    const userId = body?.userId;
    if (!redirectionUrl || !uniqueId || !userId) return kParamsMissing;
    const header = {
      Authorization: gIsPROD
        ? 'Basic NDA2NTMzNzA6dGtiM1hJQ3ZtTmFveHFVSU9GN2cyU04zaFJUTDdSaTI='
        : 'Basic NjU2Mjk2NzQ6MFZhY2VOaTFIZ1JMdFFUUzZVVEtqZm5rUDRJdkJVYXE=',
    };
    // Preparation -> API
    const url = gIsPROD
      ? 'https://svc.digitap.ai/kyc-unified/v1/generate-url/'
      : 'https://svcdemo.digitap.work/kyc-unified/v1/generate-url/';
    const reqBody = { redirectionUrl, uniqueId };
    // Hit -> API
    const response: any = await this.api.requestPost(url, reqBody, header);
    if (response === k500Error) return kInternalError;
    else if (response?.model) response.model.userId = userId;
    return response;
  }

  async pullAadhaar(reqData) {
    // Validation -> parameter
    const aadhaarNumber = reqData?.aadhaarNumber;
    if (!aadhaarNumber) return kParamMissing('aadhaarNumber');
    const transactionId = reqData?.transactionId;
    if (!transactionId) return kParamMissing('transactionId');
    const userId = reqData?.userId;
    if (!userId) return kParamMissing('userId');
    const fetchId = reqData?.fetchId;

    const url = gIsPROD
      ? `https://svc.digitap.ai/kyc-unified/v1/${transactionId}/details/`
      : `https://svcdemo.digitap.work/kyc-unified/v1/${transactionId}/details/`;
    const header = {
      Authorization: gIsPROD
        ? 'Basic NDA2NTMzNzA6dGtiM1hJQ3ZtTmFveHFVSU9GN2cyU04zaFJUTDdSaTI='
        : 'Basic NjU2Mjk2NzQ6MFZhY2VOaTFIZ1JMdFFUUzZVVEtqZm5rUDRJdkJVYXE=',
    };
    // Hit -> api -> Generate auth token
    const response = await this.api.requestGet(url, header);
    if (response == k500Error) return kInternalError;
    if (fetchId) {
      await this.repoManager.updateRowData(
        FetchDataEntity,
        { response: JSON.stringify(response) },
        fetchId,
      );
    }
    const model = response?.model;
    if (!model)
      return k422ErrorMessage('Aadhaar details not found from digitap');
    // Validate aadhaar number with digitap details
    const uidData = model?.maskedAdharNumber;
    if (uidData) {
      const maskedUid = uidData.replace(/x/g, '').trim();
      if (!aadhaarNumber.includes(maskedUid)) {
        return k422ErrorMessage(
          'Aadhaar number does not match as per digitap aadhaar details',
        );
      }
    }

    // Prepare dob
    const dobSpans = model?.dob.split('-');
    const dob = dobSpans.reverse().join('-');
    const address = model?.address;

    //rawJsonList.attributes.uid
    const aadhaarResponse = {
      status: 'Success',
      responseData: {
        digitapReresponse: { authResponse: response },
        state: '',
        valid: true,
        eid: '',
        informationSharingConsent: true,
        localResName: address?.house ?? '',
        localCareof: '',
        localBuilding: '',
        email: '',
        dob,
        mobile: '',
        gender: model?.gender == 'M' ? 'MALE' : 'FEMALE',
        landmark: address?.landmark ?? '',
        street: address?.street ?? '',
        locality: address?.loc ?? '',
        district: '',
        vtc: address?.vtc ?? '',
        building: address.house ?? '',
        districtName: address?.dist ?? '',
        vtcName: address?.vtc ?? '',
        stateName: address?.state ?? '',
        poName: address?.po ?? '',
        careof: model?.careOf ?? '',
        poNameLocal: '',
        localVtc: '',
        localState: '',
        localDistrict: '',
        pincode: address?.pc ?? '',
        uid: aadhaarNumber,
        localStreet: address?.street ?? '',
        localLocality: '',
        localLandmark: address?.landmark ?? '',
        refId: null,
        langCode: '',
        relationInfo: null,
        biometricFlag: false,
        dobStatus: '',
        enrolmentDate: '',
        enrolmentNumber: '',
        enrolmentType: '',
        exceptionPhoto: null,
        isCurrent: false,
        isNRI: 'false',
        isdCode: '+91',
        poType: null,
        poa: null,
        poi: null,
        residentPhoto: model?.image,
        subDistrict: '',
        subDistrictLocalName: '',
        subDistrictName: address?.subdist ?? '',
        updatedEIds: [],
        updatedEIdsCount: 0,
        updatedRefIds: [],
        updatedRefIdsCount: 0,
        name: model?.name,
        aadhaar_service: 'DIGITAP',
      },
    };
    if (reqData?.typeOfDevice == 2) {
      const url = HOST_URL + `${Latest_Version}/kyc/validatemAadhaar`;
      const reqBody = {
        aadhaarNumber,
        userId,
        aadhaarResponse,
      };
      return await this.api.post(url, reqBody);
    }

    const callbackList = [
      {
        url: HOST_URL + `${Latest_Version}/kyc/validatemAadhaar`,
        method: 'POST',
        body: {
          aadhaarNumber,
          userId,
          aadhaarResponse,
        },
      },
    ];
    return { callbackList };
  }

  async invitation(reqData) {
    // Validation -> parameter
    const fetchId = reqData?.entry?.id;
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    const aadhaarNumber = reqData.aadhaarNumber;
    if (!aadhaarNumber) return kParamMissing('aadhaarNumber');
    const transactionId = reqData?.transactionId;
    if (!transactionId) return kParamMissing('transactionId');
    const aadhaarService = reqData?.aadhaar_service ?? 'DIGITAP';
    if (!regAadhaar(aadhaarNumber))
      return k422ErrorMessage('Please enter valid aadhaar number');

    // Metrics log
    await this.sharedMetrics.insertLog({
      type: 2,
      status: 2,
      subType: 1,
      userId,
      values: { activity: 'INVITATION' },
    });
    const newIntitalUrl = reqData?.intitalUrl;
    if (aadhaarService == 'DIGITAP')
      return {
        needBottomSheet: false,
        webviewData: {
          title: 'Verify your kyc',
          initialLoader: false,
          initialURL: newIntitalUrl,
          type: 'DIGITAP_AUTH',
          jsTriggers: {
            [EnvConfig.digiTap.redirectUrl]: {
              allowContains: true,
              onUpdateVisitedHistory: { state: { isProcessing: true } },
              onLoadStop: {
                triggers: [
                  'console.log("METRICS->8" + new Date().getTime().toString());',
                  "console.log('REDIRECT_URL_COMPLETED' + window.location.href)",
                ],
              },
              consoles: [
                {
                  apiTriggers: [
                    {
                      url: HOST_URL + 'thirdParty/digitap/pullAadhaar',
                      method: 'POST',
                      body: { aadhaarNumber, userId, transactionId, fetchId },
                    },
                  ],
                  combinations: ['REDIRECT_URL_', 'COMPLETED'],
                },
              ],
            },
          },
        },
      };
  }
}
