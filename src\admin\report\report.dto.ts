import {
  IsBoolean,
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class DisbursementMasterReportDTO {
  @IsNotEmpty()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  startDate: Date;

  @IsNotEmpty()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  endDate: Date;

  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') return value.toLowerCase() === 'true';
    return false;
  })
  @IsBoolean()
  download: boolean;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => +value)
  page: number;

  @IsOptional()
  @IsNumber()
  downloadId: number;

  @IsOptional()
  @IsNumber()
  reportType: number;
  @IsOptional()
  adminId: any;
}
