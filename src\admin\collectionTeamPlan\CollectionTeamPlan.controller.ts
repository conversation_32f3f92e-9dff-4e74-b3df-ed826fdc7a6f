import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { CollectionTeamPlanService } from './CollectionTeamPlan.service';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('admin/CollectionTeamPlan')
export class CollectionTeamPlanController {
  constructor(
    private readonly collectionTeamPlanService: CollectionTeamPlanService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Post('createTeam')
  async funCreateShiftPlan(@Body() body, @Res() res) {
    try {
      const data: any = await this.collectionTeamPlanService.createTeam(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('updateTeam')
  async funUpdateShiftPlan(@Body() body, @Res() res) {
    try {
      const data: any = await this.collectionTeamPlanService.updateTeam(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getAllTeam')
  async funGetAllTeam(@Res() res) {
    try {
      const data: any = await this.collectionTeamPlanService.getAllTeam();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('syncTeamData')
  async funSyncTeamData(@Body() body, @Res() res) {
    try {
      const data: any = await this.collectionTeamPlanService.syncTeamData(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getCollectionAdminWithTeam')
  async funGetCollectionMembers(@Res() res) {
    try {
      const data: any =
        await this.collectionTeamPlanService.getCollectionAdminWithTeam();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
}
