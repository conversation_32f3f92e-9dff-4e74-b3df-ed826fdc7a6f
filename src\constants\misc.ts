//Process
export const k500Error = '500';
export const k302Error = '302';
export const k400Error = '400';
export const k422Error = '422';
export const k404Error = '404';
export const k409Error = '409';
export const k403Error = '403';
export const kError = '500Error';

export const k204Response = 204;

//this code is use only cibil score
export const k999Error = '-999Error';

//Doc Type
export const kElectricityBill = 'Electricity Bill';
export const kPGAgreement = 'PG Agreement';
export const kRentAgreement = 'Rent Agreement';
export const kOtherDoc = 'Other Document';
export const Kadhar = 'ADHAR CARD';
export const KDrivingLicense = 'Driving license';

export const kAdminRejection = 'Rejected By Admin';

export const RazorpayPaymentStatus = {
  1: 'issued',
  2: 'paid',
};

export const PartialPaymentStatus = {
  1: 'INITIATED',
  2: 'PROCESSING',
  3: 'COMPLETED',
  4: 'FAILURE',
};

export const UpiPaymentStatus = {
  1: 'INITIATED',
  2: 'PROCESSING',
  3: 'SUCCESS',
  4: 'FAILURE',
};

export const KLoanStatus = {
  1: 'InProcess',
  2: 'Accepted',
  3: 'Active',
  4: 'Rejected',
  5: 'Complete',
};

export const topBanks = [
  'SBI',
  'ICICI',
  'HDFC',
  'KOTAK',
  'BANK_OF_BARODA',
  'CANARA',
];
