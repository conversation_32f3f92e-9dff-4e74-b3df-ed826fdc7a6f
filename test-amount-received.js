/**
 * Test script for Amount Received API
 * This script demonstrates how to use the getAmountReceived endpoint
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Adjust if your server runs on different port
const API_ENDPOINT = '/admin/transaction/getAmountReceived';

// Test scenarios
const testScenarios = [
  {
    name: 'Current Month Test',
    startDate: new Date().toISOString().slice(0, 7) + '-01', // First day of current month
    endDate: new Date().toISOString().slice(0, 7) + '-31'    // Last day of current month
  },
  {
    name: 'Previous Month Test',
    startDate: '2024-01-01',
    endDate: '2024-01-31'
  },
  {
    name: 'Future Month Test',
    startDate: '2024-12-01',
    endDate: '2024-12-31'
  }
];

async function testAmountReceived() {
  console.log('🚀 Testing Amount Received API...\n');

  for (const scenario of testScenarios) {
    console.log(`📊 ${scenario.name}`);
    console.log(`   Date Range: ${scenario.startDate} to ${scenario.endDate}`);
    
    try {
      const response = await axios.get(`${BASE_URL}${API_ENDPOINT}`, {
        params: {
          startDate: scenario.startDate,
          endDate: scenario.endDate
        },
        timeout: 10000
      });

      if (response.data.statusCode === 200) {
        console.log('   ✅ Success!');
        console.log('   📈 Results:');
        
        response.data.data.forEach((bucket, index) => {
          const lockStatus = bucket.locked ? '🔒 Locked' : '🔓 Unlocked';
          console.log(`      ${bucket.label}: ${bucket.amount} ${lockStatus}`);
        });
      } else {
        console.log('   ❌ API Error:', response.data.message);
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('   ⚠️  Server not running. Please start the server first.');
      } else {
        console.log('   ❌ Request failed:', error.message);
      }
    }
    
    console.log(''); // Empty line for readability
  }
}

// Expected behavior documentation
function printExpectedBehavior() {
  console.log('📋 Expected Behavior:');
  console.log('');
  console.log('🟢 Current Month Scenarios:');
  console.log('   • Days 1-5: Shows actual received amount from Day 1 to today, others ₹0 Cr');
  console.log('   • Day 6: Lock first range (01-05), start second range (06-10)');
  console.log('   • Day 8: First range locked, second range shows 6th-8th cumulative');
  console.log('   • Day 11: Previous buckets freeze, new bucket (11-15) begins');
  console.log('   • Day 27: Only last range (26-31) active, others locked');
  console.log('   • End of month: All ranges locked with final actuals');
  console.log('');
  console.log('🟡 Previous/Future Months:');
  console.log('   • All buckets show final amounts (locked for past, ₹0 for future)');
  console.log('');
  console.log('💰 Currency Format: ₹ X.XX Cr (Crores with 2 decimal places)');
  console.log('');
}

// Integration example
function printIntegrationExample() {
  console.log('🔧 Frontend Integration Example:');
  console.log('');
  console.log(`
// JavaScript/TypeScript example
async function getPredictedAmountTable(startDate, endDate) {
  try {
    const response = await fetch(
      \`/admin/transaction/getAmountReceived?startDate=\${startDate}&endDate=\${endDate}\`
    );
    const data = await response.json();
    
    if (data.statusCode === 200) {
      // Map to your table structure
      const tableData = data.data.map((item, index) => ({
        days: item.label,
        forecastAmount: getForecastAmount(index), // Your existing logic
        cumulativeAmount: getCumulativeAmount(index), // Your existing logic
        amountReceived: item.amount, // New column!
        isLocked: item.locked
      }));
      
      return tableData;
    }
  } catch (error) {
    console.error('Error fetching amount received:', error);
  }
}
`);
  console.log('');
}

// Main execution
async function main() {
  printExpectedBehavior();
  printIntegrationExample();
  await testAmountReceived();
  
  console.log('✨ Test completed!');
  console.log('');
  console.log('📝 Next Steps:');
  console.log('   1. Integrate this API into your predicted amount table');
  console.log('   2. Add "Amount Received" as a new column');
  console.log('   3. Use the locked status to style completed buckets');
  console.log('   4. Test with real data in your environment');
}

// Run the tests
main().catch(console.error);
