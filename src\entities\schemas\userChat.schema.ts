// Imports
import { <PERSON><PERSON>, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserChatDocument = UserChat & Document;

@Schema({ timestamps: true, strict: false })
export class UserChat {
  @Prop({ type: String, required: true, unique: true })
  userId: string; // uuid

  @Prop({ type: Date, default: null })
  recentChatDate: Date | null;

  @Prop({ type: Object, default: null })
  recentChatData: Record<string, any> | null;

  @Prop({ type: Map, of: Object, default: {} })
  allChatData: Map<number, any> | null;
}

export const UserChatSchema = SchemaFactory.createForClass(UserChat);
