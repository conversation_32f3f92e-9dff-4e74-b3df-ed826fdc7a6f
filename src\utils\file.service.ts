// Imports
import axios from 'axios';
import * as excel from 'excel4node';
import * as fs from 'fs';
import * as env from 'dotenv';
import * as handlebars from 'handlebars';
import base64topdf = require('base64topdf');
import { PDFDocument } from 'pdf-lib';
import { Injectable } from '@nestjs/common';
import { k204Response, k500Error } from 'src/constants/misc';
import { Storage } from '@google-cloud/storage';
import { kMediaImages } from 'src/constants/directories';
import { APIService } from './api.service';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
} from 'src/constants/responses';
import { AuthAiService } from 'src/thirdParty/authAi/authAi.service';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const ILovePDFApi = require('@ilovepdf/ilovepdf-nodejs');
import {
  gIsPROD,
  GlobalServices,
  puppeteerConfig,
  PUPPETER_SERVICE,
} from 'src/constants/globals';
import { TypeService } from './type.service';
import sharp = require('sharp');
import {
  CLOUD_FOLDER_PATH,
  kBankingProHeaders,
  kBigFonts,
  kMimeTypes,
  kSmallFonts,
} from 'src/constants/objects';
import { getOrientation } from 'get-orientation';
import { DateService } from './date.service';
import puppeteer from 'puppeteer';
import { BrowserData } from 'src/main';
import { kCloudService, kGoogle, kRuppe } from 'src/constants/strings';
import { EnvConfig } from 'src/configs/env.config';
import path = require('path');
import { CRYPT_PATH } from 'src/constants/paths';
import { ObjectStorageClient, requests } from 'oci-objectstorage';
import { Region, SimpleAuthenticationDetailsProvider } from 'oci-common';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { ErrorContextService } from './error.context.service';
import * as xml2js from 'xml2js';
import {
  kRemovePasswordFile,
  PRIMARY_BANKINGPRO_URL,
} from 'src/constants/network';

env.config();

@Injectable()
export class FileService {
  googleStorage;
  objectStorageClient;
  constructor(
    private readonly api: APIService,
    private readonly authAi: AuthAiService,
    private readonly typeService: TypeService,
    private readonly dateService: DateService,
    private readonly commonSharedService: CommonSharedService,
    private readonly errorContextService: ErrorContextService,
  ) {
    this.googleStorage = new Storage({
      keyFilename: EnvConfig.gCloudCred.appCredentials,
    });

    ///oracle cloud configuration
    const keyFilePath = CRYPT_PATH.oraclePrivateKey;
    const privateKey = fs
      .readFileSync(keyFilePath, 'utf8')
      .replace(/\r\n/g, '\n')
      .trim();
    const provider = new SimpleAuthenticationDetailsProvider(
      EnvConfig.oracle.tenancy,
      EnvConfig.oracle.user,
      EnvConfig.oracle.fingerprint,
      privateKey,
      null,
      Region.AP_MUMBAI_1,
    );

    this.objectStorageClient = new ObjectStorageClient({
      authenticationDetailsProvider: provider,
    });
  }

  //#region
  async uploadFile(
    filePath: string,
    folderName = CLOUD_FOLDER_PATH.default,
    extension?: string,
    fileName?: string,
    bucketName?: string,
  ): Promise<any> {
    const service = await this.commonSharedService.getServiceName(
      kCloudService,
    );
    if (kGoogle == service)
      return this.uploadGoogleCloudFile(
        filePath,
        folderName,
        extension,
        fileName,
      );
    else
      return this.uploadOracleCloudFile(
        filePath,
        folderName,
        extension,
        bucketName,
      );
  }
  //#endregion

  //#region
  private async uploadGoogleCloudFile(
    filePath: string,
    folderName?: string,
    extension?: string,
    fileName?: string,
  ): Promise<any> {
    try {
      const bufferData = await this.getBuffer(filePath);
      const url = await new Promise(async (resolve) => {
        const bucket = await this.googleStorage.bucket(
          EnvConfig.gCloudCred.bucketName,
        );
        let nameData: string = Date.now().toString();
        nameData = nameData.concat('.');
        nameData = nameData.concat(extension ?? '');

        let file = bucket.file(
          (folderName ?? kMediaImages) + '/' + (fileName ?? nameData),
        );

        if (fileName) {
          fileName = fileName.concat('-' + Date.now().toString());
          fileName = fileName.concat('.' + extension);
          file = bucket.file(fileName ?? nameData);
        }
        file.save(bufferData, { validation: 'md5' }, function (error) {
          if (error) resolve({});
          else resolve({ url: file.publicUrl() });
        });
      });
      await this.removeFile(filePath);
      if (!url) return k500Error;
      return url['url'];
    } catch (error) {
      await this.removeFile(filePath);
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }
  //#endregion

  //#region
  private async uploadOracleCloudFile(
    filePath: string,
    folderName?: string,
    extension?: string,
    bucketName?: string,
  ): Promise<any> {
    try {
      const file = filePath.replace('./', '');
      const content = await fs.promises.readFile(filePath);
      const month = new Date().toLocaleString('default', { month: 'short' });
      const year = new Date().getFullYear();
      const objectName =
        (folderName ?? 'DEFAULT') + '/' + `${month}-${year}` + '/' + file;

      let fileType = extension ?? null;
      if (!extension) {
        const match = file.match(/\.([a-zA-Z0-9]+)(?:\?.*)?$/);
        fileType = match ? match[1] : '';
      }

      const putObjectRequest: requests.PutObjectRequest = {
        namespaceName: EnvConfig.oracle.namespace,
        bucketName: bucketName ?? EnvConfig.oracle.bucket,
        putObjectBody: content,
        objectName,
        contentType: kMimeTypes[fileType],
        opcMeta: {
          'Content-Disposition': 'inline',
        },
      };

      ///upload file
      await this.objectStorageClient.putObject(putObjectRequest);

      // remove file
      try {
        await this.removeFile(filePath);
      } catch (error) {}

      return await this.getPARUrlDetails(objectName);
    } catch (error) {
      // remove file
      try {
        await this.removeFile(filePath);
      } catch (error) {}
      return k500Error;
    }
  }
  //#endregion

  //#region get PAR URL
  private async getPARUrlDetails(filePath?: string) {
    try {
      const expirationDate = new Date();
      expirationDate.setFullYear(expirationDate.getFullYear() + 100);

      const createPARRequest: requests.CreatePreauthenticatedRequestRequest = {
        namespaceName: EnvConfig.oracle.namespace,
        bucketName: EnvConfig.oracle.bucket,
        createPreauthenticatedRequestDetails: {
          name: `par_for_${filePath}`,
          objectName: filePath,
          accessType: 'ObjectReadWrite' as any,
          timeExpires: expirationDate,
        },
      };
      const parResponse =
        await this.objectStorageClient.createPreauthenticatedRequest(
          createPARRequest,
        );
      return `https://objectstorage.ap-mumbai-1.oraclecloud.com${parResponse.preauthenticatedRequest.accessUri}`;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
    }
  }
  //#endregion

  //#region
  async deleteCloudFile(fileName?: string): Promise<any> {
    if (!fileName) return k204Response;
    if (fileName?.includes('storage.googleapis.com'))
      return this.deleteGoogleFile(fileName);
    else if (
      fileName?.includes(EnvConfig.oracle.bucket) ||
      fileName?.includes('oraclecloud.com')
    )
      return this.deleteOracleFile(fileName);
    else return k204Response;
  }
  //#endregion

  //#region
  private async deleteGoogleFile(fileName?: string): Promise<any> {
    try {
      if (!gIsPROD) return k204Response;
      const storage = new Storage({
        projectId: EnvConfig.gCloudCred.projectName,
      });
      const bucket = await storage.bucket(EnvConfig.gCloudCred.bucketName);
      const filePath = fileName
        .split(EnvConfig.gCloudCred.bucketName)[1]
        .substring(1);

      let formattedPath = decodeURIComponent(filePath);
      let data = await bucket.file(formattedPath).delete();
      // if (data[0]?.statusCode != k204Response) {
      // }
      return data[0].statusCode;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }
  //#endregion

  //#region
  private async deleteOracleFile(fileName?: string): Promise<any> {
    try {
      if (!gIsPROD) return k204Response;
      const objectName = this.extractObjectName(fileName);
      if (!objectName) return k500Error;

      // Construct the DeleteObjectRequest
      const deleteObjectRequest = {
        namespaceName: EnvConfig.oracle.namespace,
        bucketName: EnvConfig.oracle.bucket,
        objectName: objectName,
      };

      // Execute the delete request
      const response = await this.objectStorageClient.deleteObject(
        deleteObjectRequest,
      );

      if (response?.lastModified || response?.opcRequestId) return k204Response;
      return k500Error;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }
  //#endregion

  //#region
  private extractObjectName(url: string): string | null {
    const objectPathMatch = url.match(/\/o\/(.*)$/);
    return objectPathMatch ? objectPathMatch[1] : null;
  }
  //#endregion

  async compressIfNeed(targetPath: string, minKb: number) {
    try {
      const actualKb = (await fs.promises.stat(targetPath)).size / 1000;
      if (actualKb <= minKb) return targetPath;
      const instance = new ILovePDFApi(
        'project_public_4b04b0aedc9179491827125b6d52aac0_aeq34f6c25856b7a293d198644e57d06dfd3c',
        'secret_key_0701cd4578774b49624bd352f1ba2305_5Iazpaca5313f75f2a53e410ebe2b830badeb',
      );

      const task = instance.newTask('compress');
      const fileURL = await this.uploadFile(
        targetPath,
        CLOUD_FOLDER_PATH.compressedFiles,
      );
      const bufferData = await task
        .start()
        .then(() => {
          return task.addFile(fileURL);
        })
        .then(() => {
          return task.process();
        })
        .then(() => {
          return task.download();
        });

      const filePath = './' + new Date().getTime().toString() + '.pdf';
      await fs.writeFileSync(filePath, bufferData);
      await this.removeFile(targetPath);
      return filePath;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async urlToBuffer(
    url: string,
    isNeedPath = false,
    extension = null,
    extraOptions = {},
  ) {
    try {
      const options = { responseType: 'arraybuffer', ...extraOptions };
      const data = await this.api.get(url, {}, {}, { ...options });
      if (!data || data == k500Error) return data;
      let ext = 'jpg';
      if (extension) ext = extension;
      const filePath = './upload/' + new Date().getTime() + '.' + ext;
      const bufferData = Buffer.from(data, 'binary');
      await fs.writeFileSync(filePath, bufferData, 'base64');
      const base64Data = fs.readFileSync(filePath, 'base64');
      if (isNeedPath) return filePath;
      else {
        await this.removeFile(filePath);
        return base64Data;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async base64ToFile(base64Content, extension = 'pdf') {
    try {
      const filePath = './' + new Date().getTime().toString() + '.' + extension;
      await fs.writeFileSync(filePath, base64Content, 'base64');
      return filePath;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async base64ToURL(
    base64Content,
    extension = 'pdf',
    folder = CLOUD_FOLDER_PATH.default,
    fileName?,
  ) {
    try {
      let filePath = './';
      if (fileName) filePath = filePath + fileName + '-';
      filePath += new Date().getTime().toString() + '.' + extension;

      if (extension == 'png' || extension == 'jpeg' || extension != 'pdf')
        fs.writeFileSync(filePath, base64Content, 'base64');
      else await base64topdf.base64Decode(base64Content, filePath);

      const urlResponse = await this.uploadFile(
        filePath,
        folder,
        extension,
        fileName,
      );
      if (!urlResponse || urlResponse == k500Error)
        return k422ErrorMessage('ESIGN:URL -> BUFFER:UPLOAD ERROR');
      return urlResponse;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k422ErrorMessage('ESIGN:INTERNAL SERVER ERROR6');
    }
  }

  async fileUrlToBase64(fileUrl: string): Promise<string> {
    const response = await axios.get(fileUrl, {
      responseType: 'arraybuffer',
    });
    const fileContent = response.data;
    const base64 = Buffer.from(fileContent).toString('base64');
    return base64;
  }

  async fileUrlToFile(fileUrl: string): Promise<string> {
    const base64 = await this.fileUrlToBase64(fileUrl);
    const splittedSpans = fileUrl.split('.');
    const extension = splittedSpans[splittedSpans.length - 1];
    return await this.base64ToFile(base64, extension);
  }

  async binaryToFileURL(
    binaryStr,
    extension = 'pdf',
    folderName = CLOUD_FOLDER_PATH.default,
    originalname?,
    uploadFile = true,
    checkKey?,
    extraParams?,
  ) {
    let filePath = './' + new Date().getTime().toString() + '.' + extension;
    if (originalname) {
      originalname = originalname.replace(/[^a-zA-Z0-9 ]/g, '');
      filePath =
        './' + originalname + new Date().getTime().toString() + '.' + extension;
    }
    const binaryData = Buffer.from(binaryStr, 'binary');

    await fs.writeFileSync(filePath, binaryData);
    if (!uploadFile) return filePath;

    let urlResponse;
    if (checkKey == 'checkPassword') {
      const formData = {
        pdfFile: await fs.createReadStream(filePath),
        password: extraParams?.password ? extraParams?.password : '',
      };

      const url = PRIMARY_BANKINGPRO_URL + kRemovePasswordFile;
      const headers = kBankingProHeaders;

      const result = await this.api.requestPost(url, {}, headers, formData);
      if (!result?.valid) return result;
      console.log({ extension }, { folderName });

      urlResponse = await this.base64ToFileURL(
        result?.data,
        extension,
        folderName,
      );
      console.log('Decrypted PDF uploaded:', urlResponse);
    } else {
      urlResponse = await this.uploadFile(
        filePath,
        folderName,
        extension,
        originalname,
      );
    }

    if (!urlResponse || urlResponse == k500Error) throw new Error();

    return urlResponse;
  }

  async base64ToFileURL(
    base64Content,
    extension = 'pdf',
    folderName = CLOUD_FOLDER_PATH.default,
  ) {
    const filePath = './' + new Date().getTime().toString() + '.' + extension;
    if (extension == 'png' || extension == 'jpeg' || extension != 'pdf') {
      fs.writeFileSync(filePath, base64Content, 'base64');
    } else await base64topdf.base64Decode(base64Content, filePath);
    const urlResponse = await this.uploadFile(filePath, folderName, extension);
    if (!urlResponse || urlResponse == k500Error) return kInternalError;
    return urlResponse;
  }

  async streamToURL(response, password?: string, type?: string, loanId = null) {
    let path = new Date().getTime() + '.pdf';
    if (loanId) path = loanId + '_' + path;
    if (type != 'stream') await fs.writeFileSync(path, Buffer.from(response));
    else await this.createTheFile(response, path);

    if (!password) {
      const fileUrl = await this.uploadFile(path, CLOUD_FOLDER_PATH.finvu);
      return fileUrl;
    }
    return await this.authAi.removePassword(path, password);
  }

  async storeJsObjectToCloud(
    object: any,
    itemId: number,
    extension = 'json',
    cloudPath = null,
  ) {
    try {
      const fileName = `${
        itemId ?? new Date().getTime()
      }-trans-${new Date().getTime()}.${extension}`;
      const filePath = './' + fileName;
      await fs.writeFileSync(filePath, JSON.stringify(object, null, 2));

      const urlResponse = await this.uploadFile(
        filePath,
        cloudPath,
        extension,
        fileName,
      );
      if (!urlResponse || urlResponse == k500Error) throw new Error();
      return urlResponse;
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async dataToPDF(content: any, otherParams = {}) {
    try {
      let filePath = '';
      if (PUPPETER_SERVICE == true) {
        const url = `${EnvConfig.server.puppetterBaseUrl}convertToBase64WithPuppeter`;
        const response = await this.api.post(url, { content, otherParams });
        if (!response || !response.data || response.data == k500Error)
          throw new Error();
        filePath = await this.base64ToFile(response?.data);
      } else {
        const activePages = ((await BrowserData.browserInstance.pages()) ?? [])
          .length;
        if (activePages == 0)
          BrowserData.browserInstance = await puppeteer.launch(puppeteerConfig);
        filePath = './upload/' + new Date().getTime() + '.pdf';
        const page = await BrowserData.browserInstance.newPage();
        await page.setDefaultNavigationTimeout(0);
        await page.setContent(content);
        await page.pdf({
          format: 'A4',
          path: filePath,
          timeout: 0,
          printBackground: true,
          omitBackground: true,
          ...otherParams,
        });
        const totalPages = ((await BrowserData.browserInstance.pages()) ?? [])
          .length;
        if (totalPages >= 3) await page.close();
      }
      if (!filePath || filePath.length < 5) throw new Error();

      return filePath;
      //   create= await new Promise((resolve, reject) => {
      //     pdf.create(content, options).toFile(filePath, function (error, data) {
      //         if (error) reject(k500Error);
      //         else if (data.filename) resolve(filePath);
      //         reject(k500Error);
      //       });
      //     });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async mergeMultiplePDF(nextPages: string[], currentPage) {
    try {
      const cover = await PDFDocument.load(fs.readFileSync(currentPage));
      const doc = await PDFDocument.create();
      const coverPages = await doc.copyPages(cover, cover.getPageIndices());
      for (const page of coverPages) doc.addPage(page);
      for (let index = 0; index < nextPages.length; index++) {
        const imageURL =
          kGoogle == GlobalServices.CLOUD_SERVICE
            ? 'https://storage.googleapis.co'
            : 'https://objectstorage.ap-mumbai-1.oraclecloud.com';
        let targetPage = nextPages[index];

        if (index < nextPages.length - 1) {
          const nextPage = nextPages[index + 1];
          const nextPageHasImage = nextPage.startsWith(imageURL);
          if (nextPageHasImage) continue;
        }

        let isNetWorkImage = false;
        isNetWorkImage = targetPage.startsWith(imageURL);
        if (isNetWorkImage == true && index != 0)
          targetPage = nextPages[index - 1];
        let content = await PDFDocument.load(fs.readFileSync(targetPage));

        //Image insertion
        let imagePath;
        if (isNetWorkImage == true) {
          const bufferData = await this.urlToBuffer(nextPages[index]);

          const img = await content.embedJpg(bufferData);
          const imagePage = content.insertPage(1);
          imagePage.drawImage(img, {
            x: 0,
            y: 0,
            width: imagePage.getWidth(),
            height: imagePage.getHeight(),
          });
          imagePath = './upload/' + new Date().getTime() + '.pdf';
          await fs.writeFileSync(imagePath, await content.save());
          content = await PDFDocument.load(fs.readFileSync(imagePath));
        }
        //PDF merge
        const pageIndices = content.getPageIndices();
        const contentPages = await doc.copyPages(content, pageIndices);
        for (const page of contentPages) doc.addPage(page);

        if (imagePath) await this.removeFile(imagePath);
      }
      const pdfPath1 = './upload/' + Date.now() + '.pdf';
      fs.writeFileSync(pdfPath1, await doc.save());
      return pdfPath1;
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  private async getBuffer(filePath: string) {
    try {
      if (filePath) return fs.readFileSync(filePath);
      return '500';
    } catch (error) {
      return '500';
    }
  }

  async removeFile(filePath: string) {
    try {
      if (filePath) await fs.unlinkSync(filePath);
    } catch (error) {
      console.log({ error });
      return '500';
    }
  }

  async removeFiles(filePaths: string[]) {
    try {
      for (let index = 0; index < filePaths.length; index++) {
        try {
          const filePath = filePaths[index];
          if (filePath && fs.existsSync(filePath)) fs.unlinkSync(filePath);
        } catch (error) {
          return k500Error;
        }
      }
    } catch (error) {
      return k500Error;
    }
  }

  private async createTheFile(response, path) {
    return await new Promise((resolve) => {
      // @ts-ignore
      response.pipe(fs.createWriteStream(path).on('finish', resolve));
    });
  }

  async hbsHandlebars(templateName, data, source = null) {
    try {
      if (!source) source = fs.readFileSync(templateName, 'utf8');
      handlebars.registerHelper(
        'numberWithComma',
        this.typeService.amountNumberWithCommas,
      );
      handlebars.registerHelper('cIf', this.cIfHalper);
      handlebars.registerHelper('eq', (a, b) => a == b);
      handlebars.registerHelper('length', function (arr) {
        return arr.length;
      });
      handlebars.registerHelper('gt', function (value1, value2) {
        return value1 > value2;
      });
      const template = handlebars.compile(source);
      const output = template(data);
      return output;
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  // hbsHandlebars for If else
  private cIfHalper(...cnd) {
    try {
      const option = cnd[cnd.length - 1];
      const operator = cnd[cnd.length - 2];
      const value = cnd[cnd.length - 3];
      let condition;
      if (operator === 'eq') {
        if (value === '-' || value === ' ') condition = value;
      } else {
        condition = eval(cnd.slice(0, cnd.length - 1).join(' '));
      }

      if (condition) return option.fn(this);
      else return option.inverse(this);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async sharpCompression(
    filePath,
    fileName = '',
    getType = '',
    isNeedLocal = false,
    options?,
  ) {
    try {
      if (!fileName) fileName = new Date().getTime().toString();
      if (!getType) getType = filePath.split('.').reverse()[0];
      if (filePath.includes('https')) {
        filePath = await this.urlToBuffer(filePath, true, getType);
        if (filePath == k500Error) return kInternalError;
      }
      const width = options?.width;
      const height = options?.height;
      const quality = options?.quality ?? 10;
      const newPath = './upload/' + fileName + '.' + getType;
      const url = await new Promise((resolve, reject) => {
        let compress = sharp(filePath, { failOnError: false }).resize(
          width,
          height,
        );
        if (getType == 'jpg') compress = compress.jpeg({ quality });
        else if (getType == 'png') compress = compress.png({ quality });
        compress.toFile(newPath, function (err) {
          if (err) reject(err);
          else resolve(newPath);
        });
      });
      this.removeFile(filePath);
      if (!isNeedLocal) {
        const networkURL = await this.uploadFile(
          newPath,
          CLOUD_FOLDER_PATH.compressedFiles,
          getType,
        );
        if (networkURL == k500Error) return kInternalError;
        return networkURL;
      } else return newPath;
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // check orientation and get image rotate css
  async getOrientationCSS(image) {
    let style = '';
    try {
      let orientation = 1;
      try {
        const buffer: any = await this.getBase64FromImgUrl(image, true);
        if (!buffer?.message) orientation = await getOrientation(buffer);
      } catch (error) {}
      let deg = '0';
      if (orientation === 8) deg = '270';
      else if (orientation === 6) deg = '90';
      const orientDeg = [6, 8];
      style = orientDeg.includes(orientation)
        ? `-webkit-transform: rotate(${deg}deg);-moz-transform: rotate(${deg}deg);-o-transform: rotate(${deg}deg);-ms-transform: rotate(${deg}deg);transform: rotate(${deg}deg);`
        : '';
      return style;
    } catch (error) {
      return style;
    }
  }

  // image url to base64 Buffer
  async getBase64FromImgUrl(imgUrl: string, isNeed = false) {
    try {
      const successData = await this.api.get(imgUrl, undefined, undefined, {
        responseType: 'arraybuffer',
      });
      if (successData === k500Error) return kInternalError;
      if (isNeed) return Buffer.from(successData, 'binary');
      else return Buffer.from(successData, 'binary').toString('base64');
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async objectToExcel(rawData, extraParam: any = false, isCrif = false) {
    try {
      if (!rawData.sheets || !rawData.data)
        return kParamMissing('sheets or data');

      const workbook = new excel.Workbook();
      const bigFonts = workbook.createStyle(kBigFonts);
      const smallFonts = workbook.createStyle(kSmallFonts);

      // Check if rawData.sheets is an array (multiple sheets) or a string (single sheet)
      if (Array.isArray(rawData.sheets)) {
        // Handle multiple sheets
        for (
          let sheetIndex = 0;
          sheetIndex < rawData.sheets.length;
          sheetIndex++
        ) {
          const currentSheet = workbook.addWorksheet(
            rawData.sheets[sheetIndex],
          );
          const sheetDetails = rawData.data[sheetIndex];
          await this.writeSheetData(
            currentSheet,
            sheetDetails,
            bigFonts,
            smallFonts,
            extraParam,
          );
        }
      } else if (typeof rawData.sheets === 'string') {
        // Handle single sheet
        const currentSheet = workbook.addWorksheet(rawData.sheets);
        const sheetDetails = rawData.data;
        await this.writeSheetData(
          currentSheet,
          sheetDetails,
          bigFonts,
          smallFonts,
          extraParam,
        );
      } else return k422ErrorMessage('Invalid sheets data');
      const sheetName = rawData?.sheetName;
      const date = this.dateService.dateToReadableFormat(new Date());
      const dateTime = `${date.hours}-${date.minutes}`;
      const extension = !(sheetName && sheetName.includes('.xlsx'))
        ? '.xlsx'
        : '';
      const currentTime = new Date().getTime();
      let filePath = sheetName
        ? `upload/report/${date.readableStr}-${currentTime}-${dateTime}-${sheetName}${extension}`
        : 'upload/file.xlsx';
      if (isCrif) filePath = `upload/report/reports_${currentTime}.xlsx`;
      return await new Promise((resolve) => {
        workbook.write(filePath, (err) => {
          if (err) {
            console.log({ err });
            resolve(kInternalError);
          } else {
            resolve({ filePath });
          }
        });
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async writeSheetData(
    sheet,
    sheetDetails,
    bigFonts,
    smallFonts,
    extraParam: any = false,
  ) {
    const columnWidths = [];
    // Iterate through rows in sheetDetails to find maximum content length for each column
    for (let rowIndex = 0; rowIndex < sheetDetails.length; rowIndex++) {
      const rowDetails = sheetDetails[rowIndex];
      let columnIndex = 1;

      // Iterate through key-value pairs in each row
      for (const [key, value] of Object.entries(rowDetails)) {
        // Calculate cell content length
        let cellContentLength: any = '';
        if (extraParam) cellContentLength = value.toString().length;
        else cellContentLength = (value ?? '-').toString().length;

        // Update column width if the current content length is greater than the stored width
        columnWidths[columnIndex - 1] = Math.max(
          columnWidths[columnIndex - 1] || 0,
          cellContentLength,
        );
        columnIndex++;
      }
    }
    // Set column widths based on the maximum content length in each column
    for (let i = 0; i < columnWidths.length; i++) {
      const width = Math.max(columnWidths[i] * 1.75, 10); // Minimum width of 10 for readability
      sheet.column(i + 1).setWidth(width);
    }

    // Write the data to the sheet with proper formatting
    for (let rowIndex = 0; rowIndex < sheetDetails.length; rowIndex++) {
      const rowDetails = sheetDetails[rowIndex];
      let columnIndex = 1;

      for (const [key, value] of Object.entries(rowDetails)) {
        // Write header row (first row) with big fonts style
        if (rowIndex === 0)
          sheet.cell(1, columnIndex).string(key).style(bigFonts);

        // Write cell values with small fonts style
        let cellValue: any = value ?? '-';

        if (extraParam) {
          // If the cell value is an empty string, skip the rest and leave the cell blank
          if (cellValue === '') {
            columnIndex++;
            continue;
          }
        } else {
          if (cellValue == '') cellValue = '-';
        }
        let tempVal = cellValue;
        if (typeof tempVal == 'string') {
          tempVal = tempVal.includes(kRuppe)
            ? tempVal.replace(/[₹,]/g, '')
            : tempVal.includes(',')
            ? tempVal.replace(/,/g, '')
            : cellValue;
          cellValue = !isNaN(tempVal) ? +tempVal : cellValue;
        }
        if (typeof cellValue == 'number')
          sheet
            .cell(rowIndex + 2, columnIndex)
            .number(cellValue)
            .style(smallFonts);
        else
          sheet
            .cell(rowIndex + 2, columnIndex)
            .string(cellValue.toString())
            .style(smallFonts);

        columnIndex++;
      }
    }
  }

  async objectToExcelURL(
    rawData: any,
    extraParam: any = false,
    isCrif = false,
  ) {
    try {
      const excelData: any = await this.objectToExcel(
        rawData,
        extraParam,
        isCrif,
      );
      if (excelData.message) return excelData;

      const filePath = excelData.filePath;

      if (!filePath) return kInternalError;
      let fileName: any;
      if (rawData.reportStore == true) {
        let startDate = this.typeService.getDateFormatted(
          rawData.startDate ?? new Date(),
        );
        let endDate = this.typeService.getDateFormatted(
          rawData.endDate ?? new Date(),
        );
        fileName = excelData.filePath.split('/')[2] ?? '';
        fileName = fileName.replace(/ /g, '_') ?? '';
        if (startDate && endDate) {
          fileName = fileName.split('.');
          fileName = `${fileName[0]}_${startDate}_to_${endDate}.${fileName[1]}`;
        }
      }
      const urlData = await this.uploadFile(
        filePath,
        CLOUD_FOLDER_PATH.reports,
        'xlsx',
        fileName,
      );

      if (urlData == k500Error) return kInternalError;
      return urlData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // xlsx, csv file to array, key:xlsx
  async excelToArray(
    filePath,
    customColumns = {},
    needColumnName = false,
    empty?,
    date = true,
  ) {
    try {
      if (!filePath.endsWith('.xlsx') && !filePath.endsWith('.csv'))
        return k422ErrorMessage(
          'Invalid file type. Only XLSX and CSV files are supported',
        );
      const Excel = require('exceljs');
      const workbook = new Excel.Workbook();
      const readFileMethod = filePath.endsWith('.xlsx') ? 'xlsx' : 'csv';
      await workbook[readFileMethod].readFile(filePath);

      let worksheet = workbook.getWorksheet(1);
      if (!worksheet) worksheet = workbook.getWorksheet();
      const finalizedArray = [];
      const keyArr = [];
      const allColumnName = [];
      // Get column names
      worksheet.getRow(1).eachCell((cell, colNumber) => {
        try {
          const columnName = cell.value;
          keyArr[colNumber] = customColumns[columnName]
            ? customColumns[columnName]
            : columnName;
          allColumnName.push(keyArr[colNumber]);
        } catch (error) {}
      });

      for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
        const row = worksheet.getRow(rowNumber);
        const rowData = {};
        try {
          row.eachCell((cell, colNumber) => {
            const columnName = keyArr[colNumber];
            let cellVal = cell.value;
            if (cellVal instanceof Date && !date) {
              const day = String(cellVal.getDate());
              const month = String(cellVal.getMonth() + 1);
              const year = cellVal.getFullYear();
              cellVal = `${day}-${month}-${year}`;
            }
            rowData[columnName] = cellVal;
          });
          finalizedArray.push(rowData);
        } catch (error) {}
      }
      if (needColumnName)
        return { columnName: allColumnName, finalData: finalizedArray };
      return finalizedArray;
    } catch (error) {
      if (empty == true) {
        if (error.message.includes('End of data reached (data length = 0')) {
          return [];
        }
      }
      return kInternalError;
    }
  }

  async downloadAndReadExcel(url, localDirectory) {
    try {
      // Ensure the local directory exists
      if (!fs.existsSync(localDirectory)) {
        fs.mkdirSync(localDirectory, { recursive: true });
      }
      // Construct the local file path with a file name
      const fileName = path.basename(url);
      const localFilePath = path.join(localDirectory, fileName);

      // Download the file
      const response = await axios({
        url,
        method: 'GET',
        responseType: 'stream',
      });
      // Create a writable stream
      const writer = fs.createWriteStream(localFilePath);

      // Pipe the response data to the file
      response.data.pipe(writer);
      // Wait for the file to be fully written
      await new Promise((resolve, reject) => {
        // @ts-ignore
        writer.on('finish', resolve);
        writer.on('error', reject);
      });
      return localFilePath;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      console.error('Error:', error);
    }
  }

  async objToDynamicExcel(excelObj) {
    // Create a new workbook
    const wb = new excel.Workbook();

    // Add a worksheet to the workbook
    const ws = wb.addWorksheet('Sheet 1');

    const cellStyle = wb.createStyle({
      border: {
        left: { style: 'thin', color: 'black' },
        right: { style: 'thin', color: 'black' },
        top: { style: 'thin', color: 'black' },
        bottom: { style: 'thin', color: 'black' },
      },
      fill: {
        type: 'pattern',
        patternType: 'solid',
        fgColor: '#FFFFFF',
      },
      font: { color: '#000000', size: 12, bold: true },
      alignment: { horizontal: 'center', vertical: 'center' },
    });

    // ws.cell(2, 2).number(123).style(cellStyle); // B2
    // ws.cell(3, 3).date(new Date()).style(cellStyle); // C3
    // ws.cell(4, 4).formula('SUM(A2:B2)').style(cellStyle); // D4 (example formula)

    excelObj.forEach((el) => {
      const format = typeof el.value;
      if (format == 'string')
        ws.cell(el.r, el.c).string(el.value).style(cellStyle);
      else if (format == 'number')
        ws.cell(el.r, el.c).number(el.value).style(cellStyle);
    });

    // Save the workbook to a file
    wb.write('Example.xlsx');
  }

  async uploadFilePublicBucket(
    filePath: string,
    folderName?: string,
    extension?: string,
  ): Promise<any> {
    try {
      let file = filePath.replace('./', '');
      const content = await fs.promises.readFile(filePath);
      const month = new Date().toLocaleString('default', { month: 'short' });
      const year = new Date().getFullYear();

      const objectName =
        (folderName ?? 'DEFAULT') + '/' + `${month}-${year}` + '/' + file;

      let fileType = extension ?? null;
      if (!extension) {
        const match = file.match(/\.([a-zA-Z0-9]+)(?:\?.*)?$/);
        fileType = match ? match[1] : 'pdf';
      }

      const putObjectRequest: requests.PutObjectRequest = {
        namespaceName: EnvConfig.oracle.namespace,
        bucketName: EnvConfig.oracle.public_bucket,
        putObjectBody: content,
        objectName,
        contentType: kMimeTypes[fileType],
        opcMeta: {
          'Content-Disposition': 'inline',
        },
      };

      ///upload file
      await this.objectStorageClient.putObject(putObjectRequest);
      const base_url =
        'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/';
      return base_url + objectName;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
    }
  }

  async xmlToJson(rawData) {
    try {
      if (!rawData) return;
      const decodedXml = rawData
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&amp;/g, '&');
      //xml to json
      const result = await xml2js.parseStringPromise(decodedXml, {
        explicitArray: false,
      });

      return result;
    } catch (error) {
      console.log(error);
    }
  }

  // Function to remove a folder and its contents
  async removeFolder(filePath: string) {
    try {
      if (fs.existsSync(filePath))
        fs.rmSync(filePath, { recursive: true, force: true });
    } catch (error) {
      console.log({ error });
    }
  }
}
