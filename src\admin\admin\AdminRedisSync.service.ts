import { Injectable, OnModuleInit } from '@nestjs/common';
import { AdminRepository } from 'src/repositories/admin.repository';
import { RedisService } from 'src/redis/redis.service';
import { CSE_ROLE_ID } from 'src/constants/globals';
import { ConfigsRepository } from 'src/repositories/configs.repository';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { bannerEntity } from 'src/entities/banner.entity';
import { TypeService } from 'src/utils/type.service';
import { Op } from 'sequelize';
import { TemplateRepository } from 'src/repositories/template.repository';
import { k500Error } from 'src/constants/misc';
import { kInternalError } from 'src/constants/responses';
import { BankListRepository } from 'src/repositories/bankList.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { HypothecationEntity } from 'src/entities/hypothecation.entity';
import { ThirdPartyServiceRepo } from 'src/repositories/thirdParty.service.repo';
import { ThirdPartyProviderRepo } from 'src/repositories/thirdpartyService.provider.repo';
import { NUMBERS } from 'src/constants/numbers';
import { BANKINGADMINS, kMiscRedisKeys } from 'src/constants/strings';
import { UserPermissionRepository } from 'src/repositories/userPermission.repository';
import { AssignmentSharedService } from 'src/shared/assignment.service';
import { kLegalDocTypes } from 'src/constants/objects';
import { MediaRepository } from 'src/repositories/media.repository';
@Injectable()
export class AdminRedisSyncService implements OnModuleInit {
  constructor(
    private readonly adminRepo: AdminRepository,
    private readonly repoManager: RepositoryManager,
    private readonly redisService: RedisService,
    private readonly configRepo: ConfigsRepository,
    private readonly typeService: TypeService,
    private readonly commonSharedService: CommonSharedService,
    private readonly assignmentSharedService: AssignmentSharedService,
    private readonly templateRepo: TemplateRepository,
    private readonly bankListRepo: BankListRepository,
    private readonly providerRepo: ThirdPartyProviderRepo,
    private readonly serviceRepo: ThirdPartyServiceRepo,
    private readonly userPermissionRepo: UserPermissionRepository,
    private readonly mediaRepo: MediaRepository,
  ) {}
  onModuleInit() {
    this.storeAdmins();
    this.storeCSEOnRedis();
    this.storeConfigData();
    this.storeTempListForInsuranceFAQ();
    this.storeTempListTempIdDataOnRedis();
    this.storeDefaultKeys();
    this.storeBankList();
    this.commonSharedService.employeeEligibilityList();
    this.storeHypothicationLenders();
    this.storeAppServicesInRedis();
    this.storeProvidersInRedis();
    this.storePermissionsInRedis();
    this.storeAllAdminsOnRedis();
    this.setLegalDocUserList();
  }

  async storeAdmins() {
    try {
      const attr = ['id', 'isLogin'];
      const options = { where: { isActive: '1' } };
      const adminList = await this.adminRepo.getTableWhereData(attr, options);
      const object = {};
      adminList.forEach(
        (item: {
          lastActivityTime: any;
          isLogin: any;
          id: string | number;
        }) => {
          object[item.id] = {
            isLogin: item.isLogin,
          };
          if (item.isLogin == 1) {
            object[item.id].lastActivityTime = new Date();
          }
        },
      );
      const updatedAdmins = JSON.stringify(object);
      const result = await this.redisService.set('ADMIN_LIST', updatedAdmins);
      return result;
    } catch (error) {
      return false;
    }
  }

  async storeCSEOnRedis() {
    try {
      const attr = ['id', 'fullName', 'isLogin'];
      const options = { where: { roleId: CSE_ROLE_ID, isActive: '1' } };
      const adminList = await this.adminRepo.getTableWhereData(attr, options);
      const updatedCSEs = JSON.stringify(adminList);
      const result = await this.redisService.set('CSE_ADMIN_LIST', updatedCSEs);
      return result;
    } catch (error) {
      return false;
    }
  }

  async storeConfigData() {
    try {
      const attributes = [
        'androidVersion',
        'bankingProKeySecret',
        'bankingProAppID',
        'chatbotData',
        'iosVersion',
        'stuckContactUsFlow',
        'nbfcAndroidVersion',
        'nbfcIosVersion',
      ];
      const options = {};
      let configData: any = await this.configRepo.getRowWhereData(
        attributes,
        options,
      );
      if (configData === k500Error) return kInternalError;
      configData = JSON.stringify(configData);
      return await this.redisService.set(kMiscRedisKeys?.configs, configData);
    } catch (error) {
      return false;
    }
  }

  // Function to Store Latest Banners In Redis(Caching)
  async storeBanners() {
    try {
      const todayDate = this.typeService.getGlobalDate(new Date());
      const attr = [
        'title',
        'description',
        'fromDate',
        'toDate',
        'adminId',
        'platForm',
        'screen',
        'createdAt',
        'updatedAt',
        'isAllTime',
        'bannerUrl',
        'isWeekendOnly',
      ];
      const options = {
        where: {
          [Op.or]: [
            {
              [Op.and]: [
                { fromDate: { [Op.lte]: todayDate } },
                { toDate: { [Op.gte]: todayDate } },
              ],
            },
            { isAllTime: { [Op.eq]: true } },
            { isWeekendOnly: { [Op.eq]: true } },
          ],
        },
        order: [['id', 'DESC']],
      };
      let banners = await this.repoManager.getTableWhereData(
        bannerEntity,
        attr,
        options,
      );
      banners = banners.filter((el) => {
        if (el?.isWeekendOnly) {
          //add only weekend day if isWeekendOnly is true
          if (
            (el?.isAllTime ||
              (el?.fromDate <= todayDate && el?.toDate >= todayDate)) &&
            [0, 6].includes(todayDate.getDay())
          )
            return true;
          return false;
        }
        return true;
      });
      const bannersList = JSON.stringify(banners);
      const result = await this.redisService.set('BANNERS_LIST', bannersList);
      return result;
    } catch (error) {
      return false;
    }
  }

  async storeTempListForInsuranceFAQ() {
    const faqAttr: any = ['title', 'content', ['templateId', 'otherLink']];
    const templateList = await this.templateRepo.getTableWhereData(faqAttr, {
      where: {
        type: 'FAQ',
        subType: 'INSURANCE',
      },
      order: [['id', 'ASC']],
    });
    if (templateList == k500Error) throw new Error();
    let key = 'TEMPLATE_FAQ_INSURANCE';
    const updatedTempData = JSON.stringify(templateList);
    const result = await this.redisService.set(key, updatedTempData);
    return result;
  }

  async storeTempListTempIdDataOnRedis() {
    const templateList = await this.templateRepo.getTableWhereData(
      [
        'id',
        'title',
        'content',
        'type',
        'subType',
        'isActive',
        'templateId',
        'lspTemplateId',
      ],
      {
        where: { templateId: { [Op.ne]: null } },
      },
    );
    if (templateList == k500Error) throw new Error();
    let key = 'TEMPLATE_DATA_BY_TEMPID';
    const updatedTempData = JSON.stringify(templateList);
    const result = await this.redisService.set(key, updatedTempData);
    return result;
  }

  async storeDefaultKeys() {
    const key = 'MOCK_SERVICES';
    const value = { cibil_hard_pull: true };
    const result = await this.redisService.set(key, JSON.stringify(value));
    return result;
  }

  async storeBankList() {
    const attributes = [
      'aaService',
      'id',
      'bankCode',
      'bankName',
      'image',
      'pdfService',
      'service',
      'bankImg',
      'lspBankImg',
      'statusFlag',
    ];
    const options = {
      order: [['bankName', 'ASC']],
    };
    const banks = await this.bankListRepo.getTableWhereData(
      attributes,
      options,
    );
    if (banks == k500Error) throw new Error();
    const activeBanks = banks.filter((bank) => bank?.statusFlag == 1);
    await Promise.all([
      this.redisService.set('BANKS_LIST', JSON.stringify(activeBanks)),
      this.redisService.set(kMiscRedisKeys?.banks, JSON.stringify(banks)),
    ]);
  }

  async storeHypothicationLenders() {
    const lenders = await this.repoManager.getTableWhereData(
      HypothecationEntity,
      [
        'lenderName',
        'loanAmount',
        'hypothecatedAmount',
        'minLoanAmount',
        'outStandingAmount',
        'maxLoanAmount',
        'hypothecatedpercentage',
        'dayOfEMI',
        'loanTenure',
        'approvedPrincipalAmount',
        'emiAmount',
        'repaymentPercentage',
        'max_dpd',
        'interestAmount',
        'adminId',
        'repaymentSchedule',
        'lenderDoc',
        'createdAt',
        'updatedAt',
      ],
      {},
    );
    if (lenders == k500Error) throw new Error();
    await this.redisService.set(
      'HYPOTHICATION_LENDERS',
      JSON.stringify(lenders),
      NUMBERS.SEVEN_DAYS_IN_SECONDS,
    );
  }

  // Store app services  in REDIS....
  async storeAppServicesInRedis() {
    const appServices = await this.serviceRepo.getTableWhereData(
      ['id', 'name', 'status', 'totalProviderIds', 'activeProviderIds'],
      {
        where: { id: { [Op.ne]: null } },
        order: [['id', 'ASC']],
      },
    );

    if (appServices == k500Error)
      throw new Error('Error while getting the appService data');
    await this.redisService.set(
      kMiscRedisKeys?.services,
      JSON.stringify(appServices),
    );
  }

  // Store service providers in REDIS....
  async storeProvidersInRedis() {
    const serviceProviders = await this.providerRepo.getTableWhereData(
      ['id', 'name', 'status'],
      {
        where: { id: { [Op.ne]: null } },
        order: [['id', 'ASC']],
      },
    );

    if (serviceProviders == k500Error)
      throw new Error('Error while getting serviceProvider data');
    await this.redisService.set(
      kMiscRedisKeys?.providers,
      JSON.stringify(serviceProviders),
    );
  }

  // Store app permissions in REDIS....
  async storePermissionsInRedis() {
    const userPermissions = await this.userPermissionRepo.getTableWhereData(
      ['id', 'title', 'description', 'img', 'android', 'IOS'],
      {
        where: { id: { [Op.ne]: null } },
        order: [['id', 'ASC']],
      },
    );
    if (userPermissions == k500Error)
      throw new Error("Error while getting user's permissions");
    await this.redisService.set(
      kMiscRedisKeys?.permissions,
      JSON.stringify(userPermissions),
    );
  }
  async storeAllAdminsOnRedis() {
    try {
      await this.assignmentSharedService.fetchAdminAccordingRole(BANKINGADMINS);
    } catch (error) {
      return false;
    }
  }

  async setLegalDocUserList() {
    const key = 'LEGAL_DOC_USER_IDS';
    const attributes = ['userId'];
    const options = {
      where: { docType: kLegalDocTypes },
      order: [['id', 'DESC']],
    };
    const mediaData = await this.mediaRepo.getTableWhereData(
      attributes,
      options,
    );
    if (mediaData == k500Error || !mediaData.length) return {};
    const userIdList = [...new Set(mediaData.map((a) => a.userId))];
    await this.redisService.set(
      key,
      JSON.stringify(userIdList),
      NUMBERS.SEVEN_DAYS_IN_SECONDS,
    );
    return {};
  }
}
