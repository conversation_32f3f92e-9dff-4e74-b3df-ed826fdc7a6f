// Imports
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Op, Sequelize, where } from 'sequelize';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
} from 'src/constants/responses';
import { LoanRepository } from 'src/repositories/loan.repository';
import { DisbursmentRepository } from 'src/repositories/disbursement.repository';
import { TypeService } from 'src/utils/type.service';
import {
  KICICIUPI,
  KICICIUPI2,
  KYESUPI,
  kAdmins,
  kApp,
  kAutoDebit,
  kCashfree,
  kCompleted,
  kDirectBankPay,
  kFullPay,
  kHelpContact,
  kNoDataFound,
  kRazorpay,
  kRefund,
  kRuppe,
  kSupportMail,
  kUpi,
  kWeb,
} from 'src/constants/strings';
import {
  CASHFREE_HEADERS_V2,
  CLOUD_FOLDER_PATH,
  confirmation_phrases,
  HypothecationLenderColumns,
  kMonths,
  kRazorpayM1Auth,
  kRazorpayM2Auth,
  lcrFields,
  ledgerDisbFields,
  ledgerFields,
} from 'src/constants/objects';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { APIService } from 'src/utils/api.service';
import { CF_SETTLEMENT, nZohoBooksUrl } from 'src/constants/network';
import { DateService } from 'src/utils/date.service';
import { SettlementRepository } from 'src/repositories/settlement.repository';
import { RazorpoayService } from 'src/thirdParty/razorpay/razorpay.service';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import {
  GLOBAL_CHARGES,
  GLOBAL_RANGES,
  LCR_DATA_SEND_THRESHOLD_DAYS,
  NBFC_ADDRESS,
  NBFC_NAME,
  PAGE_LIMIT,
  PRODUCT_DESCRIPTION,
  SYSTEM_ADMIN_ID,
  gIsPROD,
  isUAT,
} from 'src/constants/globals';
import { registeredUsers } from 'src/entities/user.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { CryptService } from 'src/utils/crypt.service';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { EmiEntity } from 'src/entities/emi.entity';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { FileService } from 'src/utils/file.service';
import { SequelOptions } from 'src/interfaces/include.options';
import { loanTransaction } from 'src/entities/loan.entity';
import {
  kLedgerStatementPath1,
  kLedgerStatementPath2,
  tLCRReportToManagementTemplate,
} from 'src/constants/directories';
import { EnvConfig } from 'src/configs/env.config';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { ReportHistoryRepository } from 'src/repositories/reportHistory.repository';
import { LCREntity } from 'src/entities/lcr.entity';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import * as fs from 'fs';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { DashboardService } from '../dashboard/dashboard.service';
import { ReportService } from '../report/report.service';
import { HypothecationEntity } from 'src/entities/hypothecation.entity';
import { HypothecationPaymentsEntity } from 'src/entities/hypothecationPayment.entity';
import { HypothecationHistoryEntity } from 'src/entities/hypothecationHistory.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { ErrorContextService } from 'src/utils/error.context.service';
import { accountSectionSummary } from 'src/entities/accountSectionSummary.entity';
import { WaiverEntity } from 'src/entities/waiver.entity';
import { RefundService } from '../transaction/refund.service';
import { LoanService } from '../loan/loan.service';

@Injectable()
export class TallyService {
  constructor(
    private readonly loanRepo: LoanRepository,
    private readonly repository: SettlementRepository,
    private readonly disRepo: DisbursmentRepository,
    private readonly transRepo: TransactionRepository,
    private readonly typeService: TypeService,
    private readonly apiService: APIService,
    private readonly dateService: DateService,
    private readonly cryptService: CryptService,
    private readonly commonSharedService: CommonSharedService,
    private readonly repoManager: RepositoryManager,
    private readonly reportHistoryRepo: ReportHistoryRepository,
    // Third party
    private readonly razorService: RazorpoayService,
    // Util Service
    private readonly fileService: FileService,
    private readonly notificationService: SharedNotificationService,
    private readonly common: CommonSharedService,
    private readonly slack: SlackService,
    @Inject(forwardRef(() => DashboardService))
    private readonly dashboardService: DashboardService,
    private readonly errorContextService: ErrorContextService,

    @Inject(forwardRef(() => ReportService))
    private readonly report: ReportService,
    @Inject(forwardRef(() => RefundService))
    private readonly refundService: RefundService,
    private readonly loanService: LoanService,
  ) {}

  // Disbursement summary card
  async allDisbursementDetails(reqData) {
    try {
      const bankDetails = fs.readFileSync('bankDetails.json', 'utf8');
      const kTallyPayoutBanks = bankDetails ? JSON.parse(bankDetails) : {};
      const startDate = this.typeService
        .getGlobalDate(reqData?.startDate ?? new Date())
        .toJSON();
      const endDate = this.typeService
        .getGlobalDate(reqData?.endDate ?? new Date())
        .toJSON();
      const range = await this.typeService.getUTCDateRange(startDate, endDate);
      const options: any = {
        where: {
          loan_disbursement: '1',
          loan_disbursement_date: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
        },
      };
      const attributes = [
        'id',
        'netApprovedAmount',
        'charges',
        'processingFees',
        'stampFees',
        'loanFees',
        'insuranceDetails',
        'loan_disbursement',
        'loan_disbursement_date',
      ];
      const loanData = await this.loanRepo.getTableWhereDataWithCounts(
        attributes,
        options,
      );
      if (loanData === k500Error) return kInternalError;

      //total loan approved amount
      const totalNetApprovedAmount = loanData.rows
        .filter((item) => item.loan_disbursement === '1')
        .reduce((total, item) => total + parseFloat(item.netApprovedAmount), 0);

      const totalAmount = this.typeService.amountNumberWithCommas(
        totalNetApprovedAmount,
      );

      let totalProcessingFees = 0;
      let totalStampFees = 0;
      let onlineConvinenceFees = 0;
      let totalDocumentCharges = 0;
      let totalCGST = 0;
      let totalSGST = 0;
      let totalInsuranceCharges = 0;

      for (const loans of loanData.rows) {
        try {
          let processPerc =
            loans?.loanFees -
            (loans?.charges?.insurance_fee || 0) -
            (loans?.charges?.doc_charge_amt || 0) -
            (loans?.charges?.gst_amt || 0);
          totalProcessingFees += processPerc;
          totalStampFees += loans?.stampFees;
          onlineConvinenceFees += loans?.charges?.insurance_fee || 0;
          totalDocumentCharges += loans?.charges?.doc_charge_amt;
          totalCGST += loans?.charges?.cgst_amt || 0;
          totalSGST += loans?.charges?.sgst_amt || 0;
          totalInsuranceCharges += loans?.insuranceDetails?.totalPremium || 0;
        } catch (error) {}
      }

      const totalCharges =
        totalProcessingFees +
        totalStampFees +
        onlineConvinenceFees +
        totalDocumentCharges +
        totalCGST +
        totalSGST +
        totalInsuranceCharges;

      const disOptions: any = {
        where: {
          status: { [Op.or]: ['processed', 'processing', 'queued'] },
          updatedAt: {
            [Op.gte]: range.fromDate,
            [Op.lte]: range.endDate,
          },
        },
      };
      const disAttributes = ['id', 'amount', 'status', 'source'];
      const disData = await this.disRepo.getTableWhereData(
        disAttributes,
        disOptions,
      );
      if (disData === k500Error) return kInternalError;

      let totalICICI304 = 0;
      let totalICICI753 = 0;
      let totalRBL = 0;
      let totalPendingDisbursement = 0;
      for (const dis of disData) {
        if (dis.status === 'processed') {
          try {
            if (dis.source === 'RAZORPAY_M2') {
              totalICICI304 += dis.amount / 100;
            } else if (dis.source === kCashfree) {
              totalICICI753 += dis.amount / 100;
            } else {
              totalRBL += dis.amount / 100;
            }
          } catch (error) {}
        } else if (dis.status === 'processing' || dis.status === 'queued') {
          totalPendingDisbursement += dis.amount / 100;
        }
      }

      let totalDisbursedAmount = totalICICI304 + totalICICI753 + totalRBL;
      let totalCreditAmount = totalDisbursedAmount + totalCharges;

      const finalData: any = {
        totalLoan: loanData.count,
        titles: [
          {
            name: 'LOAN AMOUNT TO BORROWER',
            debit: kRuppe + totalAmount,
            credit: '-',
          },
          {
            name: 'CHARGES TO BORROWER',
            credit:
              kRuppe + this.typeService.amountNumberWithCommas(totalCharges),
            debit: '-',
            subtitles: [
              {
                name: 'PROCESSING FEES',
                credit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(totalProcessingFees),
                debit: '-',
              },
              {
                name: 'STAMP DUTY',
                credit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(totalStampFees),
                debit: '-',
              },
              {
                name: 'ONLINE CONVINENCE FEES',
                credit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(onlineConvinenceFees),
                debit: '-',
              },
              {
                name: 'DOCUMENT CHARGES',
                credit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(totalDocumentCharges),
                debit: '-',
              },
              //after migration sgst and cgst amount will come proper
              {
                name: 'SGST(9%)',
                credit:
                  kRuppe + this.typeService.amountNumberWithCommas(totalCGST),
                debit: '-',
              },
              {
                name: 'CGST(9%)',
                credit:
                  kRuppe + this.typeService.amountNumberWithCommas(totalSGST),
                debit: '-',
              },
              {
                name: 'INSURANCE CHARGES',
                credit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(
                    totalInsuranceCharges,
                  ),
                debit: '-',
              },
            ],
          },
          {
            name: 'DISBURSED AMOUNT TO BORROWER',
            credit:
              kRuppe +
              this.typeService.amountNumberWithCommas(totalDisbursedAmount),
            debit: '-',
            subtitles: [
              {
                name: kTallyPayoutBanks['RAZORPAY_M2_DEFAULT_ACC'],
                credit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(totalICICI304),
                debit: '-',
              },
              {
                name: kTallyPayoutBanks['ICICI_CONNECTED_51633_23f0101'],
                credit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(totalICICI753),
                debit: '-',
              },
              {
                name: kTallyPayoutBanks['RBL - bacc_JRtlJu0ZyNn17I'],
                credit:
                  kRuppe + this.typeService.amountNumberWithCommas(totalRBL),
                debit: '-',
              },
            ],
          },
          {
            name: 'TOTAL AMOUNT',
            debit: kRuppe + totalAmount,
            credit:
              kRuppe +
              this.typeService.amountNumberWithCommas(totalCreditAmount),
          },
          {
            name: 'LOAN DISBURSEMENT RESPONSE PENDING',
            debit:
              kRuppe +
              this.typeService.amountNumberWithCommas(totalPendingDisbursement),
            credit: '-',
          },
        ],
      };
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //repayment summary card
  async getRepaymentData(reqData) {
    try {
      const startDate = this.typeService
        .getGlobalDate(reqData?.startDate ?? new Date())
        .toJSON();
      const endDate = this.typeService
        .getGlobalDate(reqData?.endDate ?? new Date())
        .toJSON();
      const options: any = {
        where: {
          status: kCompleted,
          completionDate: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
          type: { [Op.ne]: kRefund },
        },
        order: [['createdAt', 'DESC']],
      };
      const attributes = [
        'id',
        'source',
        'subSource',
        'loanId',
        'paidAmount',
        'principalAmount',
        'interestAmount',
        'penaltyAmount',
      ];
      const transData = await this.transRepo.getTableWhereData(
        attributes,
        options,
      );
      if (transData === k500Error) kInternalError;
      const loanIds = [...new Set(transData.map((el) => el.loanId))];

      let princAmount = 0;
      let intAmount = 0;
      let penaltAmount = 0;
      let razorpay1 = 0;
      let razorpay2 = 0;
      let cashfree = 0;
      let iciciDirect = 0;
      let iciciUpi = 0;
      let iciciUpi2 = 0;
      let yesUpi = 0;
      for (const trans of transData) {
        princAmount += trans?.principalAmount || 0;
        intAmount += trans?.interestAmount || 0;
        penaltAmount += trans?.penaltyAmount || 0;
        if (
          trans.source === kRazorpay &&
          (trans.subSource === kApp || trans.subSource === kWeb)
        ) {
          razorpay1 += trans.paidAmount || 0;
        } else if (
          trans.source === kRazorpay &&
          trans.subSource === kAutoDebit
        ) {
          razorpay2 += trans.paidAmount || 0;
        } else if (trans.source === kCashfree) {
          cashfree += trans.paidAmount || 0;
        } else if (trans.source === KICICIUPI) {
          iciciUpi += trans.paidAmount || 0;
        } else if (trans.source === KICICIUPI2)
          iciciUpi2 += trans.paidAmount || 0;
        else if (trans.source === KYESUPI) yesUpi += trans.paidAmount || 0;
        else if (
          trans.subSource === kDirectBankPay ||
          trans.subSource === 'DIRECT ICICI' ||
          trans.subSource === 'ICICI DIRECT - CASH' ||
          trans.subSource === 'ICICI MANUAL' ||
          trans.source === kUpi
        ) {
          iciciDirect += trans.paidAmount || 0;
        }
      }
      const paidAmount = transData.reduce(
        (total, item) => total + item.paidAmount,
        0,
      );
      const totalAmount = this.typeService.amountNumberWithCommas(paidAmount);
      const amountRecieved = this.typeService.amountNumberWithCommas(
        razorpay1 +
          razorpay2 +
          cashfree +
          iciciDirect +
          iciciUpi +
          iciciUpi2 +
          yesUpi,
      );

      const finalData: any = {
        totalLoan: loanIds.length,
        titles: [
          {
            name: 'TOTAL REPAID AMOUNT FROM BORROWER',
            debit: '-',
            credit: kRuppe + totalAmount,
            subtitles: [
              {
                name: 'PRINCIPAL',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(princAmount),
                credit: '-',
              },
              {
                name: 'INTEREST',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(intAmount),
                credit: '-',
              },
              {
                name: 'PENALTY',
                debit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(penaltAmount),
                credit: '-',
              },
              //right now field for round-off is not available so whenever it will be added we'll add it
              {
                name: 'ROUND-OFF',
                debit: '-',
                credit: '-',
              },
            ],
          },
          {
            name: 'AMOUNT RECIEVED IN',
            debit: kRuppe + amountRecieved,
            credit: '-',
            subtitles: [
              {
                name: 'RAZORPAY-1',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(razorpay1),
                credit: '-',
              },
              {
                name: 'RAZORPAY-2',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(razorpay2),
                credit: '-',
              },
              {
                name: kCashfree,
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(cashfree),
                credit: '-',
              },
              {
                name: 'BANK TRANSFER[ICICI BANK - 30400]',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(iciciDirect),
                credit: '-',
              },
              {
                name: 'UPI [ICICI Bank - 753]',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(iciciUpi),
                credit: '-',
              },
              {
                name: 'UPI [ICICI Bank - 400]',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(iciciUpi2),
                credit: '-',
              },
              // To do. Replace this Bank a/c no. to original and make this dynamic
              {
                name: 'UPI [Yes Bank - 556]',
                debit: kRuppe + this.typeService.amountNumberWithCommas(yesUpi),
                credit: '-',
              },
            ],
          },
          {
            name: 'TOTAL NET IN-FLOW',
            debit: kRuppe + amountRecieved,
            credit: kRuppe + totalAmount,
          },
        ],
      };
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //sync settlements
  async syncSettlements(reqData) {
    // Query preparation
    const startDate = reqData?.startDate
      ? new Date(reqData?.startDate)
      : new Date();
    const endDate = reqData?.endDate ? new Date(reqData?.endDate) : new Date();

    //Razorpay
    const params = {
      count: 1000,
      year: 0,
      month: 0,
      day: 0,
    };

    let razorpayData1: any = [];
    let razorpayData2: any = [];

    for (
      let currentDate = startDate;
      currentDate <= endDate;
      currentDate.setDate(currentDate.getDate() + 1)
    ) {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      const day = currentDate.getDate();

      params.year = year;
      params.month = month;
      params.day = day;

      // Fetch settlement details from razorpay - 1
      const razorpay1: any = await this.razorService.fetchSettlements(
        params,
        kRazorpayM1Auth,
      );
      razorpayData1.push(...razorpay1);
      // Fetch settlement details from razorpay - 2
      const razorpay2: any = await this.razorService.fetchSettlements(
        params,
        kRazorpayM2Auth,
      );
      razorpayData2.push(...razorpay2);
    }
    const data: any = {};

    if (CASHFREE_HEADERS_V2['X-Client-Id']) {
      // Cashfree
      const headers = CASHFREE_HEADERS_V2;
      const rangeData = await this.dateService.utcDateRange(startDate, endDate);
      const body = {
        pagination: { limit: 100 },
        filters: {
          start_date: rangeData.minRange,
          end_date: rangeData.maxRange,
        },
      };
      const cashfree = await this.apiService.post(
        CF_SETTLEMENT,
        body,
        headers,
        null,
      );
      if (!cashfree?.message) data.cashfree = cashfree;
    }

    if (!razorpayData1?.message) data.razorpay1 = razorpayData1;
    if (!razorpayData2?.message) data.razorpay2 = razorpayData2;
    const preparedData = await this.prepareSettlementData(data);
    return preparedData;
  }

  private async prepareSettlementData(data) {
    try {
      const razorpay1Settlements = [];
      data?.razorpay1?.forEach((item) => {
        try {
          razorpay1Settlements.push({
            settlementId: item?.settlement_id,
            status: item?.settled === true ? 1 : null,
            paymentGateway: 0,
            bankAccount: 1,
            amount: item?.paymentAmount
              ? parseFloat((item.paymentAmount / 100).toFixed(2))
              : 0,
            adjustment: item?.refundAmount
              ? parseFloat((item.refundAmount / 100).toFixed(2))
              : 0,
            fees: item?.fees ? parseFloat((item.fees / 100).toFixed(2)) : 0,
            tax: item?.tax ? parseFloat((item.tax / 100).toFixed(2)) : 0,
            utr: item?.utr,
            settlementDate: new Date(item?.settled_at * 1000),
            response: JSON.stringify(item),
          });
        } catch (error) {}
      });
      const razorpay2Settlements = [];
      data?.razorpay2?.forEach((item) => {
        try {
          razorpay2Settlements.push({
            settlementId: item?.settlement_id,
            status: item?.settled === true ? 1 : null,
            paymentGateway: 1,
            bankAccount: 1,
            amount: item?.paymentAmount
              ? parseFloat((item.paymentAmount / 100).toFixed(2))
              : 0,
            adjustment: item?.refundAmount
              ? parseFloat((item.refundAmount / 100).toFixed(2))
              : 0,
            fees: item?.fees ? parseFloat((item.fees / 100).toFixed(2)) : 0,
            tax: item?.tax ? parseFloat((item.tax / 100).toFixed(2)) : 0,
            utr: item?.utr,
            settlementDate: new Date(item?.settled_at * 1000),
            response: JSON.stringify(item),
          });
        } catch (error) {}
      });
      // Cashfree
      const cashfreeSettlements = [];
      data?.cashfree?.data?.forEach((item) => {
        try {
          const settlementCharge = parseFloat(
            item?.settlement_charge?.toFixed(2) ?? 0,
          );
          const settlementTax = parseFloat(
            item?.settlement_tax?.toFixed(2) ?? 0,
          );
          const serviceCharge = parseFloat(
            item?.service_charge?.toFixed(2) ?? 0,
          );
          const serviceTax = parseFloat(item?.service_tax?.toFixed(2) ?? 0);
          cashfreeSettlements.push({
            settlementId: item?.cf_settlement_id.toString(),
            status: item?.status === 'PAID' ? 1 : null,
            paymentGateway: 2,
            bankAccount: 1,
            amount: item?.amount_settled
              ? parseFloat(item.amount_settled.toFixed(2))
              : 0,
            adjustment: item?.adjustment
              ? parseFloat(item.adjustment.toFixed(2))
              : 0,
            fees: serviceCharge + settlementCharge,
            tax: serviceTax + settlementTax,
            utr: item?.settlement_utr,
            settlementDate: item?.settlement_date,
            response: JSON.stringify(item),
          });
        } catch (error) {}
      });
      const allSettlements = [
        ...razorpay1Settlements,
        ...razorpay2Settlements,
        ...cashfreeSettlements,
      ];
      const attributes = ['id', 'settlementId'];
      const options: any = {
        where: { status: null },
      };
      const settledData = await this.repository.getTableWhereData(
        attributes,
        options,
      );
      if (settledData === k500Error) return kInternalError;
      for (let index = 0; index < allSettlements.length; index++) {
        try {
          const creationData = allSettlements[index];
          const filter = settledData.find(
            (f) => f.settlementId === creationData.settlementId,
          );
          if (filter)
            await this.repository.updateRowData(creationData, filter.id);
          await this.repository.createRawData(creationData);
        } catch (error) {}
      }

      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //wallet summary card
  async getWalletSettlementDetails(reqData) {
    try {
      const bankDetails = fs.readFileSync('bankDetails.json', 'utf8');
      const kTallyPayoutBanks = bankDetails ? JSON.parse(bankDetails) : {};
      const startDate = reqData?.startDate ?? new Date();
      const endDate = reqData?.endDate ?? new Date();
      const range = this.typeService.getUTCDateRange(startDate, endDate);

      const attributes = [
        'adjustment',
        'amount',
        'paymentGateway',
        'tax',
        'fees',
        'bankAccount',
        'settlementDate',
      ];
      const options: any = {
        where: {
          status: 1,
          settlementDate: {
            [Op.gte]: range.fromDate,
            [Op.lte]: range.endDate,
          },
        },
        order: [['id', 'DESC']],
      };
      const settledData = await this.repository.getTableWhereData(
        attributes,
        options,
      );
      if (settledData === k500Error) return kInternalError;

      let r1Amount = 0;
      let r1Gst = 0;
      let r1Charges = 0;
      let r1Adjustment = 0;
      let r2Amount = 0;
      let r2Gst = 0;
      let r2Charges = 0;
      let r2Adjustment = 0;
      let cashAmount = 0;
      let cashGst = 0;
      let cashCharges = 0;
      let cfAdjustment = 0;
      for (const data of settledData) {
        if (data?.paymentGateway === 0) {
          r1Amount += data?.amount;
          r1Gst += data?.tax;
          r1Charges += data?.fees;
          r1Adjustment += data?.adjustment ?? 0;
        } else if (data?.paymentGateway === 1) {
          r2Amount += data?.amount;
          r2Gst += data?.tax;
          r2Charges += data?.fees;
          r2Adjustment += data?.adjustment ?? 0;
        } else if (data?.paymentGateway === 2) {
          cashAmount += data?.amount;
          cashGst += data?.tax;
          cashCharges += data?.fees;
          cfAdjustment += data?.adjustment ?? 0;
        }
      }

      const razorpay1Total = this.typeService.amountNumberWithCommas(
        Math.abs(r1Amount + r1Charges + r1Gst - Math.abs(r1Adjustment)),
      );
      const razorpay2Total = this.typeService.amountNumberWithCommas(
        Math.abs(r2Amount + r2Charges + r2Gst - Math.abs(r2Adjustment)),
      );
      const cashfreeTotal = this.typeService.amountNumberWithCommas(
        Math.abs(cashAmount + cashCharges + cashGst - Math.abs(cfAdjustment)),
      );

      const totalAmount = this.typeService.amountNumberWithCommas(
        r1Amount +
          r1Charges +
          r1Gst +
          r2Amount +
          r2Charges +
          r2Gst +
          cashAmount +
          cashCharges +
          cashGst -
          Math.abs(cfAdjustment) -
          Math.abs(r1Adjustment) -
          Math.abs(r2Adjustment),
      );

      const finalData: any = {
        titles: [
          {
            name: 'RAZORPAY - 1',
            debit: '-',
            credit: kRuppe + razorpay1Total,
            subtitles: [
              {
                name: 'RAZORPAY SETTLEMENT',
                debit: '-',
                credit: kRuppe + razorpay1Total,
              },
              {
                name: 'RAZORPAY GST',
                debit: kRuppe + this.typeService.amountNumberWithCommas(r1Gst),
                credit: '-',
              },
              {
                name: 'RAZORPAY CHARGES',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(r1Charges),
                credit: '-',
              },
              {
                name: 'RAZORPAY REFUND',
                debit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(
                    Math.abs(r1Adjustment),
                  ),
                credit: '-',
              },
              {
                name: 'AMOUNT RECIEVED IN',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(r1Amount),
                credit: '-',
                section: this.mapWalletSectionData(r1Amount),
              },
            ],
          },
          {
            name: 'RAZORPAY - 2',
            debit: '-',
            credit: kRuppe + razorpay2Total,
            subtitles: [
              {
                name: 'RAZORPAY SETTLEMENT',
                debit: '-',
                credit: kRuppe + razorpay2Total,
              },
              {
                name: 'RAZORPAY GST',
                debit: kRuppe + this.typeService.amountNumberWithCommas(r2Gst),
                credit: '-',
              },
              {
                name: 'RAZORPAY CHARGES',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(r2Charges),
                credit: '-',
              },
              {
                name: 'RAZORPAY REFUND',
                debit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(
                    Math.abs(r2Adjustment),
                  ),
                credit: '-',
              },
              {
                name: 'AMOUNT RECIEVED IN',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(r2Amount),
                credit: '-',
                section: this.mapWalletSectionData(r2Amount),
              },
            ],
          },
          {
            name: kCashfree,
            debit: '-',
            credit: kRuppe + cashfreeTotal,
            subtitles: [
              {
                name: 'CASHFREE SETTLEMENT',
                debit: '-',
                credit: kRuppe + cashfreeTotal,
              },
              {
                name: 'CASHFREE GST',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(cashGst),
                credit: '-',
              },
              {
                name: 'CASHFREE CHARGES',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(cashCharges),
                credit: '-',
              },
              {
                name: `${kCashfree} REFUND`,
                debit:
                  kRuppe +
                  this.typeService.amountNumberWithCommas(
                    Math.abs(cfAdjustment),
                  ),
                credit: '-',
              },
              {
                name: 'AMOUNT RECIEVED IN',
                debit:
                  kRuppe + this.typeService.amountNumberWithCommas(cashAmount),
                credit: '-',
                section: this.mapWalletSectionData(cashAmount),
              },
            ],
          },
          {
            name: 'TOTAL',
            debit: kRuppe + totalAmount,
            credit: kRuppe + totalAmount,
          },
        ],
      };
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private mapWalletSectionData(passAmount = null) {
    const selectedSection = [];
    const bankDetails = fs.readFileSync('bankDetails.json', 'utf8');
    const kTallyPayoutBanks = bankDetails ? JSON.parse(bankDetails) : {};
    if (EnvConfig.nbfc.nbfcType == '0') {
      selectedSection.push(
        {
          name: kTallyPayoutBanks['RAZORPAY_M2_DEFAULT_ACC'],
          debit: '-',
          credit: '-',
        },
        {
          name: kTallyPayoutBanks['ICICI BANK - 753'],
          debit: kRuppe + this.typeService.amountNumberWithCommas(passAmount),
          credit: '-',
        },
        {
          name: kTallyPayoutBanks['RBL - bacc_JRtlJu0ZyNn17I'],
          debit: '-',
          credit: '-',
        },
      );
    } else if (EnvConfig.nbfc.nbfcType == '1') {
      selectedSection.push({
        name: kTallyPayoutBanks['YES_BANK_01'],
        debit: '-',
        credit: '-',
      });
    }
    return selectedSection;
  }

  //account ledger
  async getLoanDisbursementDetails(reqData) {
    try {
      const startDate = this.typeService
        .getGlobalDate(reqData?.startDate ?? new Date())
        .toJSON();
      const endDate = this.typeService
        .getGlobalDate(reqData?.endDate ?? new Date())
        .toJSON();
      const download = reqData?.download == 'true' ? true : false;
      const page = reqData?.page;
      const loanStatus = reqData?.loanStatus;
      let searchText = reqData?.searchText ?? '';

      const disInclude: any = {
        model: disbursementEntity,
        attributes: [
          'id',
          'name',
          'contact',
          'bank_name',
          'ifsc',
          'account_number',
          'utr',
          'amount',
        ],
        where: { status: 'processed' },
      };
      const options: any = {
        include: [disInclude],
        order: [['id', 'DESC']],
      };
      if (loanStatus === 'Open') {
        options.where = {
          loanStatus: 'Active',
          loan_disbursement: '1',
          loan_disbursement_date: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
        };
      } else if (loanStatus === 'Close') {
        options.where = {
          loanStatus: 'Complete',
          isNotBalanced: 0,
          loanCompletionDate: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
        };
      } else if (loanStatus === 'Closed but not balanced') {
        options.where = {
          isNotBalanced: 1,
          loanCompletionDate: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
        };
      } else {
        options.where = {
          loanStatus: {
            [Op.or]: ['Active', 'Complete'],
          },
          loan_disbursement: '1',
          [Op.or]: [
            {
              loan_disbursement_date: {
                [Op.gte]: startDate,
                [Op.lte]: endDate,
              },
            },
            {
              loanCompletionDate: {
                [Op.gte]: startDate,
                [Op.lte]: endDate,
              },
            },
          ],
        };
      }
      //search filter
      if (searchText) {
        const firstTwoLetters = searchText.substring(0, 2).toLowerCase();
        const restOfString = searchText.substring(2);
        if (firstTwoLetters == 'l-') options.where.id = +restOfString;
        else {
          if (!isNaN(searchText)) {
            disInclude.where = { contact: { [Op.iRegexp]: searchText } };
          } else disInclude.where = { name: { [Op.iRegexp]: searchText } };
        }
      }
      if (download != true) {
        options.offset = +(page ?? 1) * PAGE_LIMIT - PAGE_LIMIT;
        options.limit = PAGE_LIMIT;
      }
      const attributes = [
        'id',
        'userId',
        'loanStatus',
        'netApprovedAmount',
        'isNotBalanced',
        'loanCompletionDate',
        'loan_disbursement_date',
      ];
      const loanData = await this.loanRepo.getTableWhereDataWithCounts(
        attributes,
        options,
      );
      if (loanData === k500Error) return kInternalError;

      const prepareList = [];
      for (let index = 0; index < loanData.rows.length; index++) {
        try {
          const ele = loanData.rows[index];
          const disData = ele.disbursementData ?? [];
          let status = 'Close';
          if (ele?.loanStatus === 'Active') status = 'Open';
          else if (ele?.loanStatus === 'Complete' && ele?.isNotBalanced === 1)
            status = 'Closed but not balanced';

          for (const item of disData) {
            const temp = {
              userId: ele?.userId,
              'Loan ID': ele?.id,
              'User name': item?.name,
              'Phone number': item?.contact,
              'Bank name': item?.bank_name,
              IFSC: item?.ifsc,
              'Account number': item?.account_number,
              'Approved amount': +(+ele?.netApprovedAmount).toFixed(),
              'Disbursed amount': +((item?.amount ?? 0) / 100).toFixed(),
              UTR: item?.utr,
              'Disbursed date': ele?.loan_disbursement_date
                ? this.typeService.getDateFormatted(ele?.loan_disbursement_date)
                : '-',
              'Closed date': ele?.loanCompletionDate
                ? this.typeService.getDateFormatted(ele?.loanCompletionDate)
                : '-',
              'Loan status': status,
            };
            prepareList.push(temp);
          }
        } catch (error) {}
      }
      return { counts: loanData.count, rows: prepareList };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getLedgerLoanDetails(reqData) {
    const loanId = reqData?.loanId;
    if (!loanId) return kParamMissing('loanId');
    const forceLedgerPDF = reqData?.forceLedger ?? false;
    const isPdf = reqData?.pdfDownload ?? false;
    const inApp = reqData?.inApp ?? false;
    const loanData = await this.ledgerLoanData(loanId);
    if (inApp) loanData.inApp = inApp;
    const data = await this.prePareLedgerLoanData(
      loanData,
      isPdf,
      forceLedgerPDF,
    );
    return data;
  }

  // Ledger report -> Total steps -> #04
  private async prePareLedgerLoanData(loanData, isPdf, forceLedgerPDF = false) {
    const processingFees =
      loanData?.loanFees -
      (loanData?.charges?.insurance_fee || 0) -
      (loanData?.charges?.doc_charge_amt || 0) -
      (loanData?.charges?.gst_amt || 0);

    const nbfcLogo = EnvConfig.url.nbfcLogo;
    const rbiRegisterationNo = EnvConfig.nbfc.nbfcRegistrationNumber;
    const cinNo = EnvConfig.nbfc.nbfcCINNumber;
    const nbfcAddress = EnvConfig.nbfc.nbfcAddress;
    const helpContact = EnvConfig.number.helpContact;
    const infoEmail = EnvConfig.mail.suppportMail;
    const inApp = loanData?.inApp;
    // #01 -> Basic details
    const basicDetails: any = await this.prepareBasicDetails(
      loanData,
      processingFees,
    );

    // #02 -> Disbursement details
    let { disbursementCharges, balance } =
      await this.prepareDisbursementDetails(loanData);

    // #03 -> Repayment details
    let {
      sortedList,
      overdueAmount,
      totalRemaining,
      overdueCharge,
      faultLedger,
      emiAmount,
      overdue,
      hasIGST,
      cgst,
      sgst,
      igst,
      firstEMIDate,
      lastEMIDate,
      oneEmiAmount,
      loanDueStatus,
      refund,
      hasEqualPI,
    } = await this.prepareRepaymentDetails(loanData, balance);
    const closingBalanceItem = sortedList.find((item) => item.Date === 'Total');
    // const addCurrencySymbol = (arr) => {
    //   return arr.map((element) => {
    //     if (element?.Amount?.length) {
    //       const isReceived = element.isReceived ?? false;
    //       const symbol = isReceived ? '-₹' : '₹';
    //       element.Amount = element.Amount.map((amount) => `${symbol}${amount}`);
    //     }
    //     return element;
    //   });
    // };
    basicDetails.emiAmount = emiAmount;
    basicDetails.overdue = overdue;
    basicDetails.loanDueStatus = loanDueStatus;
    // let newArr = addCurrencySymbol([...disbursementCharges]);
    // sortedList = addCurrencySymbol(sortedList);

    const today = this.typeService.getGlobalDate(new Date());
    let generatedDate = this.dateService.dateToReadableFormat(today);
    const data = {
      basicDetails,
      startDate: this.typeService.getDateFormatted(
        loanData?.loan_disbursement_date,
      ),
      endDate: this.typeService.getDateFormated(
        loanData?.loanCompletionDate ? loanData?.loanCompletionDate : today,
      ),
      disDetails: disbursementCharges,
      repayDetails: sortedList,
      nbfcLogo,
      rbiRegisterationNo,
      cinNo,
      nbfcAddress,
      helpContact,
      infoEmail,
      generatedDate: generatedDate.readableStr,
      overdueAmount,
      totalRemaining,
      overdueCharge,
      hasIGST,
      cgst,
      sgst,
      igst,
      gstTotal: (+cgst + +sgst + +igst).toFixed(2),
      firstEMIDate,
      lastEMIDate,
      faultLedger,
      oneEmiAmount,
      emiCount: loanData?.emiData.length,
      foreclosureCharge: GLOBAL_CHARGES.FORECLOSURE_PERC,
      hasEqualPI,
    };

    const loanStatus = loanData?.loanStatus;
    if (isPdf) {
      const balance = +closingBalanceItem?.totalAmount || 0;
      const refundAmount = -1 * +refund?.amount || 0;

      if (
        !forceLedgerPDF &&
        loanStatus == 'Complete' &&
        (balance > 10 || balance < 0) &&
        !refund
      )
        return '';
      if (!forceLedgerPDF && refund && refundAmount && refundAmount !== balance)
        return '';

      if (!forceLedgerPDF && balance < 0 && Math.abs(balance) < 10 && !refund)
        return '';

      const page1 = await this.fileService.hbsHandlebars(
        kLedgerStatementPath1,
        { ...data, ledgerFields, ledgerDisbFields },
      );

      if (page1 == k500Error) return page1;
      // const page2 = await this.fileService.hbsHandlebars(
      //   kLedgerStatementPath2,
      //   { ...data, ledgerFields },
      // );
      // if (page2 === k500Error) return kInternalError;
      // const preparedData = [page1, page2];
      const preparedData = [page1];
      //Individual PDF page creation
      let currentPage;
      const nextPages: string[] = [];

      const otherContent = {
        // displayHeaderFooter: true,
        // footerTemplate: `<table
        //       style='
        //         max-width: 800px;
        //         margin: 0 auto;
        //         width: 100%;
        //         font-family: "Manrope";
        //         bottom: 0;
        //         position: fixed;
        //         z-index: 1;
        //       '
        //     >
        //       <tr>
        //         <td
        //           style='
        //             color:rgb(239, 13, 39);
        //             width: 100%;
        //             padding: 10px 0;
        //             background-color:rgb(125, 35, 35);
        //             text-align: center;
        //             font-size: 12px;
        //           '
        //         >
        //           Contact No:
        //           {{helpContact}}
        //           <span style='padding: 0 5px'>/</span>
        //           Email:
        //           {{infoEmail}}
        //         </td>
        //       </tr>
        //       <tr>
        //         <td
        //           style='
        //             color:rgb(219, 11, 11);
        //             width: 100%;
        //             padding: 10px 0;
        //             background-color:rgb(3, 2, 2);
        //             text-align: center;
        //             font-size: 12px;
        //           '
        //         >
        //           {{nbfcAddress}}
        //         </td>
        //       </tr>
        //     </table>
        // `,
      };

      for (let index = 0; index < preparedData.length; index++) {
        const pageData = preparedData[index];
        const pdfPath = await this.fileService.dataToPDF(pageData);
        if (pdfPath == k500Error) return kInternalError;
        if (index == 0) currentPage = pdfPath.toString();
        else nextPages.push(pdfPath.toString());
      }
      const ledgerPath = await this.fileService.mergeMultiplePDF(
        nextPages,
        currentPage,
      );
      if (ledgerPath == k500Error) return kInternalError;
      const pdfUrl = await this.fileService.uploadFile(
        ledgerPath,
        CLOUD_FOLDER_PATH.tally,
      );
      await this.fileService.removeFiles([...nextPages, currentPage]);
      if (!pdfUrl || pdfUrl === k500Error) return '';
      return pdfUrl;
    }
    return data;
  }

  transformOldWaiverData(oldData) {
    return oldData.map((entry) => {
      const parsed = JSON.parse(entry?.respons);

      const emiId = parsed?.emiId ?? null;
      const waiverEmiAmount = parsed?.waiver_emiAmount ?? 0;
      const waiverPenalty = parsed?.waiver_penalty ?? 0;
      const waiverPenal = parsed?.waiver_penal ?? 0;
      const waiverLegal = parsed?.waiver_legal ?? 0;
      const waiverBounce = parsed?.waiver_bounce ?? 0;
      const waiverRegInt = parsed?.waiver_regIntAmount ?? 0;

      return {
        waiverBifurcation: {
          emiId,
          penalty: waiverPenalty,
          emiAmount: waiverEmiAmount,
          legalCharge: waiverLegal,
          penalCharge: waiverPenal,
          bounceCharge: waiverBounce,
          deferredInterest: waiverRegInt, //need to check for deferred interest
        },
        waiverAmount: this.typeService.manageAmount(
          waiverPenalty +
            waiverEmiAmount +
            waiverLegal +
            waiverPenal +
            waiverBounce +
            waiverRegInt,
        ),
        loanId: entry?.loanId,
        userId: entry?.userId,
        emiId,
        createdAt: new Date(entry?.date),
      };
    });
  }

  // Basic details for ledger -> Total steps -> #01
  private async prepareBasicDetails(loanData, processingFees) {
    const {
      id,
      purposeId,
      charges,
      stampFees,
      insuranceDetails,
      disbursementData,
      registeredUsers,
      interestRate,
      loan_disbursement_date,
      loanCompletionDate,
      netApprovedAmount,
    } = loanData;

    const purposeData: any = await this.commonSharedService.getLoanPurpose(
      purposeId,
    );
    const loanCharges = +(
      stampFees +
      processingFees +
      (charges?.insurance_fee || 0) +
      (charges?.doc_charge_amt || 0) +
      charges?.gst_amt +
      (insuranceDetails?.totalPremium || 0)
    );

    const kycData = await this.commonSharedService.getKycData({
      userId: loanData?.userId,
      callFnFrom: '2',
    });
    let pinCode = '-';
    try {
      const response = JSON.parse(kycData?.aadhaarResponse);
      pinCode = response.zip ?? response.pincode ?? '-';
    } catch (error) {}

    const userAddress = this.typeService.getAadhaarAddress(kycData);

    // let loanDueStatus = registeredUsers?.loanStatus ?? '-';
    // if (loanDueStatus == 1 || loanDueStatus == 0) loanDueStatus = 'On Time';
    // else if (loanDueStatus == 2) loanDueStatus = 'Delayed';
    // else if (loanDueStatus == 3) loanDueStatus = 'Delayed';
    // else if (loanDueStatus == 3) loanDueStatus = 'Defaulter';

    const interestRatePerAnnum = (+interestRate * 365).toFixed(2) + '%';
    const data = {
      id,
      nbfcName: NBFC_NAME,
      nbfcAddress: NBFC_ADDRESS,
      helpContact: kHelpContact,
      nbfcEmail: kSupportMail,
      name: loanData?.fullName ?? '-',
      phone: this.cryptService.decryptPhone(loanData?.phone) ?? '-',
      email: loanData?.email ?? '-',
      productDescription: PRODUCT_DESCRIPTION,
      subProduct: purposeData?.purposeName ?? '-',
      loanAmount: netApprovedAmount
        ? this.typeService.amountNumberWithCommas(
            (+netApprovedAmount).toFixed(2),
          )
        : '-',

      disbursedAmount: disbursementData[0]?.amount
        ? this.typeService.amountNumberWithCommas(
            (disbursementData[0]?.amount / 100).toFixed(2),
          )
        : '-',
      loanCharges:
        this.typeService.amountNumberWithCommas((+loanCharges).toFixed(2)) ?? 0,
      tenure: loanData?.approvedDuration,
      interestRate: interestRatePerAnnum,
      accountOpen: loan_disbursement_date
        ? this.typeService.getDateFormatted(loan_disbursement_date)
        : '-',
      accountClose: loanCompletionDate
        ? this.typeService.getDateFormatted(loanCompletionDate)
        : '-',
      address: userAddress?.address ?? '-',
      city: userAddress?.dist ?? '-',
      pinCode,
    };
    return data;
  }

  // Disbursement for ledger -> Total steps -> #11
  private async prepareDisbursementDetails(loanData) {
    // Fallback -> GST is available but CGST & SGST is missing in Database
    const charges = loanData?.charges ?? {};
    if (charges?.gst_amt && !charges?.cgst_amt) {
      const cgst_amt = parseFloat((charges?.gst_amt / 2).toFixed(2));
      loanData.charges.cgst_amt = cgst_amt;
      loanData.charges.sgst_amt = cgst_amt;
    }

    let balance = loanData?.netApprovedAmount;
    let totalDeduction = 0;
    const disbursementCharges = [];
    const txnDate = loanData?.loan_disbursement_date;
    const txnDateInfo = this.dateService.dateToReadableFormat(txnDate);
    const txnDateStr = txnDateInfo.readableStr;
    const disbursementUtr = loanData?.disbursementData[0]?.utr;

    // #01 -> Approved loan amount

    // #02 -> Stamp duty
    const stampCharges = loanData?.stampFees ?? 0;
    totalDeduction += stampCharges;

    // #03 -> Processing fees
    const processingFees = parseFloat(
      ((+loanData.netApprovedAmount * loanData?.processingFees) / 100).toFixed(
        2,
      ),
    );
    totalDeduction += processingFees;

    // #04 -> Document charges
    const docCharges = loanData?.charges?.doc_charge_amt ?? 0;
    totalDeduction += docCharges;

    // #05 -> Online convenience charges
    const onlineConvenienceCharges = loanData?.charges?.insurance_fee ?? 0;
    totalDeduction += onlineConvenienceCharges;

    // #06 -> Risk assessment charges
    const riskAssessmentCharge = loanData?.charges?.risk_assessment_charge ?? 0;
    totalDeduction += riskAssessmentCharge;

    // #07 -> CGST charges
    const cGSTCharges = loanData?.charges?.cgst_amt ?? 0;
    totalDeduction += cGSTCharges;

    // #08 -> SGST charges
    const sGSTCharges = loanData?.charges?.sgst_amt ?? 0;
    const iGSTCharges = loanData?.charges?.igst_amt ?? 0;
    totalDeduction += sGSTCharges;

    // #09 -> Insurance premium charges
    const insuranceCharges = loanData?.insuranceDetails?.totalPremium ?? 0;
    totalDeduction += insuranceCharges;

    // #10 -> Disbursed amount
    const disbursementAmount = +loanData.netApprovedAmount - totalDeduction;

    let disbursementDetails = {
      'Loan Amount': '',
      'Processing Charges @5%': processingFees.toFixed(2),
      'Document Charges @1%': docCharges.toFixed(2),
      'Online Convenience Charges': onlineConvenienceCharges.toFixed(2),
      'Stamp Duty': stampCharges.toFixed(2),
      'CGST(9%)': '0',
      'SGST(9%)': '0',
      'IGST(18%)': '0',
      'Insurance Charges': insuranceCharges.toFixed(2),
      [`Disbursement Amount to Borrower (UTR: ${disbursementUtr})`]:
        (+disbursementAmount).toFixed(2),
    };

    if ((cGSTCharges > 0 || sGSTCharges > 0) && iGSTCharges == 0) {
      disbursementDetails['CGST(9%)'] = cGSTCharges.toFixed(2);
      disbursementDetails['SGST(9%)'] = sGSTCharges.toFixed(2);
    } else disbursementDetails['IGST(18%)'] = iGSTCharges.toFixed(2);

    if (+disbursementDetails?.['CGST(9%)'] == 0)
      delete disbursementDetails['CGST(9%)'];
    if (+disbursementDetails?.['SGST(9%)'] == 0)
      delete disbursementDetails['SGST(9%)'];
    if (+disbursementDetails?.['IGST(18%)'] == 0)
      delete disbursementDetails['IGST(18%)'];

    for (const key in disbursementDetails) {
      const value = disbursementDetails[key];
      if (value === '0.00' || value === 0 || value === '0' || value == null)
        delete disbursementDetails[key];
    }
    disbursementCharges.push({
      Date: txnDateStr,
      Particulars: Object.keys(disbursementDetails),
      Debit: Object.values(disbursementDetails),
      Credit: [
        `${balance < 0 ? '-' : ''}${this.typeService.amountNumberWithCommas(
          (+loanData?.netApprovedAmount).toFixed(2),
        )}`,
      ],
    });

    disbursementCharges.push({
      Date: 'Total',
      Particulars: [],
      Debit: [
        (balance < 0 ? '-' : '') +
          this.typeService.amountNumberWithCommas(
            (+loanData?.netApprovedAmount).toFixed(2),
          ),
      ],
      Credit: [
        (balance < 0 ? '-' : '') +
          this.typeService.amountNumberWithCommas(
            (+loanData?.netApprovedAmount).toFixed(2),
          ),
      ],
    });

    return { disbursementCharges, balance };
  }

  // Repayment for ledger -> Total steps -> #03
  private async prepareRepaymentDetails(loanData: loanTransaction, balance) {
    const loanId = loanData?.id;
    const adminId = SYSTEM_ADMIN_ID;
    let faultLedger = false;
    let emiAmount = 0;
    let overdue = 0;

    let totalDebit = 0;
    let totalCredit = 0;
    let firstEMIDate = '';
    let lastEMIDate = '';
    let loanDueStatus = '';

    const { totalRemaining } = await this.loanService.getEMIDetails({
      loanId: loanData?.id,
    });

    const result = await this.refundService.getRefundablesData(
      null,
      null,
      adminId,
      loanId,
    );
    const refund = result?.filteredData[0];
    const lastADTrans = result?.lastADTrans;
    let remainingRefunds = [];

    if (refund?.loanId) {
      faultLedger = true;

      // Validation -> Applicable refund amount and transaction
      const tranData = await this.refundService.getTransactionDataFroRfund(
        refund.loanId,
      );

      // Calculation -> Refund amount
      let prePareData = this.refundService.prePareRefundAmount(
        refund,
        tranData,
        adminId,
        lastADTrans,
      );

      if (prePareData.message) prePareData = [];

      // Filter out refunds where refundTran is null
      // const refundData = prePareData.filter((item) => item.refundTran === null);

      remainingRefunds = prePareData
        ? prePareData?.map((item) => item.rawData)
        : [];
    }

    const intialBalance = 0;
    const emiList = loanData.emiData.sort((a, b) => a.id - b.id) ?? [];

    const oneEmiAmount =
      emiList[0]?.principalCovered + emiList[0]?.interestCalculate;

    const transList = (loanData.transactionData ?? []).sort(
      (a, b) => a.id - b.id,
    );

    const hasReverseSettlement = transList.some(
      (transData) => transData?.subStatus === 'REVERSE_SETTLEMENT',
    );

    const refundData = this.findRefundAndFallbackMatches([
      ...transList,
      ...remainingRefunds,
    ]);
    let fullPayEmis = '';
    let today = this.typeService.getGlobalDate(new Date());
    const targetList = [];
    let emiNumbers = {};
    const isIGst = loanData?.penaltyCharges?.I_GST ?? false;
    balance = 0;

    const todayTime = today.getTime();
    const data = {
      remainingInterest: 0,
      remainingPrincipal: 0,
      remainingPenalty: 0,
      overdueInterestAmount: 0,
      overduePrincipalAmount: 0,
      forClosureAmount: 0,
      sgstForClosureCharge: 0,
      cgstForClosureCharge: 0,
      igstForClosureCharge: 0,
      regInterestAmount: 0,
      bounceCharge: 0,
      sgstBounceCharge: 0,
      cgstBounceCharge: 0,
      igstBounceCharge: 0,
      penalty_days: 0,
      legalCharges: 0,
      sgstLegalCharges: 0,
      cgstLegalCharges: 0,
      igstLegalCharges: 0,
      cgstPenalCharges: 0,
      sgstPenalCharges: 0,
      igstPenalCharges: 0,
      overdueCharge: 0,
      penalCharge: 0,
      overdueAmount: 0,
    };
    let delayDays = 0;
    let hasEqualPI = true;
    let previousPrincipal = null;

    const waiverData = this.prepareWaiverData(loanData?.waiverData ?? []);

    // #01 -> Prepare EMIs
    for (let index = 0; index < emiList.length; index++) {
      const emiData = emiList[index];
      delayDays =
        emiData?.penalty_days > delayDays ? emiData?.penalty_days : delayDays;
      if (emiData?.emiNumber == 1)
        firstEMIDate = this.dateService.dateToReadableFormat(
          new Date(emiData?.emi_date),
        ).readableStr;
      if (emiData?.partOfemi == 'LAST')
        lastEMIDate = this.dateService.dateToReadableFormat(
          new Date(emiData?.emi_date),
        ).readableStr;
      const emiDate = this.typeService.getGlobalDate(
        new Date(emiData.emi_date),
      );
      const emiTime = emiDate.getTime();

      const isPaid = emiData.payment_status === '1';

      const isPastEMI = todayTime > emiTime;
      if (isPastEMI && !isPaid) {
        overdue++;

        // Call the helper function
        const { overdueAmount, overdueCharge } = this.calculateOverdue(
          emiData,
          transList,
        );

        // Update data object
        data.overdueAmount += this.typeService.manageAmount(overdueAmount);
        data.overdueCharge += this.typeService.manageAmount(overdueCharge);
      }
      emiNumbers[emiData?.id] = emiData?.emiNumber;
      const paidDate = isPaid
        ? this.typeService.getGlobalDate(new Date(emiData.payment_done_date))
        : null;

      let interest = emiData.interestCalculate ?? 0;
      let interestToShow = emiData.interestCalculate ?? 0;

      // Add Principal Logic
      let principal = emiData.principalCovered ?? 0;

      let principalToShow = emiData.principalCovered ?? 0;
      emiAmount +=
        (emiData?.principalCovered ?? 0) + (emiData?.interestCalculate ?? 0);
      //#region // EMI DUE OBJECT
      const payType = emiData.pay_type;
      const isFullPay = payType === kFullPay;
      if (isFullPay)
        fullPayEmis = fullPayEmis
          ? fullPayEmis + ' & ' + emiData.emiNumber
          : fullPayEmis + '' + emiData.emiNumber;
      if (isFullPay && emiData?.payment_due_status == '0') {
        interest = emiData.fullPayInterest ?? 0;
        interestToShow = emiData.fullPayInterest ?? 0;
        principal = emiData.principalCovered ?? 0;
        principalToShow = emiData.principalCovered ?? 0;
      }

      if (previousPrincipal !== null) {
        if (Math.abs(principal - previousPrincipal) > 50) {
          hasEqualPI = false;
        }
      }
      previousPrincipal = principal;

      let txnDate;
      if (paidDate && paidDate.getTime() < emiDate.getTime())
        txnDate = paidDate.toJSON();
      else txnDate = emiDate.toJSON();
      if (index == 0) interest += balance;

      // Add data
      const txnDateInfo = this.dateService.dateToReadableFormat(txnDate);
      balance += interest + principal;
      let isPaidForThisEmi = transList.filter(
        (t) => t.emiId == emiData.id && t.type != kFullPay,
      );
      let isEmiPassed =
        emiData?.payment_due_status == '1' ||
        this.typeService.getGlobalDate(new Date()).getTime() ==
          this.typeService.getGlobalDate(emiDate).getTime();

      // Object for Due Interest
      if (
        (interest > 0 || principal > 0) &&
        (isEmiPassed || isPaidForThisEmi.length > 0 || isFullPay)
      ) {
        let emiDetail: any = {};

        if (principal > 0) emiDetail['Principal'] = principalToShow;
        if (interest > 0) emiDetail['Interest'] = interestToShow;
        totalDebit += interest + principal;
        targetList.push({
          Title: `EMI-${emiData.emiNumber} Due`,
          Date: txnDateInfo.readableStr,
          Particulars: '',
          Credit: [],
          Debit: [
            this.typeService.amountNumberWithCommas(
              (interest + principal).toFixed(2),
            ),
          ],
          isReceived: false,
          totalAmount: interest + principal,
          Balance:
            (interest + principal < 0 ? '-' : '') +
            this.typeService.amountNumberWithCommas(
              (interest + principal).toFixed(2),
            ),
        });
      }
      // balance += totalDueCharges;
      balance = parseFloat(balance.toFixed(2));

      if (paidDate) {
        // Apply manageAmount to each waiver component
        let waived_interest = this.typeService.manageAmount(
          emiData?.waived_interest || 0,
        );
        let waived_principal = this.typeService.manageAmount(
          emiData?.waived_principal || 0,
        );

        let deferredInt = this.typeService.manageAmount(
          emiData?.waived_regInterest || 0,
        );

        let bounceCharge = this.typeService.manageAmount(
          emiData?.waived_bounce || 0,
        );

        let penalCharge = this.typeService.manageAmount(
          emiData?.waived_penal || 0,
        );
        let penaltyCharge = this.typeService.manageAmount(
          +(emiData?.waived_penalty || 0),
        );

        let legalCharge = this.typeService.manageAmount(
          emiData?.waived_legal || 0,
        );

        let forecloseCharge = this.typeService.manageAmount(
          emiData?.waived_foreclose || 0,
        );

        let totalWaiverGiven = this.typeService.manageAmount(
          waived_principal +
            deferredInt +
            bounceCharge +
            penalCharge +
            legalCharge +
            waived_interest +
            forecloseCharge +
            penaltyCharge,
        );
        let totalWaiverGivenWithoutPI = this.typeService.manageAmount(
          (deferredInt ?? 0) +
            (bounceCharge ?? 0) +
            (penalCharge ?? 0) +
            (legalCharge ?? 0) +
            (penaltyCharge ?? 0) +
            (forecloseCharge ?? 0) +
            (hasReverseSettlement ? waived_interest ?? 0 : 0),
        );

        balance = this.typeService.manageAmount(
          balance - totalWaiverGivenWithoutPI,
        );
        if (totalWaiverGivenWithoutPI > 0) {
          // Check for existing entry with the same Date and Title
          const existingIndex = targetList.findIndex(
            (entry) =>
              entry.Date ===
                this.dateService.dateToReadableFormat(paidDate).readableStr &&
              entry.Title === `EMI-${emiNumbers[emiData?.id]} Due Charges`,
          );
          if (totalWaiverGiven > 0) {
            totalDebit += totalWaiverGivenWithoutPI;

            if (existingIndex !== -1) {
              // Update the existing entry
              const existingEntry = targetList[existingIndex];

              // Update Debit if present
              const existingDebit = existingEntry.Debit[0]
                ? parseFloat(existingEntry.Debit[0].replace(/[^0-9.-]+/g, ''))
                : 0;

              const updatedDebit = existingDebit + totalWaiverGivenWithoutPI;

              // Update Debit
              existingEntry.Debit[0] =
                '₹' +
                this.typeService.amountNumberWithCommas(
                  updatedDebit.toFixed(2),
                );

              // Update totalAmount
              existingEntry.totalAmount += totalWaiverGiven;

              // Update Balance
              existingEntry.Balance =
                (balance < 0 ? '-' : '') +
                '₹' +
                this.typeService.amountNumberWithCommas(
                  Math.abs(balance).toFixed(2),
                );
            } else {
              // Add a new entry
              targetList.push({
                Title: `EMI-${emiNumbers[emiData?.id]} Due Charges`,
                Date: this.dateService.dateToReadableFormat(paidDate)
                  .readableStr,
                Particulars: [],
                Debit: [
                  this.typeService.amountNumberWithCommas(
                    totalWaiverGivenWithoutPI.toFixed(2),
                  ),
                ],
                Credit: [],
                isReceived: false,
                totalAmount: totalWaiverGivenWithoutPI,
                Balance:
                  (balance < 0 ? '-' : '') +
                  this.typeService.amountNumberWithCommas(
                    Math.abs(balance).toFixed(2),
                  ),
              });
            }
            totalCredit += totalWaiverGiven;
            targetList.push({
              Title: `EMI-${emiNumbers[emiData?.id]} Waiver`,
              Date: this.dateService.dateToReadableFormat(paidDate).readableStr,
              Particulars: [],
              Debit: [],
              Credit: [
                (totalWaiverGiven < 0 ? '-' : '') +
                  this.typeService.amountNumberWithCommas(
                    totalWaiverGiven.toFixed(2),
                  ),
              ],
              isReceived: true,
              totalAmount: totalWaiverGiven,
              Balance:
                (balance < 0 ? '-' : '') +
                this.typeService.amountNumberWithCommas(
                  Math.abs(balance).toFixed(2),
                ),
            });
          }
        }
      } else if (waiverData.length > 0 && !paidDate) {
        const waiverForEmi = waiverData.filter(
          (waiver) => waiver?.emiId === emiData?.id,
        );

        for (const waiver of waiverForEmi) {
          const waiverDate = new Date(waiver.date);

          let waived_interest = this.typeService.manageAmount(
            waiver?.waived_interest || 0,
          );
          let waived_principal = this.typeService.manageAmount(
            waiver?.waived_principal || 0,
          );
          let deferredInt = this.typeService.manageAmount(
            waiver?.waived_regInterest || 0,
          );
          let bounceCharge = this.typeService.manageAmount(
            waiver?.waived_bounce || 0,
          );
          let penalCharge = this.typeService.manageAmount(
            waiver?.waived_penal || 0,
          );
          let penaltyCharge = this.typeService.manageAmount(
            waiver?.waived_penalty || 0,
          );
          let legalCharge = this.typeService.manageAmount(
            waiver?.waived_legal || 0,
          );
          let forecloseCharge = this.typeService.manageAmount(
            emiData?.waived_foreclose || 0,
          );

          let totalWaiverGiven = this.typeService.manageAmount(
            waived_principal +
              deferredInt +
              bounceCharge +
              penalCharge +
              legalCharge +
              waived_interest +
              penaltyCharge +
              forecloseCharge,
          );

          let totalWaiverGivenWithoutPI = this.typeService.manageAmount(
            (deferredInt ?? 0) +
              (bounceCharge ?? 0) +
              (penalCharge ?? 0) +
              (legalCharge ?? 0) +
              (penaltyCharge ?? 0) +
              (forecloseCharge ?? 0) +
              (hasReverseSettlement ? waived_interest ?? 0 : 0),
          );

          balance = this.typeService.manageAmount(
            balance - totalWaiverGivenWithoutPI,
          );

          if (totalWaiverGivenWithoutPI > 0) {
            const readableDate =
              this.dateService.dateToReadableFormat(waiverDate).readableStr;
            const title = `EMI-${emiNumbers[emiData?.id]} Due Charges`;

            const existingIndex = targetList.findIndex(
              (entry) => entry.Date === readableDate && entry.Title === title,
            );

            if (totalWaiverGiven > 0) {
              totalDebit += totalWaiverGivenWithoutPI;

              if (existingIndex !== -1) {
                const existingEntry = targetList[existingIndex];
                const existingDebit = existingEntry.Debit[0]
                  ? parseFloat(existingEntry.Debit[0].replace(/[^0-9.-]+/g, ''))
                  : 0;

                const updatedDebit = existingDebit + totalWaiverGivenWithoutPI;

                existingEntry.Debit[0] =
                  '₹' +
                  this.typeService.amountNumberWithCommas(
                    updatedDebit.toFixed(2),
                  );
                existingEntry.totalAmount += totalWaiverGiven;
                existingEntry.Balance =
                  (balance < 0 ? '-' : '') +
                  '₹' +
                  this.typeService.amountNumberWithCommas(
                    Math.abs(balance).toFixed(2),
                  );
              } else {
                targetList.push({
                  Title: title,
                  Date: readableDate,
                  Particulars: [],
                  Debit: [
                    this.typeService.amountNumberWithCommas(
                      totalWaiverGivenWithoutPI.toFixed(2),
                    ),
                  ],
                  Credit: [],
                  isReceived: false,
                  totalAmount: totalWaiverGivenWithoutPI,
                  Balance:
                    (balance < 0 ? '-' : '') +
                    this.typeService.amountNumberWithCommas(
                      Math.abs(balance).toFixed(2),
                    ),
                });
              }

              totalCredit += totalWaiverGiven;

              const waiverTitle = `EMI-${emiNumbers[emiData?.id]} Waiver`;
              const waiverDateStr =
                this.dateService.dateToReadableFormat(waiverDate).readableStr;

              const existingWaiverIndex = targetList.findIndex(
                (entry) =>
                  entry.Date === waiverDateStr && entry.Title === waiverTitle,
              );

              if (existingWaiverIndex !== -1) {
                const existingWaiverEntry = targetList[existingWaiverIndex];

                const existingCredit = existingWaiverEntry.Credit[0]
                  ? parseFloat(
                      existingWaiverEntry.Credit[0].replace(/[^0-9.-]+/g, ''),
                    )
                  : 0;

                const updatedCredit = existingCredit + totalWaiverGiven;

                existingWaiverEntry.Credit[0] =
                  '₹' +
                  this.typeService.amountNumberWithCommas(
                    updatedCredit.toFixed(2),
                  );

                existingWaiverEntry.totalAmount += totalWaiverGiven;

                existingWaiverEntry.Balance =
                  (balance < 0 ? '-' : '') +
                  '₹' +
                  this.typeService.amountNumberWithCommas(
                    Math.abs(balance).toFixed(2),
                  );
              } else {
                // No existing entry — push new
                targetList.push({
                  Title: waiverTitle,
                  Date: waiverDateStr,
                  Particulars: [],
                  Debit: [],
                  Credit: [
                    (totalWaiverGiven < 0 ? '-' : '') +
                      this.typeService.amountNumberWithCommas(
                        totalWaiverGiven.toFixed(2),
                      ),
                  ],
                  isReceived: true,
                  totalAmount: totalWaiverGiven,
                  Balance:
                    (balance < 0 ? '-' : '') +
                    this.typeService.amountNumberWithCommas(
                      Math.abs(balance).toFixed(2),
                    ),
                });
              }
            }
          }
        }
      }
    }
    const usedRefundUTRs = new Set<string>();
    // #03 -> Prepare repayments
    let igst = 0;
    let cgst = 0;
    let sgst = 0;
    let hasIGST = false;
    for (let i = 0; i < transList.length; i++) {
      const transData = transList[i];
      if (!transData) continue;

      if (
        transData?.subStatus === 'REVERSE_SETTLEMENT' &&
        transData?.paidAmount < 0
      )
        continue;

      let principal = transData?.principalAmount ?? 0;
      let interest = transData?.interestAmount ?? 0;
      let penalty = transData?.penaltyAmount ?? 0;
      let deferredInt = transData?.regInterestAmount ?? 0;
      let bounceCharge = transData?.bounceCharge ?? 0;
      let penalCharge = transData?.penalCharge ?? 0;
      let legalCharge = transData?.legalCharge ?? 0;
      let foreclosureCharges = transData?.forClosureAmount ?? 0;

      let gst =
        (transData?.sgstOnBounceCharge ?? 0) +
        (transData?.cgstOnBounceCharge ?? 0) +
        (transData?.igstOnBounceCharge ?? 0) +
        (transData?.cgstOnPenalCharge ?? 0) +
        (transData?.sgstOnPenalCharge ?? 0) +
        (transData?.igstOnPenalCharge ?? 0) +
        (transData?.cgstOnLegalCharge ?? 0) +
        (transData?.sgstOnLegalCharge ?? 0) +
        (transData?.igstOnLegalCharge ?? 0) +
        (transData?.cgstForClosureCharge ?? 0) +
        (transData?.sgstForClosureCharge ?? 0) +
        (transData?.igstForClosureCharge ?? 0);
      let amount =
        principal +
        interest +
        deferredInt +
        bounceCharge +
        penalty +
        penalCharge +
        legalCharge +
        foreclosureCharges +
        gst;

      const pIAmount = principal + interest;
      let paymentObject: any = {};
      if (pIAmount > 0) paymentObject['EMI Amount'] = pIAmount.toFixed(2);
      if (deferredInt > 0)
        paymentObject['Deferred Interest'] = deferredInt.toFixed(2);
      if (bounceCharge > 0)
        paymentObject['Bounce Charge'] = bounceCharge.toFixed(2);
      if (penalCharge > 0)
        paymentObject['Penal Charge'] = penalCharge.toFixed(2);
      if (penalty > 0) paymentObject['Penalty'] = penalty.toFixed(2);
      if (legalCharge > 0)
        paymentObject['Legal Charge'] = legalCharge.toFixed(2);
      if (foreclosureCharges > 0)
        paymentObject['Forclosure Charge'] = foreclosureCharges.toFixed(2);

      if (transData?.type === kRefund) {
        paymentObject = {};
      }
      if (gst > 0 && transData?.type !== kRefund) {
        if (isIGst) {
          paymentObject['IGST(18%)'] = +gst.toFixed(2);
          hasIGST = true;
          igst += +gst.toFixed(2);
        } else {
          paymentObject['CGST(9%)'] = (+gst / 2).toFixed(2);
          paymentObject['SGST(9%)'] = (+gst / 2).toFixed(2);
          cgst += +(gst / 2).toFixed(2);
          sgst += +(gst / 2).toFixed(2);
        }
      }
      let paidAmount =
        transData.type === kRefund ? transData.paidAmount : amount;
      const paidDate = this.typeService.getGlobalDate(
        new Date(transData.completionDate),
      );
      const paidDateInfo = this.dateService.dateToReadableFormat(paidDate);
      balance -= paidAmount;
      balance = parseFloat(balance.toFixed(2));
      let UTR = transData?.utr.split('DMY')[0];

      // Handling Due Charges
      if (transData?.type !== kRefund) {
        let title = `EMI-${emiNumbers[transData?.emiId]} Due Charges`;
        if (transData?.type === kFullPay) {
          title = `EMI-${fullPayEmis} Due Charges`;
        }
        const hasMatchingRefund = transList.some((t) => {
          if (
            t.type === kRefund &&
            t.paidAmount === -1 * transData.paidAmount &&
            t.utr &&
            !usedRefundUTRs.has(t.utr) &&
            t.principalAmount == -1 * (transData.principalAmount ?? 0) &&
            t.interestAmount == -1 * (transData.interestAmount ?? 0) &&
            t.penaltyAmount == -1 * (transData.penaltyAmount ?? 0) &&
            t.regInterestAmount == -1 * (transData.regInterestAmount ?? 0) &&
            t.bounceCharge == -1 * (transData.bounceCharge ?? 0) &&
            t.penalCharge == -1 * (transData.penalCharge ?? 0) &&
            t.legalCharge == -1 * (transData.legalCharge ?? 0) &&
            t.forClosureAmount == -1 * (transData.forClosureAmount ?? 0) &&
            t.sgstOnBounceCharge == -1 * (transData.sgstOnBounceCharge ?? 0) &&
            t.cgstOnBounceCharge == -1 * (transData.cgstOnBounceCharge ?? 0) &&
            t.igstOnBounceCharge == -1 * (transData.igstOnBounceCharge ?? 0) &&
            t.cgstOnPenalCharge == -1 * (transData.cgstOnPenalCharge ?? 0) &&
            t.sgstOnPenalCharge == -1 * (transData.sgstOnPenalCharge ?? 0) &&
            t.igstOnPenalCharge == -1 * (transData.igstOnPenalCharge ?? 0) &&
            t.cgstOnLegalCharge == -1 * (transData.cgstOnLegalCharge ?? 0) &&
            t.sgstOnLegalCharge == -1 * (transData.sgstOnLegalCharge ?? 0) &&
            t.igstOnLegalCharge == -1 * (transData.igstOnLegalCharge ?? 0) &&
            t.cgstForClosureCharge ==
              -1 * (transData.cgstForClosureCharge ?? 0) &&
            t.sgstForClosureCharge ==
              -1 * (transData.sgstForClosureCharge ?? 0) &&
            t.igstForClosureCharge == -1 * (transData.igstForClosureCharge ?? 0)
          ) {
            usedRefundUTRs.add(t.utr);
            return true;
          }
          return false;
        });

        // Skip processing if there's a matching refund
        if (!hasMatchingRefund) {
          const matchingRefund = refundData.find(
            (match) => match.match.id === transData.id,
          );

          if (matchingRefund) {
            faultLedger = true;
            // Adjust the totalPaidAmount by subtracting the refund's paidAmount
            transData.paidAmount -= Math.abs(matchingRefund.refund.paidAmount);
          }
          let totalDueAmount =
            transData.paidAmount -
            (transData.principalAmount ?? 0) -
            (transData.interestAmount ?? 0);
          if (totalDueAmount > 0) {
            const existingIndex = targetList.findIndex(
              (entry) =>
                entry.Date === paidDateInfo.readableStr &&
                entry.Title === title,
            );
            totalDebit += totalDueAmount;

            if (existingIndex !== -1) {
              // Update the existing entry
              const existingEntry = targetList[existingIndex];

              // Update Debit if present
              const existingDebit = existingEntry.Debit[0]
                ? parseFloat(existingEntry.Debit[0].replace(/[^0-9.-]+/g, ''))
                : 0;

              const updatedDebit = existingDebit + totalDueAmount;

              // Update Debit
              existingEntry.Debit[0] = this.typeService.amountNumberWithCommas(
                updatedDebit.toFixed(2),
              );

              // Update totalAmount
              existingEntry.totalAmount += totalDueAmount;

              // Update Balance
              existingEntry.Balance =
                (balance < 0 ? '-' : '') +
                this.typeService.amountNumberWithCommas(
                  Math.abs(balance).toFixed(2),
                );
            } else {
              // Add new entry
              targetList.push({
                Title: title,
                Date: paidDateInfo.readableStr,
                Particulars: [],
                Debit: [
                  this.typeService.amountNumberWithCommas(
                    totalDueAmount.toFixed(2),
                  ),
                ],
                Credit: [],
                isReceived: false,
                totalAmount: totalDueAmount,
                Balance:
                  (balance < 0 ? '-' : '') +
                  this.typeService.amountNumberWithCommas(
                    Math.abs(balance).toFixed(2),
                  ),
              });
            }
          }
        }
      }
      // Handling Repayment or Refund
      let title = `EMI-${emiNumbers[transData?.emiId]} Repayment`;
      if (transData?.type === kRefund) {
        faultLedger = true;
        title = `EMI-${emiNumbers[transData?.emiId]} Refund`;
        amount = -1 * transData?.paidAmount;
      }
      totalDebit += transData?.type !== kRefund ? 0 : amount;

      totalCredit += transData?.type !== kRefund ? amount : 0;

      if (transData?.type == kFullPay) title = `EMI-${fullPayEmis} Repayment`;
      const obj = {
        Title: title,
        Date: paidDateInfo.readableStr,
        Particulars: Object.keys(paymentObject),
        Debit:
          transData?.type !== kRefund
            ? ''
            : [this.typeService.amountNumberWithCommas(amount.toFixed(2))],
        Credit:
          transData?.type !== kRefund ? Object.values(paymentObject) : [''],
        isReceived: transData?.type != kRefund ? true : false,
        totalAmount: amount,
        UTR: ` (UTR: ${UTR})`,
        Balance:
          (balance < 0 ? '-' : '') +
          this.typeService.amountNumberWithCommas(Math.abs(balance).toFixed(2)),
      };
      targetList.push(obj);
    }

    // Sort all the records date wise
    balance = 0;
    const sortedList = targetList.sort(
      (a, b) =>
        this.dateService.readableStrToDate(a['Date']).getTime() -
        this.dateService.readableStrToDate(b['Date']).getTime(),
    );
    sortedList.forEach((el) => {
      const isReceived = el.isReceived ?? false;
      if (isReceived) balance -= el.totalAmount;
      else balance += el.totalAmount;
      balance = parseFloat(balance);
      // balance = this.typeService.manageAmount(balance);

      el['Balance'] =
        (balance < 0 ? '-' : '') +
        this.typeService.amountNumberWithCommas(Math.abs(balance).toFixed(2));
    });
    const seenDates = new Set();

    sortedList.forEach((item) => {
      if (seenDates.has(item.Date)) {
        item.Date = '';
      } else {
        seenDates.add(item.Date);
      }
    });

    // If User's EMI are Arrived OR No Payments Are Made
    if (sortedList.length === 0) balance = intialBalance;

    if (balance <= 10 && balance >= -10 && balance !== 0) {
      const roundOffAmount = Math.abs(balance);
      const isNegative = balance < 0;
      (totalDebit += isNegative ? roundOffAmount : 0),
        (totalCredit += !isNegative ? roundOffAmount : 0),
        sortedList.push({
          Date: 'Round Off',
          Particulars: [],
          Debit: isNegative
            ? [
                this.typeService.amountNumberWithCommas(
                  roundOffAmount.toFixed(2),
                ),
              ]
            : [],
          Credit: !isNegative
            ? [
                this.typeService.amountNumberWithCommas(
                  roundOffAmount.toFixed(2),
                ),
              ]
            : [],
          totalAmount: this.typeService.amountNumberWithCommas(
            roundOffAmount.toFixed(2),
          ),
          Balance: this.typeService.amountNumberWithCommas('0.00'),
        });

      balance = 0;
    }

    if (sortedList.length !== 0)
      sortedList.push({
        Date: 'Total',
        Particulars: [],
        isLastPage: 0,
        Debit: [
          this.typeService.amountNumberWithCommas(
            Math.abs(totalDebit).toFixed(2),
          ),
        ],
        Credit: [
          this.typeService.amountNumberWithCommas(
            Math.abs(totalCredit).toFixed(2),
          ),
        ],
        totalAmount: balance,
        Balance:
          (balance < 0 ? '-' : '') +
          this.typeService.amountNumberWithCommas(Math.abs(balance).toFixed(2)),
      });

    if (delayDays == 0) loanDueStatus = 'Regular';
    if (delayDays > 0) loanDueStatus = 'Overdue';
    if (loanData?.loanStatus == 'Complete') loanDueStatus = 'Closed';

    return {
      sortedList,
      overdueAmount: data?.overdueAmount.toFixed(2),
      totalRemaining: totalRemaining.toFixed(2),
      overdueCharge: data?.overdueCharge.toFixed(2),
      faultLedger,
      emiAmount: emiAmount.toFixed(2),
      overdue: overdue,
      hasIGST,
      cgst: cgst.toFixed(2),
      sgst: sgst.toFixed(2),
      igst: igst.toFixed(2),
      firstEMIDate,
      lastEMIDate,
      oneEmiAmount: oneEmiAmount.toFixed(2),
      loanDueStatus,
      refund,
      hasEqualPI,
    };
  }

  async generateBulkLedgers(reqData) {
    // Params validation
    let loanIds = reqData.loanIds;
    if (!loanIds) return kParamMissing('loanIds');
    loanIds = [...new Set(loanIds)];

    const finalizedData = {};
    for (let index = 0; index < loanIds.length; index++) {
      const loanId = loanIds[index];
      const legderResponse = await this.getLedgerLoanDetails({
        loanId,
        pdfDownload: 'true',
      });
      finalizedData[loanId] = legderResponse;
    }

    return finalizedData;
  }

  async generateLCRReport(reqData) {
    let startDate: string | Date = reqData?.hqlaDate
      ? new Date(reqData.hqlaDate)
      : new Date();
    startDate = this.typeService.getGlobalDate(startDate).toJSON();
    let endDate: Date | string = new Date(startDate);
    endDate.setDate(endDate.getDate() + 29);
    endDate = endDate.toJSON();
    const adminId: number | string = reqData?.adminId;

    // update hqla and other details
    if (adminId) {
      const outflow: number = reqData?.outflow;
      if (!outflow) return kParamMissing('Outflow Amount');
      const hqlaData = reqData?.hqlaData ?? [];
      let bankBalance = 0;
      let marketableSecurity = 0;
      const total = hqlaData.reduce(
        (acc, ele) => {
          if (ele.type == 'bank') acc.bankTotal += ele?.balance ?? 0;
          if (ele.type == 'marketableSecurities')
            acc.mSTotal += ele?.balance ?? 0;
          return acc;
        },
        { bankTotal: 0, mSTotal: 0 },
      );
      bankBalance += total.bankTotal;
      marketableSecurity += total.mSTotal;

      const inflowData = await this.repoManager.getRowWhereData(
        LCREntity,
        ['inflow', 'stressedInflow'],
        { where: { date: startDate } },
      );
      if (inflowData == k500Error) throw new Error();

      // if provided date data does not exist then create new data for that date
      let inflowDetails;
      if (!inflowData) {
        inflowDetails = await this.getInflowData(startDate, endDate);
        if (inflowDetails == k500Error) throw new Error();
      }

      const hqla = +marketableSecurity + +bankBalance;
      const stressedOutflow = Math.round(+outflow * 1.15);
      const value = Math.min(
        inflowDetails
          ? +inflowDetails.stressedInflow
          : +inflowData.stressedInflow,
        Math.round(stressedOutflow * 0.75),
      );
      const totalNetOutflow = stressedOutflow - value;
      const LCR = +(+(hqla / totalNetOutflow) * 100).toFixed(2);

      const updateData: any = {
        date: startDate,
        bankBalance: +bankBalance,
        marketableSecurity: +marketableSecurity,
        outflow: +outflow,
        stressedOutflow: +stressedOutflow,
        totalNetOutflow: +totalNetOutflow,
        LCR: +LCR,
        adminId: +adminId,
        payload: hqlaData,
      };

      // create new entry for provided date with all details
      if (inflowDetails) {
        updateData.inflow = inflowDetails.inflow;
        updateData.stressedInflow = inflowDetails.stressedInflow;

        const createdData = await this.repoManager.createRowData(
          LCREntity,
          updateData,
        );
        if (createdData == k500Error) throw new Error();
        return true;
      }
      // otherwise data already exist for this date, update other details for provided date
      const updatedData = await this.repoManager.updateRowWhereData(
        LCREntity,
        updateData,
        { where: { date: startDate } },
      );
      if (updatedData == k500Error) throw new Error();
      return true;
    }

    // create new entry of inflow data in cron
    const data: any = await this.getInflowData(startDate, endDate);
    if (data == k500Error) throw new Error();
    const createdData = await this.repoManager.createRowData(LCREntity, data);
    if (createdData == k500Error) throw new Error();
    return true;
  }

  async getInflowData(startDate: string, endDate: string) {
    const query = `SELECT SUM("a"."principalCovered" + "a"."interestCalculate") as "totalAmount"
    FROM public."EmiEntities" as "a"
    JOIN public."loanTransactions" as "b" ON "a"."loanId" = "b"."id"
    WHERE "a"."emi_date" >= '${startDate}' AND "a"."emi_date" <= '${endDate}'
    AND (("a"."payment_status" = '0') OR ("a"."payment_done_date" > '${startDate}')) 
    AND "b"."loan_disbursement_date" < '${startDate}'`;

    const response = await this.repoManager.injectRawQuery(EmiEntity, query);
    if (!response || response == k500Error) throw new Error();

    const inflow = +response[0].totalAmount;
    const stressedInflow = Math.round(inflow * 0.75);

    return {
      date: startDate,
      inflow: +inflow,
      stressedInflow: +stressedInflow,
      adminId: SYSTEM_ADMIN_ID,
    };
  }

  async getLCRInfo(reqData) {
    let startDate = reqData?.startDate ?? new Date();
    let endDate = reqData?.endDate ?? new Date();
    let lcrDate: Date;

    startDate = this.typeService.getGlobalDate(startDate);
    endDate = this.typeService.getGlobalDate(endDate);

    const sendMail = reqData?.sendMail ?? false;
    const dailyMail = reqData?.dailyMail ?? false;
    if (sendMail) {
      const currentDate = new Date();
      const startOfCurrentMonth: any = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        1,
      );
      const endOfPreviousMonth: any = new Date(startOfCurrentMonth - 1);
      const startOfPreviousMonth = new Date(
        endOfPreviousMonth.getFullYear(),
        endOfPreviousMonth.getMonth(),
        1,
      );
      startDate = this.typeService.getGlobalDate(startOfPreviousMonth);
      endDate = this.typeService.getGlobalDate(endOfPreviousMonth);
    }
    let currentDate = startDate;
    if (dailyMail) {
      //Get data of 2 days prior if it is from dailyMail
      currentDate.setDate(
        currentDate.getDate() - (LCR_DATA_SEND_THRESHOLD_DAYS - 1),
      );
      currentDate = this.dateService.getGlobalDate(currentDate);

      lcrDate = currentDate;
      startDate = currentDate.toJSON();
      endDate = currentDate.toJSON();
    }
    const pageSize = +(reqData?.pageSize ?? 10);
    const page = +(reqData?.page ?? 1);
    const offset = (page - 1) * pageSize;

    const attributes = [
      'date',
      'bankBalance',
      'marketableSecurity',
      'outflow',
      'stressedOutflow',
      'inflow',
      'stressedInflow',
      'totalNetOutflow',
      'LCR',
      'adminId',
      'payload',
    ];

    const options: any = {
      where: { date: { [Op.and]: { [Op.gte]: startDate, [Op.lte]: endDate } } },
      order: [['date', 'DESC']],
    };
    if (!sendMail && !dailyMail && !reqData.download) {
      options.offset = offset;
      options.limit = pageSize;
    }

    const response = await this.repoManager.getTableCountWhereData(
      LCREntity,
      attributes,
      options,
    );
    if (response.count == 0 && !dailyMail) kNoDataFound;
    if (response == k500Error) throw new Error();

    // for daily mail if data is not present then create data
    if (response.count == 0 && dailyMail) {
      endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 29);
      endDate = endDate.toJSON();

      const inflowData: any = await this.getInflowData(startDate, endDate);
      if (inflowData == k500Error) return { rows: [] };
      const createdData = await this.repoManager.createRowData(
        LCREntity,
        inflowData,
      );
      if (createdData == k500Error) return { rows: [] };
      const rows = [
        {
          Date: this.dateService.dateToReadableFormat(inflowData.date)
            .readableStr,
          'Bank Balance': inflowData?.bankBalance ?? '-',
          'Marketable Securities': inflowData?.marketableSecurity ?? '-',
          'Total HQLA': inflowData?.totalHqla ?? '-',
          'Expected Cash Inflow': inflowData?.inflow ?? '-',
          'Stressed Cash Inflow': inflowData?.stressedInflow ?? '-',
          'Expected Cash Outflow': inflowData?.outflow ?? '-',
          'Stressed Cash Outflow': inflowData?.stressedOutflow ?? '-',
          'Total Net Cash Outflows': inflowData?.totalNetOutflow ?? '-',
          'LCR Percentage': inflowData?.LCR ?? '-',
          'Updated By': 'System',
        },
      ];
      return {
        rows,
        currentDate: currentDate.toLocaleDateString('en-IN', {
          day: 'numeric',
          month: 'short',
          year: 'numeric',
        }),
      };
    }

    const rows = [];
    for (let i = 0; i < response.rows.length; i++) {
      const data = response.rows[i];
      const admin = await this.commonSharedService.getAdminData(data.adminId);
      const totalHqla =
        (data?.bankBalance ?? 0) + (data?.marketableSecurity ?? 0);
      const structuredData = {
        ['Date']: this.dateService.dateToReadableFormat(data.date).readableStr,
        ['Bank Balance']: data?.bankBalance ?? '-',
        ['hqlaData']: data?.payload ?? [],
        ['Marketable Securities']: data?.marketableSecurity ?? '-',
        ['Total HQLA']: totalHqla,
        ['Expected Cash Inflow']: data?.inflow ?? '-',
        ['Stressed Cash Inflow']: data?.stressedInflow ?? '-',
        ['Expected Cash Outflow']: data?.outflow ?? '-',
        ['Stressed Cash Outflow']: data?.stressedOutflow ?? '-',
        ['Total Net Cash Outflows']: data?.totalNetOutflow ?? '-',
        ['LCR Percentage']: data?.LCR ? +data?.LCR : '-',
        ['Updated By']: admin ?? '-',
      };
      rows.push(structuredData);
    }

    let url: any;
    const rawExcelData = {
      sheets: ['local-reports'],
      data: [rows],
      sheetName: 'LCR Report.xlsx',
      needFindTuneKey: false,
    };
    if (reqData.download) {
      url = await this.fileService.objectToExcelURL(rawExcelData);
      if (url?.message) return url;
      const updatedData = { downloadUrl: url, status: '1' };
      const downloadId = reqData?.downloadId;
      if (downloadId)
        await this.reportHistoryRepo.updateRowData(
          updatedData,
          reqData.downloadId,
        );
      return { fileUrl: url };
    }
    if (sendMail) {
      const currentYear = new Date(startDate).getFullYear();
      const monthAndYear =
        kMonths[new Date(startDate).getMonth() - 1] + currentYear;
      url = await this.fileService.objectToExcelURL(rawExcelData);
      if (url?.message) return url;
      return await this.sendMailToManagement(rows, url, monthAndYear);
    }
    return {
      rows,
      count: response.count,
      lcrDate: lcrDate
        ? lcrDate.toLocaleDateString('en-IN', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          })
        : startDate.toLocaleDateString('en-IN', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          }),
    };
  }

  async sendMailToManagement(data: any, url: string, monthAndYear: string) {
    const subject = 'LCR Report';
    const nbfcLogo = EnvConfig.url.nbfcLogo;
    const nbfcName = EnvConfig.nbfc.nbfcName;
    const nbfcRegisterationNumber = EnvConfig.nbfc.nbfcRegistrationNumber;
    const nbfcAddress = EnvConfig.nbfc.nbfcAddress;
    const reportMonth = monthAndYear;
    let email = EnvConfig.mail.techSupportMail;
    const finalData = {
      rows: data,
    };
    const hbsData = await this.fileService.hbsHandlebars(
      tLCRReportToManagementTemplate,
      {
        ...finalData,
        lcrFields,
        nbfcLogo,
        nbfcName,
        nbfcRegisterationNumber,
        nbfcAddress,
        reportMonth,
      },
    );
    if (hbsData == k500Error) throw new Error();
    const qa = process.env.QA_EMAILS;
    const management = process.env.MANAGEMENT_EMAILS;
    let ccEmails: any = `${management}`;
    if (!gIsPROD) {
      email = kAdmins[1];
      ccEmails = kAdmins[2];
    }
    if (isUAT) ccEmails = qa;

    ccEmails = ccEmails.split(',');
    return await this.notificationService.sendMailFromSendinBlue(
      email,
      subject,
      hbsData,
      null,
      ccEmails,
      [{ path: url, fileName: 'LCR REPORT' }],
    );
  }

  async onDemandSettlement(reqData): Promise<any> {
    const adminId = reqData.adminId;
    if (!adminId) return kParamMissing('adminId');
    const amount = reqData.amount;
    if (amount == undefined || amount == null) return kParamMissing('amount');
    if (amount < 10000)
      return k422ErrorMessage('Minimum settlement amount should be 10,000');
    const id = reqData.id;
    if (!id) return kParamMissing('id');
    if (![1, 2].includes(id)) return kInvalidParamValue('id');
    const phrase = reqData.phrase;
    if (!phrase) return kParamMissing('phrase');
    if (!confirmation_phrases.includes(phrase))
      return k422ErrorMessage('Please enter correct phrase value');

    const repaymentData = await this.razorService.getRepaymentBalance();
    const targetData = repaymentData[id - 1];
    if (targetData.balance < amount) {
      return k422ErrorMessage(
        `Maximum settlement amount available is ${this.typeService.amountNumberWithCommas(
          targetData.balance,
        )}`,
      );
    }

    const adminData = await this.common.getAdminData(adminId);
    const adminName = adminData.fullName ?? 'Admin';

    const response = await this.razorService.initiateOnDemandSettlement({
      amount,
      id,
    });
    if (response.message) return response;

    await this.dashboardService.bankBalance({ isRefresh: 'true' });

    await this.slack.sendMsg({
      channel: EnvConfig.slack.accountsChannelId,
      text: `NBFC - ${
        EnvConfig.nbfc.nbfcName
      }\nOn demand settlement amount of ${this.typeService.amountNumberWithCommas(
        amount,
      )}\nis initiated by ${adminName} from ${targetData.title}`,
    });

    return {};
  }

  async accountSummary(reqData) {
    const startDate = reqData.start_date;
    const endDate = reqData.end_date;
    const isDownload = reqData.download == 'true';

    const rawList = await this.report.tallyDisbursement({
      startDate,
      endDate,
      fullRawList: 'true',
    });
    if (rawList?.message) return rawList;

    const tempData: any = {};
    for (let index = 0; index < rawList.rows.length; index++) {
      const el = rawList.rows[index];
      const disbursedDate = el['disbursement_date'];
      const approvedAmount = +el['approvedAmount'].replace(/,/g, '');
      const disbursementAmount = +el['disbursementAmount'].replace(/,/g, '');
      const processingFessIncome = +el['processingFessIncome'].replace(
        /,/g,
        '',
      );
      const docCharges = el['Loan Documentation Charges'] ?? 0;
      const onlineCharges = +el['Online Conveyance Charges'].replace(/,/g, '');
      const stampFees = el['stampFees'] ?? 0;
      const insurancePremium = el['Insurance Premium'] ?? 0;
      const careInsuranceAmt = el['Care_Health_Insurance_Amt'] ?? 0;
      const ackoInsuranceAmt = el['Acko_Insurance_Amt'] ?? 0;
      const gateway = el['disbursement_bank_gateway'];
      const CGST = el['CGST'];
      const SGST = el['SGST'];
      const riskCharges = el['Risk Assessment Fees'] ?? 0;
      let key = `${disbursedDate}${gateway}`;
      key = key.replace(/ /g, '').replace(/-/g, '');
      key = this.cryptService.getMD5Hash(key);
      key = key.substring(0, 5) + key.substring(26, 31);

      // Assign -> Default value
      if (!tempData[key]) {
        tempData[key] = {
          disbursement_date: disbursedDate,
          netApproved_Ref_No: `NA_${key}`,
          disbursement_bank_gateway: gateway,
          disbursementAmount: 0,
          stampFees: 0,
          stampDutyIncome: 0,
          Care_Health_Insurance_Amt: 0,
          Acko_Insurance_Amt: 0,
          processingFessIncome: 0,
          'Loan Documentation Charges': 0,
          'Online Conveyance Charges': 0,
          'Risk Assessment Fees': 0,
          CGST: 0,
          SGST: 0,
          approvedAmount: 0,
          'Insurance Premium': 0,
        };
      }

      tempData[key].disbursementAmount += disbursementAmount;
      tempData[key].processingFessIncome += processingFessIncome;
      tempData[key].approvedAmount += approvedAmount;
      tempData[key]['Insurance Premium'] += insurancePremium;
      tempData[key].Care_Health_Insurance_Amt += careInsuranceAmt;
      tempData[key].Acko_Insurance_Amt += ackoInsuranceAmt;
      tempData[key].CGST += CGST;
      tempData[key].SGST += SGST;
      tempData[key].stampFees += stampFees;
      tempData[key]['Loan Documentation Charges'] += docCharges;
      tempData[key]['Online Conveyance Charges'] += onlineCharges;
      tempData[key]['Risk Assessment Fees'] += riskCharges;
    }

    const finalizedList = [];
    for (const key in tempData) {
      const value = tempData[key];
      finalizedList.push(value);
    }

    finalizedList.sort(
      (a, b) =>
        this.dateService.anyToDate(a['disbursement_date']).getTime() -
        this.dateService.anyToDate(b['disbursement_date']).getTime(),
    );

    if (isDownload) {
      const rawExcelData = {
        sheets: ['Disbursement report'],
        data: [finalizedList],
        sheetName: 'Disbursement report.xlsx',
        needFindTuneKey: false,
      };
      // Generate excel url
      const fileUrl = await this.fileService.objectToExcelURL(rawExcelData);
      if (fileUrl?.message) return fileUrl;
      return { fileUrl };
    }

    return { count: finalizedList.length, rows: finalizedList };
  }

  async hypothification(reqData) {
    const target_date = reqData.target_date;
    if (!target_date) return kParamMissing('target_date');
    const min_loan_amount = reqData.min_loan_amount;
    const max_loan_amount = reqData.max_loan_amount;
    let base_amount = reqData.base_amount;
    const isDownload = (reqData.download ?? 'false') == 'true';
    const exceptionLoanIds = reqData.exceptionLoanIds ?? [];

    let start_date = reqData.start_date
      ? new Date(reqData.start_date)
      : new Date(target_date);
    start_date = this.typeService.getGlobalDate(start_date);
    if (!reqData.start_date) start_date.setDate(1);

    const end_date = this.typeService.getGlobalDate(
      this.typeService.getLastDateOfMonth(new Date(target_date)),
    );
    const endTime = end_date.getTime();

    const loanAttr = ['id', 'loanCompletionDate', 'netApprovedAmount'];
    const loanOptions: any = {
      order: [['loan_disbursement_date', 'ASC']],
      where: {
        loan_disbursement_date: { [Op.gte]: start_date, [Op.lte]: end_date },
      },
    };
    if (exceptionLoanIds.length > 0) {
      loanOptions.where.id = { [Op.notIn]: exceptionLoanIds };
    }
    const loanList = await this.repoManager.getTableWhereData(
      loanTransaction,
      loanAttr,
      loanOptions,
    );
    if (loanList == k500Error) throw new Error();

    const loanIds = loanList.map((el) => el.id);
    const emiAttr = ['id', 'loanId', 'principalCovered', 'payment_done_date'];
    const emiOptions = { where: { loanId: loanIds } };
    const emiList = await this.repoManager.getTableWhereData(
      EmiEntity,
      emiAttr,
      emiOptions,
    );
    if (emiList == k500Error) throw new Error();

    const transAttr = ['emiId', 'principalAmount', 'loanId'];
    const transOptions = {
      where: {
        completionDate: { [Op.lte]: end_date },
        loanId: loanIds,
        status: kCompleted,
        type: { [Op.notIn]: [kRefund, kFullPay] },
      },
    };
    const transList = await this.repoManager.getTableWhereData(
      TransactionEntity,
      transAttr,
      transOptions,
    );
    if (transList == k500Error) throw new Error();

    const finalizedList = [];
    for (let index = 0; index < loanList.length; index++) {
      const loanData = loanList[index];

      if (+loanData.netApprovedAmount < min_loan_amount) continue;
      if (+loanData.netApprovedAmount > max_loan_amount) continue;

      if (loanData.loanCompletionDate) {
        const completionDate = new Date(loanData.loanCompletionDate);
        const completionTime = completionDate.getTime();

        if (completionTime <= endTime) continue;
      }

      if (base_amount > 0) {
        const targetEMIs = emiList.filter((el) => el.loanId == loanData.id);
        const targetPayments = transList.filter(
          (el) => el.loanId == loanData.id,
        );
        let loanOutstanding = 0;
        for (let i = 0; i < targetEMIs.length; i++) {
          const emiData = targetEMIs[i];
          const paymentDate = emiData.payment_done_date;
          if (paymentDate) {
            const paidDate = this.typeService.getGlobalDate(
              new Date(paymentDate),
            );
            if (paidDate.getTime() <= endTime) continue;
          }
          const expPrincipal = emiData.principalCovered ?? 0;
          const emiPayments = targetPayments.filter(
            (el) => el.emiId == emiData.id,
          );

          const paidPrincipal = emiPayments.reduce((total, element) => {
            return total + (element?.principalAmount ?? 0);
          }, 0);

          // Running EMI
          if (paidPrincipal < expPrincipal) {
            loanOutstanding += expPrincipal - paidPrincipal;
          }
        }
        base_amount -= loanOutstanding;
        finalizedList.push({
          max_loan_amount,
          loanId: loanData.id,
          approvedAmount: +loanData.netApprovedAmount,
          principalOutstanding: loanOutstanding,
        });
      }
    }

    if (isDownload) {
      const rawExcelData = {
        sheets: ['Hypothification'],
        data: [finalizedList],
        sheetName: 'Hypothification report.xlsx',
        needFindTuneKey: false,
      };
      const fileUrl = await this.fileService.objectToExcelURL(rawExcelData);
      if (fileUrl?.message) return fileUrl;
      return { fileUrl };
    }

    return { remainingBalance: base_amount, rows: finalizedList };
  }

  async addLenderDetails(reqData, file) {
    let params: any = {
      lenderName: reqData?.lenderName,
      loanAmount: parseFloat(reqData?.loanAmount),
      hypothecatedAmount: parseFloat(reqData?.hypothecatedAmount),
      maxLoanAmount: parseFloat(reqData?.maxLoanAmount),
      minLoanAmount: parseFloat(reqData?.minLoanAmount),
      outStandingAmount: parseFloat(reqData?.loanAmount),
      hypothecatedpercentage: parseFloat(reqData?.hypothecatedPercentage),
      dayOfEMI: parseInt(reqData?.dayOfEMI),
      loanTenure: parseFloat(reqData?.loanTenure),
      approvedPrincipalAmount: parseFloat(reqData?.approvedPrincipalAmount),
      emiAmount: parseFloat(reqData?.emiAmount),
      repaymentPercentage: parseFloat(reqData?.repaymentPercentage),
      max_dpd: parseInt(reqData?.max_dpd),
      interestAmount: parseFloat(reqData?.interestAmount),
      adminId: +reqData?.adminId,
    };

    const missingParam = Object.entries(params).find(([key, value]) => !value);
    if (missingParam) return kParamMissing(missingParam[0]);

    if (!params.lenderName.trim())
      return k422ErrorMessage('please enter valid Name');

    if (params.dayOfEMI < 1 || params.dayOfEMI > 31)
      return k422ErrorMessage('please enter valid day of EMI');

    if (
      params.maxLoanAmount < GLOBAL_RANGES.MIN_LOAN_AMOUNT ||
      params.maxLoanAmount < params.minLoanAmount
    )
      return k422ErrorMessage(
        'Max amount is less than minimum loan amount criteria',
      );

    const repaymentSchedule = file.find(
      (file) => file.fieldname === 'repaymentSchedule',
    );
    if (!repaymentSchedule) return kParamMissing('repaymentSchedule');

    const repaymentScheduleFileName = repaymentSchedule?.filename;
    if (!repaymentScheduleFileName) return kParamMissing('fileName');
    if (!repaymentScheduleFileName.endsWith('xlsx'))
      return k422ErrorMessage('Upload valid excel file!');

    const excelToArray: any = await this.fileService.excelToArray(
      repaymentScheduleFileName,
      HypothecationLenderColumns,
      true,
    );
    if (excelToArray?.message) throw new Error();

    const missingColumns = Object.values(HypothecationLenderColumns).filter(
      (column) => !excelToArray.columnName.includes(column),
    );
    if (missingColumns.length > 0)
      return k422ErrorMessage(
        `Excel is not having ${missingColumns[0]} column`,
      );

    let repaymentScheduleFile = await this.fileService.uploadFile(
      repaymentScheduleFileName,
      'hypothecation_repayment_schedule',
      'xlsx',
    );
    repaymentScheduleFile = await this.cryptService.encryptText(
      repaymentScheduleFile,
    );

    params.repaymentSchedule = repaymentScheduleFile;
    const lenderDoc = file.find((file) => file.fieldname === 'lenderDoc');
    if (lenderDoc) {
      const lenderDocFileName = lenderDoc?.filename;
      if (!lenderDocFileName) return kParamMissing('fileName');

      let lenderDocFileNameFile = await this.fileService.uploadFile(
        lenderDocFileName,
      );

      lenderDocFileNameFile = await this.cryptService.encryptText(
        lenderDocFileNameFile,
      );
      params.lenderDoc = lenderDocFileNameFile;
    }

    excelToArray.finalData.forEach((el) => {
      el.emi_date = this.dateService.convertExcelDate(el.emi_date);
    });

    const addLenderData = await this.repoManager.createRowData(
      HypothecationEntity,
      params,
    );
    if (addLenderData == k500Error) throw new Error();

    const emiHistoryData = excelToArray.finalData.map((item) => ({
      ...item,
      lender_id: addLenderData.id,
      emiPaid: false,
      adminId: params.adminId,
    }));

    const createHistoryData = await this.repoManager.bulkCreate(
      HypothecationPaymentsEntity,
      emiHistoryData,
    );
    if (createHistoryData == k500Error) throw new Error();

    return { lender_id: addLenderData.id };
  }

  async viewLenderDetails(reqData) {
    const page = reqData?.page || 1;

    const options: any = {
      order: [['id', 'DESC']],
      offset: page * 5 - 5,
      limit: 5,
    };

    const getLenderData = await this.repoManager.getTableCountWhereData(
      HypothecationEntity,
      null,
      options,
    );
    if (getLenderData == k500Error) throw new Error();

    const finalizedData = [];
    for (let i = 0; i < getLenderData.rows.length; i++) {
      let lenderData = getLenderData.rows[i];
      let formatData: any = {};
      formatData['lender_id'] = lenderData?.id;
      formatData['Lender Name'] = lenderData?.lenderName;
      formatData['Loan Amount'] = this.typeService.amountNumberWithCommas(
        lenderData?.loanAmount,
      );
      formatData['Hypothecated Percentage'] =
        lenderData?.hypothecatedpercentage + '%';
      formatData['Outstanding Amount'] =
        this.typeService.amountNumberWithCommas(lenderData?.outStandingAmount);

      formatData['Max DPD Days'] = lenderData?.max_dpd;
      formatData['Approved Principal Amount'] =
        this.typeService.amountNumberWithCommas(
          lenderData?.approvedPrincipalAmount,
        );
      formatData['Hypothecated Amount'] =
        this.typeService.amountNumberWithCommas(lenderData?.hypothecatedAmount);
      formatData['Upcoming Repayment'] = lenderData?.dayOfEMI; //need to change
      formatData['No. Of Repayments'] = lenderData?.loanTenure;
      formatData['Document'] = await this.cryptService.decryptText(
        lenderData?.repaymentSchedule,
      );
      formatData['last updated by'] =
        (await this.commonSharedService.getAdminData(lenderData?.adminId))
          ?.fullName ?? '-';
      finalizedData.push(formatData);
    }
    getLenderData.rows = finalizedData;
    return getLenderData;
  }

  async hypothecationRepaymentDetails(reqData) {
    const page = reqData?.page || 1;
    const download = reqData?.download ?? false;
    const downloadId = reqData?.downloadId ?? '0';
    const lenderId = reqData?.lender_id;
    if (!lenderId) return kParamMissing('lenderId');

    const options: any = {
      order: [['emi_no', 'ASC']],
      where: {
        lender_id: lenderId,
      },
    };

    if (!download) {
      (options.offset = page * 5 - 5), (options.limit = 5);
    }

    const options2: any = {
      where: {
        lender_id: lenderId,
        emiPaid: true,
      },
    };

    const getLenderData = await this.repoManager.getTableCountWhereData(
      HypothecationPaymentsEntity,
      null,
      options,
    );
    if (getLenderData == k500Error) throw new Error();

    const getLenderDataPaid = await this.repoManager.getCountsWhere(
      HypothecationPaymentsEntity,
      options2,
    );

    const emiStatus = {
      total: getLenderData?.count,
      paid: getLenderDataPaid,
      unpaid: getLenderData?.count - getLenderDataPaid,
    };

    const finalizedData = [];
    for (let i = 0; i < getLenderData.rows.length; i++) {
      let lenderData = getLenderData.rows[i];
      let formatData: any = {};
      formatData['Status'] = lenderData?.emiPaid;
      formatData['EMI No.'] = lenderData?.emi_no;
      formatData['Month'] = await this.dateService.dateToReadableFormat(
        lenderData?.emi_date,
        'DD/MM/YYYY',
      )?.readableStr;

      formatData['Opening balance'] = this.typeService.amountNumberWithCommas(
        lenderData?.openingBalance,
      );
      formatData['EMI'] = this.typeService.amountNumberWithCommas(
        lenderData?.emiAmount,
      );
      formatData['Interest'] = this.typeService.amountNumberWithCommas(
        lenderData?.interestAmount,
      );
      formatData['Principal'] = this.typeService.amountNumberWithCommas(
        lenderData?.principalAmount,
      );
      formatData['Closing Balance'] = this.typeService.amountNumberWithCommas(
        lenderData?.closingBalance,
      );

      formatData['Last update by'] =
        (await this.commonSharedService.getAdminData(lenderData?.adminId))
          ?.fullName ?? '-';
      finalizedData.push(formatData);
    }
    getLenderData.rows = finalizedData;

    if (download == true) {
      const rawExcelData = {
        sheets: ['local-reports'],
        data: [getLenderData.rows],
        sheetName: 'Hypothecation.xlsx',
        needFindTuneKey: false,
        reportStore: true,
      };

      const url: any = await this.fileService.objectToExcelURL(rawExcelData);
      if (url?.message) return url;

      const updatedData = { downloadUrl: url, status: '1' };
      await this.reportHistoryRepo.updateRowData(updatedData, downloadId);
      return { fileUrl: url };
    } else {
      return { ...getLenderData, emiStatus };
    }
  }

  async updateHypothecationRepaymentDetails(reqData) {
    const updatePayment = reqData?.updatePayment ?? true;
    const adminId = reqData?.adminId;
    if (!adminId) return kParamMissing('adminId');
    const lenderId = reqData?.lenderId;
    if (!lenderId) return kParamMissing('lenderId');
    const emiNo = reqData?.emiNo;
    if (emiNo == null || emiNo == undefined) return kParamMissing('emiNo');

    const options = {
      where: {
        lender_id: lenderId,
        emi_no: emiNo,
      },
    };

    const updatedData = {
      lastUpdatedBy: adminId,
      emiPaid: updatePayment,
    };

    const paymentUpdate = await this.repoManager.updateRowWhereData(
      HypothecationPaymentsEntity,
      updatedData,
      options,
    );
    if (paymentUpdate == k500Error) throw new Error();

    return true;
  }

  async newHypothification(reqData) {
    const target_date = reqData.target_date;
    if (!target_date) return kParamMissing('target_date');

    let min_loan_amount;
    let max_loan_amount;
    let base_amount;
    const exceptionLoanIds = reqData.exceptionLoanIds ?? [];
    let lenderId;
    let maxDelayDays;

    // Date range
    let start_date = reqData.start_date
      ? new Date(reqData.start_date)
      : new Date(target_date);
    start_date = this.typeService.getGlobalDate(start_date);
    if (!reqData.start_date) start_date.setDate(1);
    const end_date = this.typeService.getGlobalDate(
      this.typeService.getLastDateOfMonth(new Date(target_date)),
    );

    // Purpose -> Debugging
    const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;
    const target_lender_id = reqData.target_lender_id;
    const target_lender_name = reqData.target_lender_name;

    const hypoOptions: any = { where: {} };
    if (target_lender_id) {
      hypoOptions.where.id = target_lender_id; // Purpose -> Debugging
    } else if (target_lender_name) {
      hypoOptions.where.lenderName = target_lender_name; // Purpose -> Debugging
    }

    const allLenderData = await this.repoManager.getTableWhereData(
      HypothecationEntity,
      null,
      hypoOptions,
    );
    if (allLenderData == k500Error) throw new Error();

    const finalData: any = [];
    for (let i = 0; i < allLenderData.length; i++) {
      const lenderData = allLenderData[i];
      min_loan_amount = lenderData?.minLoanAmount;
      max_loan_amount = lenderData?.maxLoanAmount;
      base_amount = lenderData?.hypothecatedAmount;
      lenderId = lenderData?.id;
      maxDelayDays = lenderData?.max_dpd;

      // Restriction -> exception_state are not allowed to hypothicate
      let exception_state = lenderData?.exception_state ?? [];
      exception_state = exception_state.map((el) => el.toLowerCase());

      const endTime = end_date.getTime();
      const loanAttr = [
        'id',
        'loanCompletionDate',
        'netApprovedAmount',
        'hypothecation_assigned_date',
      ];

      // Joins
      const kycInclude: SequelOptions = {
        attributes: ['aadhaarState'],
        model: KYCEntity,
        required: false,
      };
      const userInclude: SequelOptions = {
        attributes: ['state'], // Current location state
        model: registeredUsers,
        include: [kycInclude],
      };

      const loanOptions: any = {
        include: [userInclude],
        order: [['loan_disbursement_date', 'ASC']],
        where: {
          [Op.or]: [
            {
              loan_disbursement_date: {
                [Op.gte]: start_date,
                [Op.lte]: end_date,
              },
              hypothecation_lender_id: null,
            },
            {
              hypothecation_assigned_date: { [Op.not]: null },
              hypothecation_lender_id: lenderId,
            },
          ],
        },
      };

      if (exceptionLoanIds.length > 0) {
        loanOptions.where.id = { [Op.notIn]: exceptionLoanIds };
      }

      const loanList = await this.repoManager.getTableWhereData(
        loanTransaction,
        loanAttr,
        loanOptions,
      );
      if (loanList == k500Error) throw new Error();

      const toDate = await this.typeService
        .getGlobalDate(end_date)
        .toISOString(); // Format to YYYY-MM-DDTHH:MM:SS

      const loanIds = loanList.map((el) => el.id);
      const emiAttr: any[] = [
        'id',
        'loanId',
        'principalCovered',
        'payment_done_date',
        [
          Sequelize.literal(`
      CASE
        WHEN "payment_due_status" = '0' THEN 0
        WHEN "emi_date" >= '${toDate}' THEN 0
        WHEN "payment_status" = '0' THEN
          TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("emi_date", 'YYYY-MM-DDTHH24:MI:SS')
        WHEN "payment_status" = '1' AND "payment_done_date" > '${toDate}' THEN
          TO_DATE('${toDate}', 'YYYY-MM-DDTHH24:MI:SS') - TO_DATE("emi_date", 'YYYY-MM-DDTHH24:MI:SS')
        WHEN "payment_status" = '1' AND "payment_done_date" <= '${toDate}' THEN 0
      END
    `),
          'delay_days',
        ],
      ];
      const emiOptions = { where: { loanId: loanIds } };
      const emiList = await this.repoManager.getTableWhereData(
        EmiEntity,
        emiAttr,
        emiOptions,
      );
      if (emiList == k500Error) throw new Error();

      const transAttr = ['emiId', 'principalAmount', 'loanId'];
      const transOptions = {
        where: {
          completionDate: { [Op.lte]: end_date },
          loanId: loanIds,
          status: kCompleted,
          type: { [Op.notIn]: [kRefund, kFullPay] },
        },
      };
      const transList = await this.repoManager.getTableWhereData(
        TransactionEntity,
        transAttr,
        transOptions,
      );
      if (transList == k500Error) throw new Error();

      const notEligibleLoan = [];
      const finalizedList = [];
      const newEligibleLoan = [];
      const newEligibleLoanList = [];

      const assignedData = loanList.filter(
        (item) => item.hypothecation_assigned_date !== null,
      );
      let eligibleAmount = 0;

      // for loop for unassign
      for (let index = 0; index < assignedData.length; index++) {
        const loanData = assignedData[index];

        if (base_amount > eligibleAmount) {
          if (
            loanData.loanCompletionDate &&
            new Date(loanData.loanCompletionDate).getTime() <=
              new Date(toDate).getTime()
          ) {
            notEligibleLoan.push(loanData.id);
            continue;
          }

          const targetEMIs = emiList.filter((el) => el.loanId == loanData.id);
          const targetPayments = transList.filter(
            (el) => el.loanId == loanData.id,
          );
          let loanOutstanding = 0;
          let isNotEligible = false;

          for (let i = 0; i < targetEMIs.length; i++) {
            const emiData = targetEMIs[i];
            if (emiData?.delay_days >= maxDelayDays) {
              notEligibleLoan.push(loanData.id);
              isNotEligible = true;
              continue;
            }

            const paymentDate = emiData.payment_done_date;
            if (paymentDate) {
              const paidDate = this.typeService.getGlobalDate(
                new Date(paymentDate),
              );
              if (paidDate.getTime() <= endTime) {
                isNotEligible = true;
                continue;
              }
            }
            const expPrincipal = emiData.principalCovered ?? 0;
            const emiPayments = targetPayments.filter(
              (el) => el.emiId == emiData.id,
            );

            const paidPrincipal = emiPayments.reduce((total, element) => {
              return total + (element?.principalAmount ?? 0);
            }, 0);

            // Running EMI
            if (paidPrincipal < expPrincipal) {
              loanOutstanding += expPrincipal - paidPrincipal;
            }
          }

          if (isNotEligible == true) continue;
          eligibleAmount += loanOutstanding;
          newEligibleLoan.push(loanData.id);
          newEligibleLoanList.push({
            loanId: loanData?.id,
            lenderId: lenderId,
            assigned_date: toDate,
            // unassigned_date: null
          });
          finalizedList.push({
            loanId: loanData.id,
            approvedAmount: +loanData.netApprovedAmount,
            principalOutstanding: loanOutstanding,
          });
        } else {
          notEligibleLoan.push(loanData.id);
        }
      }

      // isReadOnly ( Purpose -> Debugging )
      if (notEligibleLoan.length > 0 && !isReadOnly) {
        const updateNotELigibleQuery = `UPDATE  public."loanTransactions"
          SET "hypothecation_assigned_date" = null , 
          "hypothecation_lender_id" = NULL
           WHERE id IN (${notEligibleLoan})`;
        const notEligible = await this.repoManager.injectRawQuery(
          loanTransaction,
          updateNotELigibleQuery,
        );
        if (notEligible == k500Error) throw new Error();

        const updateUnassignedQuery = `UPDATE  public."HypothecationHistoryEntities"
          SET "unassigned_date" = '${toDate}' 
          WHERE "loanId" IN (${notEligibleLoan})  AND "lenderId" = '${lenderId}'`;

        const unassignedQuery = await this.repoManager.injectRawQuery(
          HypothecationHistoryEntity,
          updateUnassignedQuery,
        );
        if (unassignedQuery == k500Error) throw new Error();
      }

      const unAssignedData = loanList.filter(
        (item) => item.hypothecation_assigned_date == null,
      );

      if (eligibleAmount < base_amount) {
        base_amount -= eligibleAmount;
        //for loop for assign
        for (
          let index = 0;
          index < unAssignedData.length && base_amount > 0;
          index++
        ) {
          const loanData = unAssignedData[index];

          if (+loanData.netApprovedAmount < min_loan_amount) continue; // Restriction -> Lower loan amount
          if (+loanData.netApprovedAmount > max_loan_amount) continue; // Restriction -> Higher loan amount
          // Restriction -> State
          if (exception_state?.length > 0) {
            const live_location_state = (
              loanData.registeredUsers?.state ?? ''
            ).toLowerCase();
            const aadhaar_state = (
              loanData.registeredUsers?.kycData?.aadhaarState ?? ''
            ).toLowerCase();
            if (aadhaar_state == '') continue; // In some cases KYC state is not updated in the database column

            if (
              exception_state.includes(live_location_state) ||
              exception_state.includes(aadhaar_state)
            ) {
              continue;
            }
          }

          if (loanData.loanCompletionDate) {
            const completionDate = new Date(loanData.loanCompletionDate);
            const completionTime = completionDate.getTime();
            if (completionTime <= endTime) continue;
          }

          const targetEMIs = emiList.filter((el) => el.loanId == loanData.id);
          const targetPayments = transList.filter(
            (el) => el.loanId == loanData.id,
          );
          let loanOutstanding = 0;
          for (let i = 0; i < targetEMIs.length; i++) {
            const emiData = targetEMIs[i];

            if (emiData?.delay_days >= maxDelayDays) continue;

            const paymentDate = emiData.payment_done_date;
            if (paymentDate) {
              const paidDate = this.typeService.getGlobalDate(
                new Date(paymentDate),
              );
              if (paidDate.getTime() <= endTime) continue;
            }
            const expPrincipal = emiData.principalCovered ?? 0;
            const emiPayments = targetPayments.filter(
              (el) => el.emiId == emiData.id,
            );

            const paidPrincipal = emiPayments.reduce((total, element) => {
              return total + (element?.principalAmount ?? 0);
            }, 0);

            // Running EMI
            if (paidPrincipal < expPrincipal) {
              loanOutstanding += expPrincipal - paidPrincipal;
            }
          }
          base_amount -= loanOutstanding;
          newEligibleLoan.push(loanData.id);
          newEligibleLoanList.push({
            loanId: loanData?.id,
            lenderId: lenderId,
            assigned_date: toDate,
          });

          finalizedList.push({
            max_loan_amount,
            loanId: loanData.id,
            principalOutstanding: loanOutstanding,
            approvedAmount: +loanData.netApprovedAmount,
          });
        }
      }

      // isReadOnly ( Purpose -> Debugging )
      if (newEligibleLoan.length > 0 && !isReadOnly) {
        const updateELigibleQuery = `UPDATE  public."loanTransactions"
          SET "hypothecation_assigned_date" = '${toDate}', 
          "hypothecation_lender_id" = ${lenderId}
          WHERE id IN (${newEligibleLoan})`;

        const updateEligible = await this.repoManager.injectRawQuery(
          loanTransaction,
          updateELigibleQuery,
        );
        if (updateEligible == k500Error) throw new Error();

        const createHistoryData = await this.repoManager.bulkCreate(
          HypothecationHistoryEntity,
          newEligibleLoanList,
        );
        if (createHistoryData == k500Error) throw new Error();
      }

      const data = {
        LenderId: lenderId,
        remainingBalance: base_amount,
        rows: finalizedList,
      };
      finalData.push(data);
    }

    const isDownload = reqData.download == 'true';
    if (isDownload) {
      const rawExcelData = {
        sheets: ['Sheet 1'],
        data: [finalData],
        sheetName: 'Hypothecation.xlsx',
        needFindTuneKey: false,
      };

      const fileUrl: any = await this.fileService.objectToExcelURL(
        rawExcelData,
      );
      if (fileUrl?.message) return fileUrl;
      else return { fileUrl };
    } else return finalData;
  }

  async viewHypothecationUserHistory(reqData) {
    const lenderId = reqData?.lender_id;
    if (!lenderId) return kParamMissing('LenderId');

    const type = reqData?.type;
    if (!type) return kParamMissing('type');

    let month = reqData?.month;
    if (!month) return kParamMissing('month');
    month = new Date(month);

    const page = +(reqData?.page ?? 1);
    const download = reqData?.download ?? false;
    const downloadId = reqData?.downloadId ?? '0';

    const options: any = {
      order: [['id', 'DESC']],
    };

    if (!download) {
      (options.offset = page * PAGE_LIMIT - PAGE_LIMIT),
        (options.limit = PAGE_LIMIT);
    }

    if (type == 'ALL') {
      options.where = {
        lenderId: lenderId,
        [Op.or]: [{ assigned_date: month }, { unassigned_date: month }],
      };
    } else if (type == 'ADDED') {
      options.where = {
        lenderId: lenderId,
        assigned_date: month,
      };
    } else if (type == 'REMOVED') {
      options.where = {
        lenderId: lenderId,
        unassigned_date: month,
      };
    }
    const userHistory = await this.repoManager.getTableCountWhereData(
      HypothecationHistoryEntity,
      ['loanId'],
      options,
    );

    const loanIds = userHistory.rows.map((item) => item.loanId);

    const kycInclude: any = { model: KYCEntity };
    kycInclude.attributes = [
      'aadhaarState',
      'maskedPan',
      'panCardNumber',
      'aadhaarAddress',
      'aadhaarDOB',
      'panStatus',
      'userId',
      'pincode',
      'maskedAadhaar',
      'aadhaarResponse',
    ];

    const regInclude: any = { model: registeredUsers };
    regInclude.attributes = ['gender', 'fullName', 'uniqueId', 'city', 'phone'];
    regInclude.include = [kycInclude];

    const toDate = await this.typeService.getGlobalDate(month).toISOString(); // Format to YYYY-MM-DDTHH:MM:SS

    const emiInclude: any = { model: EmiEntity };
    emiInclude.attributes = [
      'id',
      'emi_date',
      'emi_amount',
      'emiNumber',
      'payment_done_date',
      'paid_principal',
      'payment_status',
      'payment_due_status',
      'interestCalculate',
      'principalCovered',
      [
        Sequelize.literal(`
          CASE
            WHEN "payment_due_status" = '0' THEN 0
            WHEN TO_DATE("emi_date", 'YYYY-MM-DD"T"HH24:MI:SS') >= TO_DATE('${toDate}', 'YYYY-MM-DD"T"HH24:MI:SS') THEN 0
            WHEN "payment_status" = '0' THEN
              TO_DATE('${toDate}', 'YYYY-MM-DD"T"HH24:MI:SS') - TO_DATE("emi_date", 'YYYY-MM-DD"T"HH24:MI:SS')
            WHEN "payment_status" = '1' AND TO_DATE("payment_done_date", 'YYYY-MM-DD"T"HH24:MI:SS') > TO_DATE('${toDate}', 'YYYY-MM-DD"T"HH24:MI:SS') THEN
              TO_DATE('${toDate}', 'YYYY-MM-DD"T"HH24:MI:SS') - TO_DATE("emi_date", 'YYYY-MM-DD"T"HH24:MI:SS')
            WHEN "payment_status" = '1' AND TO_DATE("payment_done_date", 'YYYY-MM-DD"T"HH24:MI:SS') <= TO_DATE('${toDate}', 'YYYY-MM-DD"T"HH24:MI:SS') THEN 0
            ELSE NULL
          END
        `),
        'delay_days',
      ],
    ];

    const loanAttr = [
      'userId',
      'loanAmount',
      'id',
      'netApprovedAmount',
      'processingFees',
      'interestRate',
      'approvedDuration',
      'charges',
      'loan_disbursement_date',
    ];
    const options2 = {
      where: {
        id: loanIds,
      },
      include: [regInclude, emiInclude],
    };

    const disbursementattributes = ['mode', 'loanId', 'amount', 'fundAccount'];
    const transactionAttr = [
      'loanId',
      'subSource',
      'id',
      'paidAmount',
      'completionDate',
      'principalAmount',
    ];

    const masterAttr = ['otherInfo', 'miscData', 'loanId'];
    const options3 = {
      where: {
        loanId: loanIds,
      },
    };

    const transactionOptions = {
      where: {
        loanId: loanIds,
        status: 'COMPLETED',
      },
      order: [['id', 'DESC']],
    };

    const [loanData, masterData, disbursementData, paymentData] =
      await Promise.all([
        this.repoManager.getTableWhereData(loanTransaction, loanAttr, options2),
        this.repoManager.getTableWhereData(MasterEntity, masterAttr, options3),
        this.repoManager.getTableWhereData(
          disbursementEntity,
          disbursementattributes,
          options3,
        ),
        this.repoManager.getTableWhereData(
          TransactionEntity,
          transactionAttr,
          transactionOptions,
        ),
      ]);

    const finalizedData: any = [];
    const bankDetails: any = fs.readFileSync('bankDetails.json', 'utf8');

    for (let i = 0; i < loanData.length; i++) {
      const loan = loanData[i];
      const master = masterData.find((f) => f.loanId === loan.id);
      const disburse = disbursementData.find((f) => f.loanId === loan.id);
      const payment = paymentData
        .filter((f) => f.loanId === loan.id)
        .sort((a, b) => b.id - a.id);

      let formatData: any = {};
      formatData['Loan Account Number'] = loan.id;
      formatData['Customer Name'] = loan?.registeredUsers?.fullName ?? '-';

      const address = this.typeService.getAadhaarAddress(
        loan?.registeredUsers?.kycData,
      );

      formatData['State'] = address?.state ?? '-';
      formatData['District'] = address?.dist ?? '-';
      formatData['Customer Code/ID'] = loan?.registeredUsers?.uniqueId ?? '-';
      formatData['Gender'] = loan?.registeredUsers?.gender ?? '-';
      formatData['Co-borrower/Spouse Name'] =
        master?.otherInfo?.spouseName ?? master?.otherInfo?.motherName ?? '-';
      formatData['Category of loan'] = 'PERSONAL LOAN';
      formatData['purpose of loan'] = master?.miscData?.purposeName ?? '-';
      formatData['Client Address'] = address?.address ?? '-';
      formatData['Pin code'] = loan?.registeredUsers?.kycData?.pincode ?? '-';
      formatData['KYC1 Type'] = 'Aadhaar';
      formatData['KYC1 Number'] =
        loan?.registeredUsers?.kycData?.maskedAadhaar ?? '-';
      formatData['KYC2 Type'] = 'PAN';
      formatData['KYC2 Number'] =
        loan?.registeredUsers?.kycData?.maskedPan ?? '-';
      formatData['Date of Birth'] =
        loan?.registeredUsers?.kycData?.aadhaarDOB ?? '-';
      formatData['Mobile Number'] =
        this.cryptService.decryptPhone(loan?.registeredUsers?.phone) ?? '-';
      formatData['Disbursement Date'] =
        this.dateService.dateToReadableFormat(loan?.loan_disbursement_date)
          .readableStr ?? '-';
      formatData['Loan Amount Disbursed'] = disburse?.amount
        ? disburse?.amount / 100
        : '-';
      formatData['Loan approved amount'] = loan?.netApprovedAmount ?? '-';
      formatData['Mode of Disbursement'] = disburse?.mode ?? '-';
      formatData['PF'] = loan?.processingFees
        ? `${loan?.processingFees}%`
        : '-';
      formatData['ROI'] = loan?.interestRate ? `${loan?.interestRate}%` : '-';
      let totalPrincipal = 0;
      let totalInterest = 0;
      let max_dpd = loan.emiData[0]?.delay_days;
      const overdueLoans: any = [];

      const advanceEmi = loan.emiData.filter((el) => {
        if (max_dpd < el?.delay_days) max_dpd = el.delay_days;
        totalPrincipal += el.principalCovered;
        totalInterest += el.interestCalculate;

        const paymentDate = new Date(el.payment_done_date);
        const emiDate = new Date(el.emi_date);

        if (el?.payment_due_status == '1' && emiDate <= month) {
          overdueLoans.push(el?.id);
        }

        const paymentMonth = this.typeService.getMonthAndYear(paymentDate);

        const emiMonth = this.typeService.getMonthAndYear(emiDate);

        return (
          paymentDate < emiDate &&
          paymentDate < month &&
          paymentMonth != emiMonth &&
          el?.payment_done_date != null
        );
      });
      formatData['Advance EMI Taken (If any)'] = advanceEmi.length;
      formatData['Product Name'] = 'PERSONAL LOAN';
      formatData['Purpose of Loan'] = master?.miscData?.purposeName ?? '-';
      formatData['Secured (Yes/No)'] = 'NO';
      formatData['Type of Security 1'] = 'HYPOTHECATION';
      formatData['Original Tenure (Months)'] = (
        loan?.approvedDuration / 30
      ).toFixed(2);
      formatData['Moratorium (In months)'] = 'NA';
      formatData['Residual Tenure (in months)'] = 'Need To change';
      formatData['Repayment Frequency'] = 'MONTHLY';
      formatData['Mode of repayment'] = payment[0]?.subSource ?? '-';
      formatData['EMI Amount'] = loan?.emiData[0]?.emi_amount ?? '-';
      formatData['First Installment Date'] =
        this.dateService.dateToReadableFormat(loan?.emiData[0]?.emi_date)
          .readableStr ?? '-';
      formatData['Last Installment Date'] =
        this.dateService.dateToReadableFormat(
          loan?.emiData[loan.emiData.length - 1]?.emi_date,
        ).readableStr ?? '-';
      formatData['Processing Fee-including GST'] = (
        loan?.processingFees +
        loan?.processingFees * 0.18
      ).toFixed(2);
      formatData['Doc Charge Amount including GST'] = (
        loan?.charges?.doc_charge_amt +
        loan?.charges?.doc_charge_amt * 0.18
      ).toFixed(2);
      formatData['Online Convenience Fees including GST'] = (
        loan?.charges?.insurance_fee +
        loan?.charges?.insurance_fee * 0.18
      ).toFixed(2);
      formatData['Total Premium Insurance'] = loan?.charges?.insurance_fee;
      formatData['Net Disbursement Amount'] = disburse?.amount
        ? disburse?.amount / 100
        : '-';
      formatData['Restructuring of Account '] = 'NO';

      const paidPrincipal = payment.reduce((total, el) => {
        const paymentDate = new Date(el.completionDate);
        return paymentDate <= month ? total + el.principalAmount : total;
      }, 0);

      formatData['Principal Outstanding (As on month end)'] =
        totalPrincipal - paidPrincipal;
      formatData['Total Accrued interest'] = totalInterest;
      formatData['Max DPD'] = max_dpd;
      formatData['Total overdue emi'] = overdueLoans.length;

      const fundAcc = bankDetails ? JSON.parse(bankDetails) : {};
      formatData['Disbursement Bank Name'] =
        fundAcc[disburse?.fundAccount] ?? '-';

      finalizedData.push(formatData);
    }

    userHistory.rows = finalizedData;

    if (download == true) {
      const rawExcelData = {
        sheets: ['local-reports'],
        data: [userHistory.rows],
        sheetName: 'Hypothecation.xlsx',
        needFindTuneKey: false,
        reportStore: true,
      };

      const url: any = await this.fileService.objectToExcelURL(rawExcelData);
      if (url?.message) return url;

      const updatedData = { downloadUrl: url, status: '1' };
      await this.reportHistoryRepo.updateRowData(updatedData, downloadId);
      return { fileUrl: url };
    } else {
      return userHistory;
    }
  }

  async zohoTallyDisbursementData(reqData) {
    const { body } = reqData;
    const { file } = body;
    if (!file) return kParamMissing('file');

    const fileName = file.filename ?? '-';
    const fileListData: any = await this.fileService.excelToArray(fileName, {});
    await this.fileService.removeFile(fileName);

    const loanIds = fileListData.map((data) => data.loanId);
    const allLoanData = await this.fetchLoanData(loanIds);
    const userIds = allLoanData.map((loan) => loan.userId);

    const userBankDataMap = await this.fetchUserBankData(userIds);
    const loanUserDataList = this.combineLoanAndUserData(
      allLoanData,
      userBankDataMap,
    );

    await this.syncContactsAndLedgers(loanUserDataList);

    const emiOptions = { where: { loanId: loanIds } };
    const emiAttr = [
      'emi_date',
      'emiNumber',
      'id',
      'loanId',
      'principalCovered',
    ];
    const emiList = await this.repoManager.getTableWhereData(
      EmiEntity,
      emiAttr,
      emiOptions,
    );
    if (emiList == k500Error) throw new Error();

    for (const journalData of fileListData) {
      const loanId = journalData.loanId;
      const loanData = loanUserDataList.find((el) => el.loanId == loanId);
      if (!loanData) throw new Error();

      await this.createJournal(journalData);

      const targetEmiList = emiList.filter((el) => el.loanId == loanId);
      for (let i = 0; i < targetEmiList.length; i++) {
        const emiData = targetEmiList[i];
        const emi_date = new Date(emiData.emi_date);

        const reqData = {
          customer_id: loanData.customer_id,
          emi_date,
          emiNumber: emiData.emiNumber,
          disbursement_date: loanData.loan_disbursement_date,
          loanId,
          principalCovered: emiData.principalCovered,
        };
        await this.createInvoice(reqData);
      }
    }
  }

  async fetchLoanData(loanIds) {
    const allLoanData = await this.repoManager.getTableWhereData(
      loanTransaction,
      ['id', 'userId', 'loan_disbursement_date'],
      { where: { id: loanIds } },
    );
    if (allLoanData == k500Error) throw new Error();
    return allLoanData;
  }

  async fetchUserBankData(userIds) {
    const allUserBankData = await this.repoManager.getTableWhereData(
      registeredUsers,
      ['id', 'email', 'fullName', 'phone'],
      { where: { id: userIds } },
    );
    if (allUserBankData == k500Error) throw new Error();

    const userBankDataMap = new Map();
    allUserBankData.forEach((user) => userBankDataMap.set(user.id, user));
    return userBankDataMap;
  }

  combineLoanAndUserData(allLoanData, userBankDataMap) {
    return allLoanData.map((loan) => {
      const userData = userBankDataMap.get(loan.userId) || {};
      return {
        loanId: loan.id,
        userData: { userId: loan.userId, ...userData },
        loan_disbursement_date: new Date(loan.loan_disbursement_date),
      };
    });
  }

  async syncContactsAndLedgers(loanUserDataList) {
    const url = `${nZohoBooksUrl}contacts?organization_id=${EnvConfig.zoho.org_id}`;
    const headers = {
      authorization: `Zoho-oauthtoken ${EnvConfig.zoho.accessToken}`,
    };

    for (const loanUser of loanUserDataList) {
      const { userData, loanId } = loanUser;

      const phone = this.cryptService.decryptPhone(userData.phone);
      const userExist = await this.isExistingUser(loanId);
      if (userExist == k500Error) throw new Error();

      let customer_id;

      // Creation -> New customer
      if (userExist.contacts?.length == 0) {
        const body = {
          contact_name: `${userData.fullName} ${loanId}`,
          contact_persons: [
            {
              email: `${loanId}${EnvConfig.emailDomain.companyEmailDomain2}`,
              phone: `${phone}`,
            },
          ],
        };
        await this.apiService.post(url, body, headers, null);
      } else {
        if (userExist.contacts.length > 1) throw new Error();
        customer_id = userExist.contacts[0].contact_id;
      }

      const index = loanUserDataList.findIndex((el) => el.loanId == loanId);
      if (index == -1) throw new Error();
      loanUserDataList[index].customer_id = customer_id;
    }
  }

  async isExistingUser(email) {
    const url = `${nZohoBooksUrl}contacts?organization_id=${EnvConfig.zoho.org_id}&email=${email}${EnvConfig.emailDomain.companyEmailDomain2}`;
    const headers = {
      authorization: `Zoho-oauthtoken ${EnvConfig.zoho.accessToken}`,
    };

    return this.apiService.get(url, null, headers, null);
  }

  async createLedger(loanData) {
    const userName = loanData?.userData?.fullName ?? '-';
    const loanId = loanData?.loanId;
    const url = `${nZohoBooksUrl}chartofaccounts?organization_id=${EnvConfig.zoho.org_id}`;
    const headers = {
      authorization: `Zoho-oauthtoken ${EnvConfig.zoho.accessToken}`,
    };
    const body = {
      account_name: `${userName} - ${loanId} (L-${loanId})`,
      account_type: 'other_current_asset',
      currency_id: '2147639000000000064',
      can_show_in_ze: false,
      parent_account_id: EnvConfig.zoho.book_acc_id_loan_recv,
      custom_fields: [],
    };

    const response = await this.apiService.post(url, body, headers, null);
    if (response == k500Error) throw new Error();
    if (!response.chart_of_account) throw new Error();
    if (!response.chart_of_account.account_id) throw new Error();
    return response.chart_of_account.account_id;
  }

  async createJournal(journal) {
    const userExist = await this.isExistingUser(journal.loanId);
    if (!userExist || !userExist.contacts || userExist.contacts.length === 0) {
      throw new Error();
    }

    const url = `${nZohoBooksUrl}journals?organization_id=${EnvConfig.zoho.org_id}`;
    const headers = {
      authorization: `Zoho-oauthtoken ${EnvConfig.zoho.accessToken}`,
    };

    const customer_id = userExist.contacts[0].contact_id;
    const currency_id = userExist.contacts[0].currency_id;

    let igst_amt = 0;
    let cgst_amt = 0;
    let sgst_amt = 0;
    if (journal.CGST != '-') cgst_amt += +journal.CGST;
    if (journal.SGST != '-') sgst_amt += +journal.SGST;
    if (journal.IGST != '-') igst_amt += +journal.IGST;

    let charges = 0;
    let doc_charges = 0;
    if (journal['Loan Documentation Charges'] != '-')
      doc_charges += journal['Loan Documentation Charges'];
    let online_conv_charges = 0;
    if (journal['Online Conveyance Charges'] != '-')
      online_conv_charges += journal['Online Conveyance Charges'];
    let risk_assessment_charge = 0;
    if (journal['Risk Assessment Fees'] != '-')
      risk_assessment_charge += journal['Risk Assessment Fees'];
    const processingFees =
      journal.approvedAmount * (journal.processingFeesPerc / 100);
    charges += processingFees;

    let care_Health_Insurance_Amt = 0;
    if (journal['Care_Health_Insurance_Amt'] != '-')
      care_Health_Insurance_Amt = journal['Care_Health_Insurance_Amt'];
    let acko_Insurance_Amt = 0;
    if (journal['Acko_Insurance_Amt'] != '-')
      acko_Insurance_Amt = journal['Acko_Insurance_Amt'];

    const lineItems = [
      // Loan  receivable
      {
        account_id: EnvConfig.zoho.book_acc_id_loan_recv,
        amount: journal?.approvedAmount?.toString(),
        debit_or_credit: 'debit',
        customer_id,
        description: 'New Loan Disbursal',
        item_order: 1,
      },
      // Processing Fees
      {
        account_id: EnvConfig.zoho.book_acc_id_processing_fees,
        amount: processingFees.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: 'Charges (Excluding GST)',
        item_order: 2,
      },
      // Document charges
      {
        account_id: EnvConfig.zoho.book_acc_id_doc_charges,
        amount: doc_charges.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: 'Charges (Excluding GST)',
        item_order: 3,
      },
      // Online convenience fees
      {
        account_id: EnvConfig.zoho.book_acc_id_online_conv_fees,
        amount: online_conv_charges.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: 'Charges (Excluding GST)',
        item_order: 4,
      },
      // Risk assessment fees
      {
        account_id: EnvConfig.zoho.book_acc_id_online_conv_fees,
        amount: risk_assessment_charge.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: 'Charges (Excluding GST)',
        item_order: 5,
      },
      // Insurance -> Care health
      {
        account_id: EnvConfig.zoho.book_acc_id_care_health,
        amount: care_Health_Insurance_Amt.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: 'Insurance (Inc GST)',
        item_order: 6,
      },
      // Insurance -> Acko
      {
        account_id: EnvConfig.zoho.book_acc_id_acko,
        amount: acko_Insurance_Amt.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: 'Insurance (Inc GST)',
        item_order: 7,
      },
      // Output IGST
      {
        account_id: EnvConfig.zoho.book_acc_id_igst_charge,
        amount: igst_amt.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: 'Applicable GST (18%)',
        item_order: 8,
      },
      // Output CGST
      {
        account_id: EnvConfig.zoho.book_acc_id_cgst_charge,
        amount: cgst_amt.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: 'Applicable CGST (9%)',
        item_order: 8,
      },
      // Output SGST
      {
        account_id: EnvConfig.zoho.book_acc_id_sgst_charge,
        amount: sgst_amt.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: 'Applicable SGST (9%)',
        item_order: 8,
      },
      // Bank account
      {
        account_id: EnvConfig.zoho.book_acc_id_bank_acc,
        amount: journal.disbursementAmount.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: 'YES BANK - 5882',
        item_order: 9,
      },
    ];

    // Filter items where the amount is greater than 0
    const filteredLineItems = lineItems.filter(
      (item) => parseFloat(item.amount) > 0,
    );

    const body = {
      journal_date: journal.disbursement_date.split('-').reverse().join('-'),
      reference_number: `${journal.netApproved_Ref_No}`,
      currency_id,
      notes: `Disbursement JV for LoanId - ${journal.loanId}`,
      journal_type: 'both',
      line_items: filteredLineItems,
      documents: [],
      status: 'published',
      custom_fields: [],
    };

    const response = await this.apiService.post(url, body, headers, null);
    if (response == k500Error) throw new Error();
  }

  async createInvoice(reqData: {
    customer_id: string;
    disbursement_date: Date;
    emi_date: Date;
    emiNumber: number;
    loanId: number;
    principalCovered: number;
  }) {
    let url = `${nZohoBooksUrl}invoices/editpage/fromcontacts?contact_id=${reqData.customer_id}&disable_settings=true&organization_id=${EnvConfig.zoho.org_id}`;
    const headers = {
      authorization: `Zoho-oauthtoken ${EnvConfig.zoho.accessToken}`,
    };
    const customerData = await this.apiService.get(url, null, headers, null);
    if (customerData == k500Error) throw new Error();

    const contactData = customerData.contact ?? {};
    const billing_address = contactData?.billing_address ?? {};
    const shipping_address = contactData?.shipping_address ?? {};
    const invoice_number = `P${reqData.emiNumber}-${reqData.loanId}`;
    const body = {
      invoice_number,
      reference_number: `O${reqData.emiNumber}-${reqData.loanId}`,
      payment_terms: -1,
      payment_terms_label: 'Custom',
      payment_options: {
        payment_gateways: [],
      },
      customer_id: reqData.customer_id,
      contact_persons: [],
      date: reqData.disbursement_date.toJSON().substring(0, 10),
      due_date: reqData.emi_date.toJSON().substring(0, 10),
      notes: '',
      notes_default: true,
      terms: '',
      is_inclusive_tax: false,
      line_items: [
        {
          item_order: 1,
          rate: `${reqData.principalCovered}`,
          description: `Principal due for EMI ${reqData.emiNumber}`,
          quantity: '1.00',
          discount: '0%',
          tax_id: '',
          project_id: '',
          tags: [],
          item_custom_fields: [],
        },
      ],
      allow_partial_payments: false,
      custom_fields: [],
      is_discount_before_tax: true,
      discount: 0,
      discount_type: 'entity_level',
      adjustment: '',
      adjustment_description: 'Adjustment',
      pricebook_id: '',
      template_id: EnvConfig.zoho.book_acc_invoice_template_id,
      project_id: '',
      documents: [],
      mail_attachments: [],
      billing_address_id: billing_address.address_id,
      shipping_address_id: shipping_address.address_id,
      subject_content: `Principal due for EMI ${reqData.emiNumber}`,
      is_tcs_amount_in_percent: true,
    };
    url = `${nZohoBooksUrl}invoices?organization_id=${EnvConfig.zoho.org_id}`;

    const response = await this.apiService.post(url, body, headers, null);
    if (response == k500Error) throw new Error();
    if (!response.invoice) throw new Error();

    const invoice_id = response.invoice?.invoice_id;
    url = `${nZohoBooksUrl}invoices/${invoice_id}/status/sent?organization_id=${EnvConfig.zoho.org_id}`;
    await this.apiService.post(url, {}, headers, null);
  }

  async getRefreshToken() {
    const client_id = '1000.P3AOOO3OQBKEL8TOOYP1QEWRITYF2R';
    const client_secret = '3b83c9967daf0f793befeb149540e279691266524d';
    const redirect_uri = 'http://www.zoho.com/books';
    const grant_type = 'refresh_token';
    const code =
      '**********************************************************************';

    const refreshToken =
      '**********************************************************************';

    const url = `https://accounts.zoho.in/oauth/v2/token?refresh_token=${refreshToken}&client_id=${client_id}&client_secret=${client_secret}&redirect_uri=${redirect_uri}&grant_type=${grant_type}`;

    console.log(url);

    const response = await this.apiService.post(url, null, null, null);
    return response.access_token;
  }

  async customTallyInterestIncome(reqData) {
    const loanIds = reqData.loanIds;
    if (!loanIds) return kParamMissing('loanIds');
    const readOnly = reqData.readOnly == 'true';

    const emiInc: SequelOptions = { model: EmiEntity };
    emiInc.attributes = ['emi_date', 'payment_done_date'];
    const include = [emiInc];

    const loanAttr = ['id'];
    const loanOptions = { include, where: { id: loanIds } };
    const loanList = await this.repoManager.getTableWhereData(
      loanTransaction,
      loanAttr,
      loanOptions,
    );
    if (loanList == k500Error) throw new Error();

    const target_dates = [];
    for (let index = 0; index < loanList.length; index++) {
      const loanData = loanList[index];
      const emiList = loanData.emiData ?? [];
      emiList.forEach((el) => {
        const emi_date = this.typeService.getGlobalDate(new Date(el.emi_date));
        target_dates.push(emi_date);
        if (el.payment_done_date) {
          const paid_date = this.typeService.getGlobalDate(
            new Date(el.payment_done_date),
          );
          target_dates.push(paid_date);
        }
      });
    }
    target_dates.sort((a, b) => a.getTime() - b.getTime());
    const startDate = target_dates[0];
    const endDate = target_dates[target_dates.length - 1];

    const data = await this.report.tallyInterestIncome({
      startDate,
      endDate,
      loanId: loanIds,
      rawList: true,
    });
    if (data?.message) return data;

    const finalizedList = [];
    for (let index = 0; index < data.length; index++) {
      const targetData = data[index];
      const loanId = targetData.loanId;

      for (let i = 0; i < 6; i++) {
        let emiInterest = targetData[`emiInterest${i + 1}`];
        if (!emiInterest) continue;
        emiInterest = +emiInterest.replace(/,/g, '');
        const reference_no = targetData[`I${i + 1}_Ref_No`];
        const emi_date = targetData.emi_date;
        const emiSpans = emi_date.split('/');
        const date = emiSpans[0].padStart(2, '0');
        const month = emiSpans[1].padStart(2, '0');
        const year = emiSpans[2];
        const target_date = `${year}-${month}-${date}`;
        finalizedList.push({
          due_interest: emiInterest,
          loanId,
          reference_no,
          target_date,
        });
      }
    }

    if (readOnly == true) {
      return { count: finalizedList.length, rows: finalizedList };
    }

    for (let index = 0; index < finalizedList.length; index++) {
      const finalizedData = finalizedList[index];
      finalizedData.loanId = await this.createInterestIncomeJournal(
        finalizedData,
      );
      if (index == 0) break;
    }

    return finalizedList;
  }

  async createInterestIncomeJournal(reqData) {
    const userExist = await this.isExistingUser(reqData.loanId);
    if (!userExist || !userExist.contacts || userExist.contacts.length === 0) {
      throw new Error();
    }

    const url = `${nZohoBooksUrl}journals?organization_id=${EnvConfig.zoho.org_id}`;
    const headers = {
      authorization: `Zoho-oauthtoken ${EnvConfig.zoho.accessToken}`,
    };

    const customer_id = userExist.contacts[0].contact_id;
    const currency_id = userExist.contacts[0].currency_id;

    const lineItems = [
      // Loan  receivable
      {
        account_id: EnvConfig.zoho.book_acc_id_loan_recv,
        amount: reqData?.due_interest?.toString(),
        debit_or_credit: 'debit',
        customer_id,
        description: `Interest income due for Loan id - ${reqData.loanId}`,
        item_order: 1,
      },
      // Loan  receivable
      {
        account_id: EnvConfig.zoho.book_acc_id_interest_income,
        amount: reqData?.due_interest?.toString(),
        debit_or_credit: 'credit',
        customer_id,
        description: `Interest income due for Loan id - ${reqData.loanId}`,
        item_order: 1,
      },
    ];

    // Filter items where the amount is greater than 0
    const filteredLineItems = lineItems.filter(
      (item) => parseFloat(item.amount) > 0,
    );

    const body = {
      journal_date: reqData.target_date,
      reference_number: `${reqData.reference_no}`,
      currency_id,
      notes: `Interest income due for Loan id - ${reqData.loanId}`,
      journal_type: 'both',
      line_items: filteredLineItems,
      documents: [],
      status: 'published',
      custom_fields: [],
    };

    const response = await this.apiService.post(url, body, headers, null);
    console.log(response?.message);
    if (response == k500Error) throw new Error();
  }

  async newAccountSection(reqData) {
    let { startDate, endDate } = reqData;

    // Default to previous day if dates not provided
    if (!startDate || !endDate) {
      let today = new Date();
      today.setDate(today.getDate() - 1);
      startDate = startDate || today;
      endDate = endDate || today;
    }

    startDate = this.typeService.getGlobalDate(startDate).toJSON();
    endDate = this.typeService.getGlobalDate(endDate ?? new Date()).toJSON();

    reqData.startDate = startDate;
    reqData.endDate = endDate;
    reqData.forAccounting = 'true';
    reqData.download = 'true';

    const tallyDisbursement = await this.report.tallyDisbursement(reqData);
    if (tallyDisbursement.message) return tallyDisbursement;

    const tallyInterestIncome = await this.report.tallyInterestIncome(reqData);
    if (tallyInterestIncome.message) return tallyInterestIncome;

    let tallyRepayment = await this.report.tallyRepayment(reqData);
    if (tallyRepayment.message) return tallyRepayment;

    const tallyRepaymentAndSettlement = [
      ...tallyRepayment.autoList,
      ...tallyRepayment.manualList,
      ...tallyRepayment.defaulterUser,
      ...tallyRepayment.manualDefaulterUser,
      ...tallyRepayment.defaulterUserGtr180,
      ...tallyRepayment.manualDefaulterUserGtr180,
    ];
    const disbursementSummaryData =
      this.summarizeDisbursementsByDate(tallyDisbursement);

    const repaymentSummaryData = this.summarizeRepaymentsByDate(
      tallyRepaymentAndSettlement,
    );
    const interestIncomeSummaryData =
      this.summarizeInterestIncomeByDate(tallyInterestIncome);

    const summaryByDate = {};
    const tableData = [];

    tallyDisbursement.forEach((disbursement) => {
      const date = this.normalizeDate(disbursement.disbursement_date);
      const disbursedAmount =
        parseFloat(disbursement.disbursementAmount.replace(/[₹,]/g, '')) || 0;

      if (!summaryByDate[date]) {
        summaryByDate[date] = {
          Date: date,
          'Disbursed Amount': 0,
          'Repayment Amount': 0,
          'Interest Income': 0,
          Expense: 0,
        };
      }

      summaryByDate[date]['Disbursed Amount'] += disbursedAmount;
    });

    tallyRepaymentAndSettlement.forEach((repayment) => {
      const date = this.normalizeDate(repayment.createdAt);
      const repaidAmount =
        parseFloat(repayment.repaidAmount.replace(/[₹,]/g, '')) || 0;

      if (!summaryByDate[date]) {
        summaryByDate[date] = {
          Date: date,
          'Disbursed Amount': 0,
          'Repayment Amount': 0,
          'Interest Income': 0,
          Expense: 0,
        };
      }

      summaryByDate[date]['Repayment Amount'] += repaidAmount;
    });

    tallyInterestIncome.forEach((interestEntry) => {
      const date = this.normalizeDate(interestEntry.emi_date);
      let totalInterestIncome = 0;
      for (let i = 1; i <= 12; i++) {
        totalInterestIncome +=
          parseFloat(
            typeof interestEntry[`emiInterest${i}`] === 'string'
              ? interestEntry[`emiInterest${i}`].replace(/[₹,]/g, '')
              : interestEntry[`emiInterest${i}`] || 0,
          ) || 0;
      }

      if (!summaryByDate[date]) {
        summaryByDate[date] = {
          Date: date,
          'Disbursed Amount': 0,
          'Repayment Amount': 0,
          'Interest Income': 0,
          Expense: 0,
        };
      }

      summaryByDate[date]['Interest Income'] += totalInterestIncome;
    });

    // Ensure all numerical data in summaryByDate is fixed to 2 decimal places
    Object.keys(summaryByDate).forEach((date) => {
      const summary = summaryByDate[date];

      summary['Disbursed Amount'] = parseFloat(
        summary['Disbursed Amount'].toFixed(2),
      );
      summary['Repayment Amount'] = parseFloat(
        summary['Repayment Amount'].toFixed(2),
      );
      summary['Interest Income'] = parseFloat(
        summary['Interest Income'].toFixed(2),
      );
      summary['Expense'] = parseFloat(summary['Expense'].toFixed(2));
    });

    let currentDate = new Date(startDate);
    const end = new Date(endDate);

    while (currentDate <= end) {
      const dateKey = this.normalizeDate(
        `${('0' + currentDate.getDate()).slice(-2)}/${(
          '0' +
          (currentDate.getMonth() + 1)
        ).slice(-2)}/${currentDate.getFullYear()}`,
      );

      if (!summaryByDate[dateKey]) {
        summaryByDate[dateKey] = {
          Date: dateKey,
          'Disbursed Amount': 0,
          'Repayment Amount': 0,
          'Interest Income': 0,
          Expense: 0,
        };
      }

      tableData.push(summaryByDate[dateKey]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    for (let i = 0; i < tableData.length; i++) {
      const element = tableData[i];
      const [day, month, year] = element['Date'].split('/');
      const formattedDate = `${day}-${month}-${year}`;

      const disbursementDataForDate =
        disbursementSummaryData.disbursementSummaryArray.find(
          (disbursement) => disbursement.date.trim() === element['Date'].trim(),
        );
      const disbursementListForDate =
        disbursementSummaryData.disbursementDataArray.find(
          (disbursement) => disbursement.date.trim() === element['Date'].trim(),
        );
      const disbursements = disbursementListForDate?.disbursements || [];

      const repaymentDataForDate =
        repaymentSummaryData.repaymentSummaryArray.find(
          (repayment) => repayment.date.trim() === element['Date'].trim(),
        );
      const repaymentListForDate = repaymentSummaryData.repaymentDataArray.find(
        (repayment) => repayment.date.trim() === element['Date'].trim(),
      );
      const repayments = repaymentListForDate?.repayments || [];

      const interestIncomeDataForDate =
        interestIncomeSummaryData.interestIncomeSummaryArray.find(
          (interest) => interest.date.trim() === element['Date'].trim(),
        );
      const interestIncomeListForDate =
        interestIncomeSummaryData.interestIncomeDataArray.find(
          (interest) => interest.date.trim() === element['Date'].trim(),
        );
      const interestIncomes = interestIncomeListForDate?.interestIncomes || [];

      const disbursementFileURL = disbursements.length
        ? await this.generateExcelAndUpload(
            disbursements,
            `Disbursement Summary ${formattedDate}`,
          )
        : '';
      const repaymentFileURL = repayments.length
        ? await this.generateExcelAndUpload(
            repayments,
            `Repayment Summary ${formattedDate}`,
          )
        : '';

      const interestIncomeFileURL = interestIncomes.length
        ? await this.generateExcelAndUpload(
            interestIncomes,
            `Int Income ${formattedDate}`,
          )
        : '';

      const createData = {
        reportDate: this.typeService
          .getGlobalDate(new Date(`${year}-${month}-${day}`))
          .toJSON(),
        summaryData: element,
        expense: 0,
        disbursementData: disbursementDataForDate || {},
        repaymentData: repaymentDataForDate || {},
        interestIncomeData: interestIncomeDataForDate || {},
        disbursementListURL: disbursementFileURL || '',
        repaymentListURL: repaymentFileURL || '',
        interestIncomeListURL: interestIncomeFileURL || '',
      };
      await this.repoManager.findOrCreate(
        accountSectionSummary,
        {
          reportDate: this.typeService
            .getGlobalDate(new Date(`${year}-${month}-${day}`))
            .toJSON(),
        },
        createData,
      );
    }

    return tableData;
  }

  summarizeDisbursementsByDate(disbursements) {
    const disbursementSummary = {};
    const disbursementDataByDate = {};

    disbursements.forEach((disbursement) => {
      const date = this.normalizeDate(disbursement.disbursement_date);

      if (!disbursementSummary[date]) {
        disbursementSummary[date] = {
          'Loan Count': 0,
          disbursementAmount: 0,
          stampFees: 0,
          stampDutyIncome: 0,
          Care_Health_Insurance_Amt: 0,
          Acko_Insurance_Amt: 0,
          processingFessIncome: 0,
          'Loan Documentation Charges': 0,
          'Online Conveyance Charges': 0,
          'Risk Assessment Fees': 0,
          CGST: 0,
          SGST: 0,
          approvedAmount: 0,
          'Insurance Premium': 0,
          IGST: 0,
        };
      }

      if (!disbursementDataByDate[date]) {
        disbursementDataByDate[date] = [];
      }

      const parseValue = (val) =>
        val == null
          ? 0
          : (typeof val === 'string'
              ? parseFloat(val.replace(/[₹,-]/g, ''))
              : val) || 0;

      disbursementSummary[date]['Loan Count'] += 1;
      disbursementSummary[date]['disbursementAmount'] += parseValue(
        disbursement.disbursementAmount,
      );
      disbursementSummary[date]['stampFees'] += parseValue(
        disbursement.stampFees,
      );
      disbursementSummary[date]['stampDutyIncome'] += parseValue(
        disbursement.stampDutyIncome,
      );
      disbursementSummary[date]['Care_Health_Insurance_Amt'] += parseValue(
        disbursement.Care_Health_Insurance_Amt,
      );
      disbursementSummary[date]['Acko_Insurance_Amt'] += parseValue(
        disbursement.Acko_Insurance_Amt,
      );
      disbursementSummary[date]['processingFessIncome'] += parseValue(
        disbursement.processingFessIncome,
      );
      disbursementSummary[date]['Loan Documentation Charges'] += parseValue(
        disbursement['Loan Documentation Charges'],
      );
      disbursementSummary[date]['Online Conveyance Charges'] += parseValue(
        disbursement['Online Conveyance Charges'],
      );
      disbursementSummary[date]['Risk Assessment Fees'] += parseValue(
        disbursement['Risk Assessment Fees'],
      );
      disbursementSummary[date]['CGST'] += parseValue(
        disbursement['CGST'] || 0,
      );
      disbursementSummary[date]['SGST'] += parseValue(
        disbursement['SGST'] || 0,
      );
      disbursementSummary[date]['approvedAmount'] += parseValue(
        disbursement.approvedAmount,
      );
      disbursementSummary[date]['Insurance Premium'] += parseValue(
        disbursement['Insurance Premium'],
      );
      disbursementSummary[date]['IGST'] += parseValue(disbursement['IGST']);
      disbursement.processingFeesPerc = disbursement.processingFeesPerc + '%';
      disbursementDataByDate[date].push(disbursement);
    });

    const disbursementSummaryArray = Object.keys(disbursementSummary).map(
      (date) => ({
        date,
        ...disbursementSummary[date],
      }),
    );

    const disbursementDataArray = Object.keys(disbursementDataByDate).map(
      (date) => ({
        date,
        disbursements: disbursementDataByDate[date],
      }),
    );

    return { disbursementSummaryArray, disbursementDataArray };
  }

  summarizeRepaymentsByDate(repayments) {
    const repaymentSummary = {};
    const repaymentDataByDate = {};
    const allPaymentTypes = new Set();

    repayments.forEach((repayment) => {
      const paymentType = repayment?.payment_type;
      allPaymentTypes.add(paymentType);
    });

    repayments.forEach((repayment) => {
      const date = this.normalizeDate(repayment.createdAt);
      const paymentType = repayment?.payment_type;

      if (!repaymentSummary[date]) {
        repaymentSummary[date] = {
          'Repayment Count': 0,
          repaidAmount: 0,
        };

        allPaymentTypes.forEach((type) => {
          repaymentSummary[date][`Amount_${type}`] = 0;
        });

        for (let i = 1; i <= 12; i++) {
          repaymentSummary[date][`emiPrincipal${i}`] = 0;
          repaymentSummary[date][`emiInterest${i}`] = 0;
          repaymentSummary[date][`emi${i}_penalty`] = 0;
          repaymentSummary[date][`Def_Int${i} Amt`] = 0;
          repaymentSummary[date][`Bounce Charges${i}_Amt`] = 0;
          repaymentSummary[date][`Penal Charges${i} Amt`] = 0;
          repaymentSummary[date][`Waiver_Principal_${i}`] = 0;
          repaymentSummary[date][`Waiver_Int${i}`] = 0;
          repaymentSummary[date][`Waiver_Penalty${i}`] = 0;
          repaymentSummary[date][`Waiver_Def_Int${i}`] = 0;
          repaymentSummary[date][`Waiver_BounceCharges${i}`] = 0;
          repaymentSummary[date][`Waiver_PenalCharge${i}`] = 0;
        }

        repaymentSummary[date]['Foreclosure Charges'] = 0;
        repaymentSummary[date]['Legal Charges'] = 0;
        repaymentSummary[date]['CGST'] = 0;
        repaymentSummary[date]['SGST'] = 0;
        repaymentSummary[date]['IGST'] = 0;
        repaymentSummary[date]['Waiver_LegalCharge'] = 0;
        repaymentSummary[date]['TOTALwaiverAmount'] = 0;
      }

      repaymentSummary[date]['Repayment Count'] += 1;
      repaymentSummary[date]['repaidAmount'] +=
        parseFloat(repayment.repaidAmount.replace(/[₹,]/g, '')) || 0;

      repaymentSummary[date][`Amount_${paymentType}`] +=
        parseFloat(repayment.repaidAmount.replace(/[₹,]/g, '')) || 0;

      for (let i = 1; i <= 12; i++) {
        repaymentSummary[date][`emiPrincipal${i}`] +=
          parseFloat(repayment[`emiPrincipal${i}`]) || 0;
        repaymentSummary[date][`emiInterest${i}`] +=
          parseFloat(repayment[`emiInterest${i}`]) || 0;
        repaymentSummary[date][`emi${i}_penalty`] +=
          parseFloat(repayment[`emi${i}_penalty`]) || 0;
        repaymentSummary[date][`Def_Int${i} Amt`] +=
          parseFloat(repayment[`Def_Int${i} Amt`] || 0) || 0;
        repaymentSummary[date][`Bounce Charges${i}_Amt`] +=
          parseFloat(repayment[`Bounce Charges${i}_Amt`] || 0) || 0;
        repaymentSummary[date][`Penal Charges${i} Amt`] +=
          parseFloat(repayment[`Penal Charges${i} Amt`] || 0) || 0;

        repaymentSummary[date][`Waiver_Principal_${i}`] +=
          parseFloat(repayment[`Waiver_Principal_${i}`] || 0) || 0;
        repaymentSummary[date][`Waiver_Int${i}`] +=
          parseFloat(repayment[`Waiver_Int${i}`] || 0) || 0;
        repaymentSummary[date][`Waiver_Penalty${i}`] +=
          parseFloat(repayment[`Waiver_Penalty${i}`] || 0) || 0;
        repaymentSummary[date][`Waiver_Def_Int${i}`] +=
          parseFloat(repayment[`Waiver_Def_Int${i}`] || 0) || 0;
        repaymentSummary[date][`Waiver_BounceCharges${i}`] +=
          parseFloat(repayment[`Waiver_BounceCharges${i}`] || 0) || 0;
        repaymentSummary[date][`Waiver_PenalCharge${i}`] +=
          parseFloat(repayment[`Waiver_PenalCharge${i}`] || 0) || 0;
      }

      repaymentSummary[date]['Foreclosure Charges'] +=
        parseFloat(repayment['Foreclosure Charges']) || 0;
      repaymentSummary[date]['Legal Charges'] +=
        parseFloat(repayment['Legal Charges']) || 0;
      repaymentSummary[date]['CGST'] += parseFloat(repayment.CGST || 0);
      repaymentSummary[date]['SGST'] += parseFloat(repayment.SGST || 0);
      repaymentSummary[date]['Waiver_LegalCharge'] +=
        parseFloat(repayment['Waiver_LegalCharge']) || 0;
      repaymentSummary[date]['TOTALwaiverAmount'] += parseFloat(
        repayment['TOTALwaiverAmount'] || 0,
      );
      repaymentSummary[date]['IGST'] += parseFloat(repayment['IGST'] || 0);

      if (!repaymentDataByDate[date]) {
        repaymentDataByDate[date] = [];
      }
      repaymentDataByDate[date].push(repayment);
    });

    const repaymentSummaryArray = Object.keys(repaymentSummary).map((date) => {
      const {
        'Foreclosure Charges': foreclosureCharges,
        'Legal Charges': legalCharges,
        CGST,
        SGST,
        IGST,
        Waiver_LegalCharge: waiverLegalCharge,
        TOTALwaiverAmount,
        ...rest
      } = repaymentSummary[date];

      return {
        date,
        ...rest,
        'Foreclosure Charges': foreclosureCharges,
        'Legal Charges': legalCharges,
        CGST,
        SGST,
        Waiver_LegalCharge: waiverLegalCharge,
        TOTALwaiverAmount,
        IGST,
      };
    });

    const repaymentDataArray = Object.keys(repaymentDataByDate).map((date) => ({
      date,
      repayments: repaymentDataByDate[date],
    }));

    return { repaymentSummaryArray, repaymentDataArray };
  }

  summarizeInterestIncomeByDate(interestIncomes) {
    const interestIncomeSummary = {};
    const interestIncomeDataByDate = {};

    interestIncomes.forEach((income) => {
      let date = this.normalizeDate(income.emi_date);

      if (!interestIncomeSummary[date]) {
        interestIncomeSummary[date] = {
          'Interest Count': 0,
          emiInterest1: 0,
          emiInterest2: 0,
          emiInterest3: 0,
          emiInterest4: 0,
          emiInterest5: 0,
          emiInterest6: 0,
          emiInterest7: 0,
          emiInterest8: 0,
          emiInterest9: 0,
          emiInterest10: 0,
          emiInterest11: 0,
          emiInterest12: 0,
        };
      }

      if (!interestIncomeDataByDate[date]) {
        interestIncomeDataByDate[date] = [];
      }

      interestIncomeSummary[date]['Interest Count'] += 1;

      // Loop through all 12 EMI interest fields and add them to their respective fields
      for (let i = 1; i <= 12; i++) {
        const interestField = `emiInterest${i}`;
        interestIncomeSummary[date][interestField] += parseFloat(
          typeof income[interestField] === 'string'
            ? income[interestField].replace(/[₹,]/g, '')
            : income[interestField] || 0,
        );
      }

      interestIncomeDataByDate[date].push(income);
    });

    const interestIncomeSummaryArray = Object.keys(interestIncomeSummary).map(
      (date) => ({
        date,
        ...interestIncomeSummary[date],
      }),
    );

    const interestIncomeDataArray = Object.keys(interestIncomeDataByDate).map(
      (date) => ({
        date,
        interestIncomes: interestIncomeDataByDate[date],
      }),
    );

    return { interestIncomeSummaryArray, interestIncomeDataArray };
  }

  async getAccountSummary(reqData) {
    const type = reqData?.type ?? 'Normal';
    const downloadOption = reqData?.downloadOption ?? 'Summary';
    const download = reqData?.download ?? false;

    const attributes = ['reportDate', 'summaryData'];

    // Adjust attributes based on the type
    if (type === 'Disbursement') {
      attributes.push('disbursementData', 'disbursementListURL');
    } else if (type === 'Repayment') {
      attributes.push('repaymentData', 'repaymentListURL');
    } else if (type === 'Interest Income') {
      attributes.push('interestIncomeData', 'interestIncomeListURL');
    } else if (type !== 'Normal') {
      throw new Error('Invalid type');
    }

    let { startDate, endDate } = reqData;

    startDate = this.typeService
      .getGlobalDate(startDate ?? new Date())
      .toJSON();
    endDate = this.typeService.getGlobalDate(endDate ?? new Date()).toJSON();

    const options = {
      where: {
        reportDate: {
          [Op.gte]: startDate,
          [Op.lte]: endDate,
        },
      },
      order: [['reportDate', 'asc']],
    };

    const data = await this.repoManager.getTableWhereData(
      accountSectionSummary,
      attributes,
      options,
    );

    if (data == k500Error) throw new Error();

    const localPath = './upload/accountSummary';

    if (type !== 'Normal') {
      for (let i = 0; i < data.length; i++) {
        const element = data[i];

        if (type === 'Disbursement' && element.disbursementListURL) {
          const disbursementData = await this.report.processExcelData(
            element.disbursementListURL,
            '',
            localPath,
          );
          if (disbursementData?.message) return disbursementData;
          data[i]['disbursementList'] = disbursementData;
        }

        if (type === 'Repayment' && element.repaymentListURL) {
          const repaymentData = await this.report.processExcelData(
            element.repaymentListURL,
            '',
            localPath,
          );
          if (repaymentData?.message) return repaymentData;
          data[i]['repaymentList'] = repaymentData;
        }

        if (type === 'Interest Income' && element.interestIncomeListURL) {
          const interestIncomeData = await this.report.processExcelData(
            element.interestIncomeListURL,
            '',
            localPath,
          );
          if (interestIncomeData?.message) return interestIncomeData;
          data[i]['interestIncomeList'] = interestIncomeData;
        }
      }
    }

    // Prepare formatted data based on the type
    const formattedData = data.map((item) => {
      if (type === 'Disbursement') {
        return {
          ...item.summaryData,
          summaryDetails: item.disbursementData || {},
          allDetails: item.disbursementList || [],
        };
      } else if (type === 'Repayment') {
        return {
          ...item.summaryData,
          summaryDetails: item.repaymentData || {},
          allDetails: item.repaymentList || [],
        };
      } else if (type === 'Interest Income') {
        return {
          ...item.summaryData,
          summaryDetails: item.interestIncomeData || {},
          allDetails: item.interestIncomeList || [],
        };
      } else if (type === 'Normal') {
        return {
          ...item.summaryData,
        };
      }
    });

    if (download) {
      let sheets = ['Summary Data'];

      let dataForExcel = [];
      dataForExcel.push(data.map((item) => item.summaryData));

      if (downloadOption === 'Summary & Details') {
        if (type === 'Disbursement') {
          sheets.push('Disbursement Data');
          dataForExcel.push([data[0].disbursementData]);
        } else if (type === 'Repayment') {
          sheets.push('Repayment Data');
          dataForExcel.push([data[0].repaymentData]);
        } else if (type === 'Interest Income') {
          sheets.push('InterestIncome Data');
          dataForExcel.push([data[0].interestIncomeData]);
        }
      } else if (downloadOption === 'All the details') {
        if (type === 'Disbursement') {
          sheets.push('Disbursement Data', 'Disbursement List');
          dataForExcel.push(
            [data[0].disbursementData],
            data[0].disbursementList,
          );
        } else if (type === 'Repayment') {
          sheets.push('Repayment Data', 'Repayment List');
          dataForExcel.push([data[0].repaymentData], data[0].repaymentList);
        } else if (type === 'Interest Income') {
          sheets.push('InterestIncome Data', 'InterestIncome List');
          dataForExcel.push(
            [data[0].interestIncomeData],
            data[0].interestIncomeList,
          );
        }
      }

      let fileURL = '';
      const path = `${downloadOption}.xlsx`;
      const rawExcelData = {
        sheets,
        data: dataForExcel,
        sheetName: path,
        needFindTuneKey: false,
      };

      const excelResponse: any = await this.fileService.objectToExcel(
        rawExcelData,
      );
      if (excelResponse?.message) return excelResponse;

      if (excelResponse.filePath) {
        fileURL = await this.fileService.uploadFile(
          excelResponse.filePath,
          'reports',
          'xlsx',
        );
      }

      return { fileURL };
    }

    return formattedData;
  }

  async updateExpense(reqData) {
    const { startDate, expense: reqExpense } = reqData;

    if (!startDate) return kParamMissing('startDate');

    let expense = parseFloat(reqExpense) || 0;

    const options = {
      where: {
        reportDate: startDate,
      },
    };

    const data = await this.repoManager.getRowWhereData(
      accountSectionSummary,
      ['expense', 'id', 'summaryData'],
      options,
    );

    if (data == k500Error) throw new Error();

    expense += parseFloat(data?.expense ?? 0);

    data.summaryData.Expense = expense;

    await this.repoManager.updateRowData(
      accountSectionSummary,
      { expense, summaryData: data.summaryData },
      data.id,
    );

    return { ...data, expense, summaryData: data.summaryData };
  }

  async generateExcelAndUpload(data, sheetName) {
    try {
      let fileURL = '';
      const path = `${sheetName}.xlsx`;
      const rawExcelData = {
        sheets: [sheetName],
        data: [data],
        sheetName: path,
        needFindTuneKey: false,
      };
      const excelResponse: any = await this.fileService.objectToExcel(
        rawExcelData,
      );
      if (excelResponse?.message) return excelResponse;
      if (excelResponse.filePath) {
        fileURL = await this.fileService.uploadFile(
          excelResponse.filePath,
          `reports_${sheetName}`,
          'xlsx',
        );
      }
      return fileURL;
    } catch (error) {
      console.log(error);
    }
  }

  normalizeDate(dateString) {
    if (dateString.includes('-')) {
      const [day, month, year] = dateString.split('-');
      return `${day}/${month}/${year}`;
    }
    if (dateString.includes('/')) {
      const [day, month, year] = dateString.split('/');
      return `${('0' + day).slice(-2)}/${('0' + month).slice(-2)}/${year}`;
    }
    return '';
  }

  private getMonthStartAndEnd(monthName: string, year: number) {
    const monthIndex = new Date(`${monthName} 1, ${year}`).getMonth();
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    const startDate = new Date(year, monthIndex, 1);
    const endDate =
      currentYear === year && currentMonth === monthIndex
        ? today
        : new Date(year, monthIndex + 1, 0);

    return { startDate, endDate };
  }

  findRefundAndFallbackMatches(transList) {
    const usedTransactionIds = new Set();
    const usedRefundUTRs = new Set();
    const matches = [];

    // Sort transactions by completion date
    const sortedTransList = [...transList].sort(
      (a, b) =>
        new Date(a.completionDate).getTime() -
        new Date(b.completionDate).getTime(),
    );

    // Process each refund transaction
    sortedTransList.forEach((refundTransaction) => {
      if (refundTransaction.type !== 'REFUND') {
        return;
      }
      // Check if the refund has a matching transaction
      const hasMatchingRefund = sortedTransList.some((t) => {
        if (
          t.paidAmount === -1 * refundTransaction.paidAmount &&
          !usedRefundUTRs.has(t.utr) &&
          t.principalAmount == -1 * (refundTransaction.principalAmount ?? 0) &&
          t.interestAmount == -1 * (refundTransaction.interestAmount ?? 0) &&
          t.penaltyAmount == -1 * (refundTransaction.penaltyAmount ?? 0) &&
          t.regInterestAmount ==
            -1 * (refundTransaction.regInterestAmount ?? 0) &&
          t.bounceCharge == -1 * (refundTransaction.bounceCharge ?? 0) &&
          t.penalCharge == -1 * (refundTransaction.penalCharge ?? 0) &&
          t.legalCharge == -1 * (refundTransaction.legalCharge ?? 0) &&
          t.forClosureAmount ==
            -1 * (refundTransaction.forClosureAmount ?? 0) &&
          t.sgstOnBounceCharge ==
            -1 * (refundTransaction.sgstOnBounceCharge ?? 0) &&
          t.cgstOnBounceCharge ==
            -1 * (refundTransaction.cgstOnBounceCharge ?? 0) &&
          t.igstOnBounceCharge ==
            -1 * (refundTransaction.igstOnBounceCharge ?? 0) &&
          t.cgstOnPenalCharge ==
            -1 * (refundTransaction.cgstOnPenalCharge ?? 0) &&
          t.sgstOnPenalCharge ==
            -1 * (refundTransaction.sgstOnPenalCharge ?? 0) &&
          t.igstOnPenalCharge ==
            -1 * (refundTransaction.igstOnPenalCharge ?? 0) &&
          t.cgstOnLegalCharge ==
            -1 * (refundTransaction.cgstOnLegalCharge ?? 0) &&
          t.sgstOnLegalCharge ==
            -1 * (refundTransaction.sgstOnLegalCharge ?? 0) &&
          t.igstOnLegalCharge ==
            -1 * (refundTransaction.igstOnLegalCharge ?? 0) &&
          t.cgstForClosureCharge ==
            -1 * (refundTransaction.cgstForClosureCharge ?? 0) &&
          t.sgstForClosureCharge ==
            -1 * (refundTransaction.sgstForClosureCharge ?? 0) &&
          t.igstForClosureCharge ==
            -1 * (refundTransaction.igstForClosureCharge ?? 0)
        ) {
          usedRefundUTRs.add(t.utr);
          usedTransactionIds.add(t.id);
          usedTransactionIds.add(refundTransaction.id);
          return true;
        }
        return false;
      });
      // Skip if there is a matching refund
      if (hasMatchingRefund) {
        return;
      }

      // Adjust refund's paid amount by excluding principal and interest
      const adjustedRefundAmount =
        refundTransaction.paidAmount -
        (refundTransaction.principalAmount ?? 0) -
        (refundTransaction.interestAmount ?? 0);

      // Filter for eligible preceding transactions
      const eligibleTransactions = sortedTransList.filter(
        (t) =>
          t.type !== 'REFUND' &&
          !usedTransactionIds.has(t.id) &&
          new Date(t.completionDate) <=
            new Date(refundTransaction.completionDate) &&
          t.paidAmount - (t.principalAmount ?? 0) - (t.interestAmount ?? 0) >=
            Math.abs(adjustedRefundAmount),
      );

      // Find the nearest match
      const nearestMatch = eligibleTransactions.sort((a, b) => {
        const timeDiffA =
          new Date(refundTransaction.completionDate).getTime() -
          new Date(a.completionDate).getTime();
        const timeDiffB =
          new Date(refundTransaction.completionDate).getTime() -
          new Date(b.completionDate).getTime();
        return timeDiffA - timeDiffB;
      })[0];

      if (nearestMatch) {
        // Mark both transactions as used
        usedTransactionIds.add(nearestMatch.id);
        usedTransactionIds.add(refundTransaction.id);

        // Record the match
        matches.push({
          refund: {
            ...refundTransaction,
            paidAmount: adjustedRefundAmount,
          },
          match: nearestMatch,
        });
      }
    });

    return matches;
  }

  async manuallyLedgerCreation(data) {
    const loanId = data?.loanId;
    if (!loanId) return kParamMissing('loanId');
    const hasIGST = data?.hasIGST ?? false;
    const cgst = data?.cgst ?? 0;
    const sgst = data?.sgst ?? 0;
    const igst = data?.igst ?? 0;

    const loanData = await this.ledgerLoanData(loanId);
    data.overdueAmount = 0;
    data.overdueCharge = 0;

    const processingFees =
      loanData?.loanFees -
      (loanData?.charges?.insurance_fee || 0) -
      (loanData?.charges?.doc_charge_amt || 0) -
      (loanData?.charges?.gst_amt || 0);

    let firstEMIDate = '';
    let lastEMIDate = '';

    const nbfcLogo = EnvConfig.url.nbfcLogo;
    const rbiRegisterationNo = EnvConfig.nbfc.nbfcRegistrationNumber;
    const cinNo = EnvConfig.nbfc.nbfcCINNumber;
    const nbfcAddress = EnvConfig.nbfc.nbfcAddress;
    const helpContact = EnvConfig.number.helpContact;
    const infoEmail = EnvConfig.mail.suppportMail;
    const inApp = loanData?.inApp;
    // #01 -> Basic details
    const basicDetails: any = await this.prepareBasicDetails(
      loanData,
      processingFees,
    );

    const { totalRemaining } = await this.loanService.getEMIDetails({
      loanId: loanData?.id,
    });

    const { disbursementCharges, balance } =
      await this.prepareDisbursementDetails(loanData);

    let today = this.typeService.getGlobalDate(new Date());
    const todayTime = today.getTime();
    let sortedList = this.prepareLedgerArray(data);
    let emiAmount = 0;
    let overdue = 0;
    let hasEqualPI = true;
    let previousPrincipal = null;
    let delayDays = 0;

    for (let index = 0; index < loanData?.emiData.length; index++) {
      const emiData = loanData?.emiData[index];
      const emiDate = this.typeService.getGlobalDate(
        new Date(emiData.emi_date),
      );
      let principal = emiData.principalCovered ?? 0;

      delayDays =
        emiData?.penalty_days > delayDays ? emiData?.penalty_days : delayDays;

      if (previousPrincipal !== null) {
        if (Math.abs(principal - previousPrincipal) > 50) {
          hasEqualPI = false;
        }
      }
      previousPrincipal = principal;

      if (emiData?.emiNumber == 1)
        firstEMIDate = this.dateService.dateToReadableFormat(
          new Date(emiData?.emi_date),
        ).readableStr;
      if (emiData?.partOfemi == 'LAST')
        lastEMIDate = this.dateService.dateToReadableFormat(
          new Date(emiData?.emi_date),
        ).readableStr;
      const emiTime = emiDate.getTime();

      const isPaid = emiData.payment_status === '1';

      const isPastEMI = todayTime > emiTime;
      if (isPastEMI && !isPaid) {
        overdue++;

        // Call the helper function
        const { overdueAmount, overdueCharge } = this.calculateOverdue(
          emiData,
          loanData?.transList,
        );
        // Update data object
        data.overdueAmount += this.typeService.manageAmount(overdueAmount);
        data.overdueCharge += this.typeService.manageAmount(overdueCharge);
      }
      emiAmount +=
        (emiData?.principalCovered ?? 0) + (emiData?.interestCalculate ?? 0);
    }

    let loanDueStatus = '';
    if (delayDays == 0) loanDueStatus = 'Regular';
    if (delayDays > 0) loanDueStatus = 'Overdue';
    if (loanData?.loanStatus == 'Complete') loanDueStatus = 'Closed';
    basicDetails.emiAmount = emiAmount.toFixed(2);
    basicDetails.overdue = overdue;
    basicDetails.loanDueStatus = loanDueStatus;

    let generatedDate = this.dateService.dateToReadableFormat(today);
    const prepData = {
      basicDetails,
      startDate: this.typeService.getDateFormatted(
        loanData?.loan_disbursement_date,
      ),
      endDate: this.typeService.getDateFormated(
        loanData?.loanCompletionDate ? loanData?.loanCompletionDate : today,
      ),
      disDetails: disbursementCharges,
      repayDetails: sortedList,
      nbfcLogo,
      rbiRegisterationNo,
      cinNo,
      nbfcAddress,
      helpContact,
      infoEmail,
      generatedDate: generatedDate.readableStr,
      overdueAmount: (data?.overdueAmount).toFixed(2),
      totalRemaining: totalRemaining.toFixed(2),
      overdueCharge: (data?.overdueCharge).toFixed(2),
      firstEMIDate,
      lastEMIDate,
      oneEmiAmount: (
        loanData?.emiData[0]?.principalCovered +
        loanData?.emiData[0]?.interestCalculate
      ).toFixed(2),
      emiCount: loanData?.emiData.length,
      foreclosureCharge: GLOBAL_CHARGES.FORECLOSURE_PERC,
      hasEqualPI,
      hasIGST,
      cgst,
      sgst,
      igst,
      gstTotal: (+cgst + +sgst + +igst).toFixed(2),
    };

    const page1 = await this.fileService.hbsHandlebars(kLedgerStatementPath1, {
      ...prepData,
      ledgerFields,
      ledgerDisbFields,
    });

    if (page1 == k500Error) return page1;
    const preparedData = [page1];
    //Individual PDF page creation
    let currentPage;
    const nextPages: string[] = [];
    for (let index = 0; index < preparedData.length; index++) {
      const pageData = preparedData[index];
      const pdfPath = await this.fileService.dataToPDF(pageData);
      if (pdfPath == k500Error) return kInternalError;
      if (index == 0) currentPage = pdfPath.toString();
      else nextPages.push(pdfPath.toString());
    }
    const ledgerPath = await this.fileService.mergeMultiplePDF(
      nextPages,
      currentPage,
    );
    if (ledgerPath == k500Error) return kInternalError;
    const pdfUrl = await this.fileService.uploadFile(
      ledgerPath,
      CLOUD_FOLDER_PATH.tally,
    );
    await this.fileService.removeFiles([...nextPages, currentPage]);
    if (!pdfUrl || pdfUrl === k500Error) return '';
    return { pdfUrl, sortedList };
  }

  prepareLedgerArray(data) {
    const formatAmount = (amount) =>
      this.typeService.amountNumberWithCommas(amount.toFixed(2));

    const formatAmountOnlyComma = (amount) => amount.toFixed(2);

    let ledgerEntries = [];
    let balance = 0;
    let totalDebit = 0;
    let totalCredit = 0;

    // Process EMI Dues
    for (const due of data.totalDues || []) {
      const amount = due?.amount;
      totalDebit += amount;
      ledgerEntries.push({
        Title: `EMI-${due?.emiNumber} Due`,
        Date: due?.dueDate || '',
        Particulars: [],
        Credit: [],
        Debit: [formatAmount(amount)],
        isReceived: false,
        totalAmount: amount,
      });
    }

    // Process Due Charges
    for (const charge of data.totalDueCharges || []) {
      const amount = charge?.amount;
      totalDebit += amount;
      ledgerEntries.push({
        Title: `EMI-${charge.emiNumber} Due Charges`,
        Date: charge?.chargeDate || '',
        Particulars: [],
        Debit: [formatAmount(amount)],
        Credit: [],
        isReceived: false,
        totalAmount: amount,
      });
    }

    // Process Waivers
    for (const waiver of data.waivers || []) {
      const amount = waiver?.amount;
      totalCredit += amount;
      ledgerEntries.push({
        Title: `EMI-${waiver.emiNumber} Waiver`,
        Date: waiver?.waiverDate || '',
        Particulars: [],
        Debit: [],
        Credit: [formatAmount(amount)],
        isReceived: true,
        totalAmount: amount,
      });
    }

    // Process Repayments
    for (const repayment of data.repayments || []) {
      const creditAmounts = repayment?.creditAmounts || [];
      const totalRepaymentCredit = creditAmounts.reduce(
        (sum, value) => sum + value,
        0,
      );
      totalCredit += totalRepaymentCredit;

      ledgerEntries.push({
        Title: `EMI-${repayment?.emiNumber} REPAYMENT`,
        Date: repayment?.repaymentDate || '',
        Particulars: repayment?.particulars || [],
        Debit: [],
        Credit: creditAmounts.map(formatAmountOnlyComma),
        isReceived: true,
        totalAmount: repayment?.totalAmount,
        UTR: repayment?.utr ? ` (UTR: ${repayment?.utr})` : '',
      });
    }

    // Process Refunds
    for (const refund of data.refunds || []) {
      const debitAmounts = refund?.debitAmounts || [];
      const totalRefundDebit = debitAmounts.reduce(
        (sum, value) => sum + value,
        0,
      );
      totalDebit += totalRefundDebit;

      ledgerEntries.push({
        Title: `EMI-${refund.emiNumber} REFUND`,
        Date: refund?.refundDate || '',
        Particulars: refund?.particulars || [],
        Debit: debitAmounts.map(formatAmount),
        Credit: [],
        isReceived: false,
        totalAmount: refund.totalAmount,
        UTR: refund.utr ? ` (UTR: ${refund.utr})` : '',
      });
    }

    // Sort entries by Date
    ledgerEntries = ledgerEntries.sort((a, b) => {
      const dateA = this.dateService.readableStrToDate(a['Date']);
      const dateB = this.dateService.readableStrToDate(b['Date']);
      return (dateA ? dateA.getTime() : 0) - (dateB ? dateB.getTime() : 0);
    });

    // Calculate balances
    ledgerEntries.forEach((entry) => {
      const totalAmount = entry?.totalAmount;
      if (entry.isReceived) {
        balance -= totalAmount;
      } else {
        balance += totalAmount;
      }
      entry['Balance'] =
        (balance < 0 ? '-' : '') +
        this.typeService.amountNumberWithCommas(
          Math.abs(+balance || 0).toFixed(2),
        );
    });

    const seenDates = new Set();

    ledgerEntries.forEach((item) => {
      if (seenDates.has(item.Date)) {
        // Replace Date with an empty string if already seen
        item.Date = '';
      } else {
        // Mark the date as seen
        seenDates.add(item.Date);
      }
    });

    if (ledgerEntries.length != 0) {
      // Add Total
      ledgerEntries.push({
        Date: 'Total',
        Particulars: [],
        isLastPage: 0,
        Debit: [formatAmount(totalDebit || 0)],
        Credit: [formatAmount(totalCredit || 0)],
        Balance:
          (balance < 0 ? '-' : '') +
          this.typeService.amountNumberWithCommas(
            Math.abs(+balance || 0).toFixed(2),
          ),
      });
    }

    return ledgerEntries;
  }

  calculateOverdue(emiData, transList) {
    // Initialize paid amounts
    let paidPrincipal = emiData?.waived_principal ?? 0;
    let paidInterest = emiData?.waived_interest ?? 0;
    let paidBounceCharge = 0;
    let paidLegalCharge = 0;
    let paidPenalCharge = 0;
    let paidRegInterest = 0;

    paidPrincipal = emiData?.paid_principal ?? 0;
    paidInterest = emiData?.paid_interest ?? 0;
    paidBounceCharge = emiData?.paidBounceCharge ?? 0;
    paidLegalCharge = emiData?.paidLegalCharge ?? 0;
    paidRegInterest = emiData?.paidRegInterestAmount ?? 0;
    paidPenalCharge =
      (emiData?.paid_penalty ?? 0) + (emiData?.paidPenalCharge ?? 0);

    // Round paid amounts
    paidPrincipal = parseFloat(paidPrincipal.toFixed(2));
    paidInterest = parseFloat(paidInterest.toFixed(2));

    const penaltyAmount = this.typeService.manageAmount(emiData.penalty ?? 0);
    // Calculate overdue components
    const principalOverdue = (emiData.principalCovered ?? 0) - paidPrincipal;
    const interestOverdue = (emiData.interestCalculate ?? 0) - paidInterest;
    const bounceChargeOverdue =
      penaltyAmount > 0
        ? 0
        : this.typeService.manageAmount(
            (emiData?.bounceCharge ?? 0) +
              (emiData?.gstOnBounceCharge ?? 0) -
              (paidBounceCharge ?? 0),
          );
    const legalChargeOverdue = this.typeService.manageAmount(
      +(emiData?.legalCharge ?? 0) +
        +(emiData?.legalChargeGST ?? 0) -
        paidLegalCharge,
    );
    const penalChargeOverdue = this.typeService.manageAmount(
      +(emiData?.dpdAmount ?? 0) +
        +(emiData.totalPenalty ?? 0) +
        (emiData?.penaltyChargesGST ?? 0) -
        paidPenalCharge,
    );
    const regInterestOverdue = this.typeService.manageAmount(
      +(emiData?.regInterestAmount ?? 0) -
        +(emiData?.paidRegInterestAmount ?? 0),
    );

    // Add all overdue charges
    let overdueAmount = principalOverdue + interestOverdue || 0;

    let overdueCharge =
      bounceChargeOverdue +
        legalChargeOverdue +
        penalChargeOverdue +
        regInterestOverdue || 0;

    return { overdueAmount, overdueCharge };
  }
  async ledgerLoanData(loanId) {
    const emiInclude: SequelOptions = {
      model: EmiEntity,
      attributes: [
        'id',
        'emi_date',
        'principalCovered',
        'interestCalculate',
        'payment_done_date',
        'payment_due_status',
        'emiNumber',
        'pay_type',
        'payment_status',
        'penalty',
        'fullPayPrincipal',
        'fullPayInterest',
        'fullPayPenalty',
        'bounceCharge',
        'gstOnBounceCharge',
        'totalPenalty',
        'penalty_days',
        'legalCharge',
        'legalChargeGST',
        'fullPayLegalCharge',
        'regInterestAmount',
        'dpdAmount',
        'penaltyChargesGST',
        'fullPayPenal',
        'fullPayRegInterest',
        'fullPayBounce',
        'waived_principal',
        'waived_interest',
        'waived_regInterest',
        'waived_bounce',
        'waived_penal',
        'waived_penalty',
        'waived_legal',
        'waived_foreclose',
        'forClosureAmount',
        'cgstForClosureCharge',
        'sgstForClosureCharge',
        'igstForClosureCharge',
        'paid_principal',
        'paid_interest',
        'paid_penalty',
        'paidPenalCharge',
        'paidLegalCharge',
        'paidRegInterestAmount',
        'paidBounceCharge',
        'partOfemi',
      ],
    };
    const transInclude: any = {
      model: TransactionEntity,
      attributes: [
        'id',
        'paidAmount',
        'principalAmount',
        'interestAmount',
        'completionDate',
        'emiId',
        'type',
        'utr',
        'source',
        'subSource',
        'subStatus',
        'penaltyAmount',
        'regInterestAmount',
        'bounceCharge',
        'sgstOnBounceCharge',
        'cgstOnBounceCharge',
        'igstOnBounceCharge',
        'penalCharge',
        'cgstOnPenalCharge',
        'sgstOnPenalCharge',
        'igstOnPenalCharge',
        'legalCharge',
        'cgstOnLegalCharge',
        'sgstOnLegalCharge',
        'igstOnLegalCharge',
        'forClosureAmount',
        'cgstForClosureCharge',
        'sgstForClosureCharge',
        'igstForClosureCharge',
        'updatedAt',
      ],
      where: { status: kCompleted },
      required: false,
    };
    const disInclude: any = {
      model: disbursementEntity,
      attributes: ['amount', 'utr', 'source'],
    };
    const userInclude: any = {
      model: registeredUsers,
      attributes: ['loanStatus', 'id'],
    };
    let waiverDataInc = {
      model: WaiverEntity,
      attributes: [
        'oldBifurcation',
        'newBifurcation',
        'waiverBifurcation',
        'waiverAmount',
        'loanId',
        'userId',
        'emiId',
        'adminId',
        'type',
        'createdAt',
      ],
      where: {
        type: { [Op.or]: [{ [Op.ne]: 'WAIVER_REVERSED' }, { [Op.eq]: null }] },
      },
      required: false,
    };
    const options: any = {
      where: { id: loanId },
      include: [
        disInclude,
        emiInclude,
        transInclude,
        waiverDataInc,
        userInclude,
      ],
    };
    const attributes = [
      'id',
      'userId',
      'fullName',
      'phone',
      'email',
      'purposeId',
      'netApprovedAmount',
      'stampFees',
      'processingFees',
      'loanFees',
      'charges',
      'loan_disbursement_date',
      'approvedDuration',
      'interestRate',
      'insuranceDetails',
      'loanCompletionDate',
      'penaltyCharges',
      'loanStatus',
    ];
    const loanData = await this.loanRepo.getRowWhereData(attributes, options);
    if (loanData === k500Error) throw new Error();
    return loanData;
  }

  prepareWaiverData(waiverData) {
    const result = [];

    for (const waiver of waiverData) {
      const bifurcation = waiver.waiverBifurcation;
      const createdAt = waiver.createdAt;

      if (waiver?.type === 'EMIPAY') {
        const emiId = bifurcation.emiId;
        result.push({
          emiId,
          date: createdAt,
          waived_regInterest: this.typeService.manageAmount(
            bifurcation.deferredInterest || 0,
          ),
          waived_bounce: this.typeService.manageAmount(
            bifurcation.bounceCharge || 0,
          ),
          waived_penal: this.typeService.manageAmount(
            bifurcation.penalCharge || 0,
          ),
          waived_penalty: this.typeService.manageAmount(
            bifurcation.penalty || 0,
          ),
          waived_legal: this.typeService.manageAmount(
            bifurcation.legalCharge || bifurcation.forecloseCharge || 0,
          ),
          waived_interest: this.typeService.manageAmount(
            bifurcation.emiAmount || 0,
          ),
          waived_foreclose: this.typeService.manageAmount(
            bifurcation.waived_foreclose || 0,
          ),
        });
      } else if (waiver?.type === 'FULLPAY') {
        for (const [emiId, bif] of Object.entries(bifurcation) as [
          string,
          any,
        ][]) {
          result.push({
            emiId: +emiId,
            date: createdAt,
            waived_regInterest: this.typeService.manageAmount(
              bif.deferredInterest || 0,
            ),
            waived_bounce: this.typeService.manageAmount(bif.bounceCharge || 0),
            waived_penal: this.typeService.manageAmount(bif.penalCharge || 0),
            waived_penalty: this.typeService.manageAmount(bif.penalty || 0),
            waived_legal: this.typeService.manageAmount(
              bif.legalCharge || bif.forecloseCharge || 0,
            ),
            waived_interest: this.typeService.manageAmount(bif.emiAmount || 0),
            waived_foreclose: this.typeService.manageAmount(
              bif.waived_foreclose || 0,
            ),
          });
        }
      }
    }

    return result;
  }
}
