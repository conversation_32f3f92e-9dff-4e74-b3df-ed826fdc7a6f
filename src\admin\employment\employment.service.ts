// Imports
import { Op, Sequelize, where } from 'sequelize';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import {
  employementMessageFun,
  PAGE_LIMIT,
  SYSTEM_ADMIN_ID,
  validAppTypeValues,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
  kParamsMissing,
} from 'src/constants/responses';
import {
  CompanyVerifiedSuccess,
  kNoDataFound,
  kRuppe,
  kYouHaveAccess,
  SalarySlipVerificationSucess,
  SCopanyNotVerfy,
  SCopanyVerfication,
  SSalarySlipNotVerify,
  SSalarySlipVerification,
  SWorkEmailNotVerify,
  SWorkEmailVerification,
  vCompany,
  vSalarySlip,
  vWorkMail,
  WorkMailVerificationSucess,
} from 'src/constants/strings';
import { EmiEntity } from 'src/entities/emi.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { SalarySlipEntity } from 'src/entities/salarySlip.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { WorkMailEntity } from 'src/entities/workMail.entity';
import { EmploymentRepository } from 'src/repositories/employment.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { ManualVerifiedCompanyRepo } from 'src/repositories/manual.verified.company.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { SalarySlipRepository } from 'src/repositories/salarySlip.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { WorkMailRepository } from 'src/repositories/workMail.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { CryptService } from 'src/utils/crypt.service';
import { DateService } from 'src/utils/date.service';
import { TypeService } from 'src/utils/type.service';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import { employmentDesignation } from 'src/entities/designation.entity';
import { employmentSector } from 'src/entities/sector.entity';
import { employmentType } from 'src/entities/employment.type';
import { EmploymentHistoryRepository } from 'src/repositories/employmentHistory.repository';
import { StringService } from 'src/utils/string.service';
import { PredictionService } from '../eligibility/prediction.service';
import { EligibilityService } from '../eligibility/eligibility.service';
import { EligibilitySharedService } from 'src/shared/eligibility.shared.service';
import { CompanyRepository } from 'src/repositories/google.company.repository';
import { RedisService } from 'src/redis/redis.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { loanTransaction } from 'src/entities/loan.entity';
import { InstaFinancialService } from 'src/thirdParty/instafinancial/instafinancial.service';
import { ProteanService } from 'src/thirdParty/protean/protean.servics';
import { EMIRepository } from 'src/repositories/emi.repository';
import { ErrorContextService } from 'src/utils/error.context.service';
import {
  EMPLOYMENT_DETAILS_TYPES,
  kVerificationAccessStatus,
  SALARY_STATUS_OBJECT,
} from 'src/constants/objects';
import { CAAssignmentService } from 'src/shared/assignment/caAssignCase.service';
import { GoogleCompanyResultEntity } from 'src/entities/company.entity';

@Injectable()
export class EmploymentService {
  constructor(
    private readonly repository: EmploymentRepository,
    private readonly userRepo: UserRepository,
    private readonly salarySlipRepository: SalarySlipRepository,
    private readonly workMailRepository: WorkMailRepository,
    private readonly manualVerifyCompanyRepo: ManualVerifiedCompanyRepo,
    private readonly masterRepository: MasterRepository,
    private readonly typeService: TypeService,
    private readonly cryptService: CryptService,
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userService: UserServiceV4,
    private readonly sharedNotification: SharedNotificationService,
    private readonly loanRepo: LoanRepository,
    private readonly commonSharedService: CommonSharedService,
    private readonly dateService: DateService,
    private readonly StringService: StringService,
    private readonly empHistoryRepo: EmploymentHistoryRepository,
    private readonly predictionService: PredictionService,
    private readonly eligiblityService: EligibilityService,
    private readonly sharedEligibility: EligibilitySharedService,
    private readonly CompanyRepo: CompanyRepository,
    private readonly instaFinancial: InstaFinancialService,
    private readonly proteanService: ProteanService,
    private readonly employmentRepository: EmploymentRepository,
    private readonly emiRepository: EMIRepository,
    private readonly errorContextService: ErrorContextService,
    private readonly caAssignmentService: CAAssignmentService,
    // Database
    private readonly repo: RepositoryManager,
  ) {}

  //#region get verification data fun
  async getVerificationData(query) {
    try {
      const options = this.prepareVerficationDataOptions(query);
      if (options?.message) return options;
      const comapanyData = await this.findCompanyData(options);
      if (comapanyData?.message) return comapanyData;
      const finalData = this.prepareCompanyData(comapanyData?.rows);
      return { count: comapanyData.count, rows: finalData };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region prepare verficationData options
  private prepareVerficationDataOptions(query) {
    try {
      const status = query?.status ?? '0';
      const page = query?.page ?? 1;
      const sizePage = query?.sizePage ?? PAGE_LIMIT;
      const searchText = query?.searchText;
      const startDate = query?.startDate ?? null;
      const endDate = query?.endDate ?? null;
      const download = query?.download ?? 'false';
      const toDay = this.typeService.getGlobalDate(new Date());

      /// user
      const registeredModel: any = {
        model: registeredUsers,
        attributes: ['fullName', 'city', 'completedLoans'],
        where: { isBlacklist: { [Op.ne]: '1' }, homeStatus: { [Op.ne]: '-1' } },
      };
      //For searching using User Names
      if (searchText)
        registeredModel.where.fullName = { [Op.iRegexp]: searchText };

      if (status === '0') {
        registeredModel.where['NextDateForApply'] = {
          [Op.or]: [{ [Op.lte]: toDay.toJSON() }, { [Op.eq]: null }],
        };
      }
      /// salary slip and work mail
      const empWhere = { status: { [Op.ne]: '-1' } };
      const include = [
        registeredModel,
        { model: SalarySlipEntity, attributes: [], where: empWhere },
        { model: WorkMailEntity, attributes: [], where: empWhere },
      ];

      //// where condition
      const notNull = { [Op.ne]: null };
      const where: any = { salarySlipId: notNull, workMailId: notNull };
      if (status === '0') where.bankingId = notNull;
      if (status !== '4')
        if (status === '1') where.companyVerification = { [Op.or]: ['1', '3'] };
        else where.companyVerification = status;
      if (startDate && endDate && status !== '0') {
        const range = this.typeService.getUTCDateRange(
          startDate.toString(),
          endDate.toString(),
        );
        where.createdAt = {
          [Op.gte]: range.fromDate,
          [Op.lte]: range.endDate,
        };
      }
      //// add options
      const options: any = { include, where, distinct: true };
      if (status != '0' && download != 'true') {
        options.offset = +(page ?? 1) * sizePage - sizePage;
        options.limit = sizePage;
      }
      options.order = [['updatedAt', 'DESC']];
      return options;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region find company Data
  private async findCompanyData(options: any) {
    try {
      const attr = [
        'id',
        'userId',
        'companyName',
        'companyPhone',
        'companyUrl',
        'companyAddress',
        'companyStatusApproveByName',
        'companyVerification',
        'updatedAt',
        'rejectReason',
      ];
      const result = await this.repository.getTableWhereDataWithCounts(
        attr,
        options,
      );
      if (!result || result === k500Error) return kInternalError;
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region pre pare company row data
  private prepareCompanyData(list) {
    const finalData = [];
    try {
      list.forEach((element) => {
        try {
          const tempData: any = {};
          const url = (element?.companyUrl ?? '-').trim();
          const contact = (element?.companyPhone ?? '-').trim();
          const address = (element?.companyAddress ?? '-').trim();
          tempData['Name'] = element?.user?.fullName;
          tempData['Company name'] = element?.companyName ?? '-';
          tempData['Company URL'] = url ? url : '-';
          tempData['Company contact'] = contact ? contact : '-';
          tempData['Company address'] = address ? address : '-';
          tempData['Completed loans'] = element?.user?.completedLoans ?? 0;
          tempData['City'] = element?.user?.city;
          tempData['Last action by'] =
            element?.companyStatusApproveByName ?? 'SYSTEM';
          tempData['userId'] = element?.userId ?? '-';
          tempData['Status'] = element?.companyVerification;
          tempData['Reject reason'] = element?.rejectReason;
          tempData['employeeId'] = element?.id ?? '-';
          finalData.push(tempData);
        } catch (error) {}
      });
    } catch (error) {}
    return finalData;
  }

  async getEmployementData(userId) {
    try {
      const workMailInclude = {
        model: WorkMailEntity,
        attributes: ['status', 'email', 'tempStatus', 'tempEmail', 'masterId'],
      };
      const empInclude = {
        model: employmentDetails,
        attributes: [
          'id',
          'salarySlipId',
          'companyName',
          'companyPhone',
          'companyUrl',
          'userId',
          'masterId',
        ],
      };
      const masterInclude = {
        model: MasterEntity,
        attributes: [
          'id',
          'userId',
          'loanId',
          'status',
          'dates',
          'workMailAdminId',
          'salarySlipAdminId',
          'companyAdminId',
          'empId',
          'salarySlipId',
          'workMailId',
          'rejection',
        ],
        include: [workMailInclude, empInclude],
      };
      const att = ['id', 'email', 'fullName', 'fcmToken', 'phone', 'masterId'];
      const options = { where: { id: userId }, include: [masterInclude] };
      const userData = await this.userRepo.getRowWhereData(att, options);
      if (userData == k500Error || !userData) return kInternalError;
      return userData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async changeEmployementData(body) {
    try {
      if (!body?.userId || !body?.adminId || !body?.status || !body?.type)
        return kParamsMissing;
      if (body.status == '2') if (!body.rejectReason) return kParamsMissing;
      if (body.status == '3' && !body?.remark) return kParamsMissing;
      const userId = body.userId;
      const adminId = body.adminId;
      const status = body.status;
      const rejectionReason = body?.rejectReason;
      const type = body?.type;
      const remark = body?.remark;
      const salaryDate = body?.salaryDate;
      const loanId = body?.loanId;
      if (type == 'LOAN' && !loanId) return kParamMissing('loanId');
      const userData = await this.getEmployementData(userId);
      if (!userData?.masterData)
        return k422ErrorMessage('Employement data not found!');
      const allAdminData = await this.caAssignmentService.getCAData();
      if (allAdminData?.message) return kInternalError;
      const adminData = allAdminData.find((ele) => ele.id == adminId);
      if (adminData && !adminData?.verificationAccessStatus?.employment)
        return k422ErrorMessage(kYouHaveAccess);
      const updateEmpData: any = await this.changeEmployementStatus(
        type,
        status,
        adminId,
        userData,
        remark,
        rejectionReason,
        salaryDate,
        loanId,
      );
      return updateEmpData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  preapareUpdateData(
    type,
    status,
    userId,
    adminId,
    remark,
    rejectionReason,
    masterData,
    salaryDate,
  ) {
    try {
      const updateData: any = {
        status: status,
        approveById: adminId,
        remark: remark,
        rejectReason: rejectionReason ?? '',
      };
      const rejection = masterData.rejection ?? {};
      const masterUpdate: any = {};
      if (type == 'SALARYSLIP') {
        masterUpdate.salarySlipAdminId = adminId;
        masterUpdate.rejection = {
          ...rejection,
          salarySlip: rejectionReason ?? '',
        };

        updateData.salaryVerifiedDate = new Date().toJSON();
        if (salaryDate)
          updateData.salarySlipDate = new Date(salaryDate).toJSON();
      } else if (type == 'WORKMAIL') {
        updateData.verifiedDate = new Date().toJSON();
        updateData.userId = userId;
        masterUpdate.rejection = {
          ...rejection,
          workMail: rejectionReason ?? '',
        };
        masterUpdate.workMailAdminId = adminId;
      } else if (type == 'COMPANY') {
        delete updateData.approveById;
        delete updateData.status;
        updateData.companyVerification = status;
        updateData.companyStatusApproveById = adminId;
        updateData.verifiedDate = new Date().toJSON();
        masterUpdate.rejection = {
          ...rejection,
          company: rejectionReason ?? '',
        };
        masterUpdate.companyAdminId = adminId;
      }
      return { updateData, masterUpdate };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  preapareLogData(type, userId, status, adminId, remark, rejectionReason) {
    try {
      const logObj: any = {
        userId: userId,
        status,
        remark,
        reason: rejectionReason ?? '',
        adminId,
      };

      if (type == 'SALARYSLIP') logObj.type = vSalarySlip;
      else if (type == 'WORKMAIL') logObj.type = vWorkMail;
      else if (type == 'COMPANY') logObj.type = vCompany;
      return logObj;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async changeEmployementStatus(
    type,
    status,
    adminId,
    userData,
    remark,
    rejectionReason,
    salaryDate,
    loanId,
  ) {
    try {
      const userId = userData.id;
      const masterData = userData?.masterData;
      const statusData = masterData.status;
      if (!masterData) return kInternalError;
      const data: any = await this.preapareUpdateData(
        type,
        status,
        userId,
        adminId,
        remark,
        rejectionReason,
        masterData,
        salaryDate,
      );
      if (data.message) return data;
      let updateData = data.updateData;
      masterData.masterUpdate = data.masterUpdate;
      const logData: any = this.preapareLogData(
        type,
        userId,
        status,
        adminId,
        remark,
        rejectionReason,
      );
      if (logData.message) return logData;
      let finalResult: any;
      let successMessage = '';
      const checkStages = ['SALARYSLIP', 'WORKMAIL'];
      if (
        checkStages.includes(type) &&
        statusData.company != '1' &&
        statusData.company != '3'
      )
        return k422ErrorMessage('Company should be verified first!');
      if (type == 'SALARYSLIP') {
        finalResult = await this.updateSalarySlipData(
          updateData,
          status,
          masterData,
        );
        successMessage = employementMessageFun('Salary slip', status);
      } else if (type == 'WORKMAIL') {
        finalResult = await this.updateWorkMailStatus(
          status,
          updateData,
          masterData,
        );
        successMessage = employementMessageFun('Work mail', status);
      } else if (type == 'COMPANY') {
        finalResult = await this.updateCompanyStatus(
          userData,
          updateData,
          type,
        );
        successMessage = employementMessageFun('Company', status);
      } else if (type == 'LOAN') {
        finalResult = await this.eligiblityService.rejectLoanFromAdmin({
          userId,
          loanId,
          adminId,
          remark: remark ?? rejectionReason,
        });
        successMessage = employementMessageFun('Loan', status);
      }
      await this.userService.routeDetails({ id: userId });
      if (finalResult?.message) return finalResult;

      if (rejectionReason && status == '2') {
        const reason = await this.commonSharedService.getRejectReasonTemplate(
          rejectionReason,
        );

        if (reason?.message) {
          // return rejectionReason;
        } else rejectionReason = reason;
      }
      const att = ['id'];
      const options = { where: { userId } };
      const loanData = await this.loanRepo.getRowWhereData(att, options);
      await this.caAssignmentService.assignCA(
        kVerificationAccessStatus.employment,
        loanData.id,
      );

      await this.sendNotificationToUser(
        status,
        userData,
        rejectionReason,
        type,
        adminId,
      );

      return successMessage;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async updateSalarySlipData(updateData, status, masterData) {
    try {
      let newData;
      const statusData = masterData.status;
      const masterUpdate = masterData.masterUpdate;
      let finalData;
      if (statusData.salarySlip == status)
        return k422ErrorMessage('Salary already updated');
      if (statusData.salarySlip == '2') {
        if (statusData.salarySlip == 1 || statusData.salarySlip == 3)
          return k422ErrorMessage('Salary already verified');
        newData = await this.salarySlipRepository.createRowDataWithCopy(
          updateData,
          masterData.salarySlipId,
        );
        if (newData == k500Error) return kInternalError;
        finalData = await this.repository.updateRowData(
          { salarySlipId: newData.id },
          masterData.empId,
        );
        masterUpdate.salarySlipId = newData.id;
      } else {
        finalData = await this.salarySlipRepository.updateRowData(
          updateData,
          masterData.salarySlipId,
        );
      }
      if (finalData == k500Error || finalData[0] <= 0) return kInternalError;
      masterData.masterUpdate = masterUpdate;
      await this.approvedEmployementDate(masterData, status, 'SALARYSLIP');
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async approvedEmployementDate(
    masterData,
    status,
    type,
    adminId = SYSTEM_ADMIN_ID,
  ) {
    try {
      const loanId = masterData?.loanId;
      const userId = masterData?.userId;
      const dates = masterData?.dates ?? {};
      const statusData: any = masterData.status;
      const approvedStatus = [1, 3, 4];
      if (
        type == 'SALARYSLIP' &&
        status == '3' &&
        approvedStatus.includes(statusData.company) &&
        approvedStatus.includes(statusData.workMail)
      ) {
        dates.employment = new Date().getTime();
      } else if (
        type == 'WORKMAIL' &&
        status == '3' &&
        approvedStatus.includes(statusData.company) &&
        approvedStatus.includes(statusData.salarySlip)
      )
        dates.employment = new Date().getTime();
      else if (
        type == 'COMPANY' &&
        status == '3' &&
        approvedStatus.includes(statusData.workMail) &&
        approvedStatus.includes(statusData.salarySlip)
      )
        dates.employment = new Date().getTime();
      let allStatus = [1, 2, 3, 4];
      if (allStatus.includes(+status)) {
        if (status == '2') dates.employment = new Date().getTime();
        if (type == 'SALARYSLIP') statusData.salarySlip = +status;
        else if (type == 'WORKMAIL') {
          if (status != '2') {
            const workMailData = masterData?.workMailData;
            const empData = masterData?.empData;
            await this.commonSharedService.checkManualWorkMail(
              workMailData,
              empData,
              { status },
              adminId,
            );
          }
          statusData.workMail = +status;
        } else if (type == 'COMPANY') statusData.company = +status;
      }
      await this.masterRepository.updateRowData(
        { dates, status: statusData, ...masterData.masterUpdate },
        masterData.id,
      );
      if (
        status == '3' &&
        userId &&
        loanId &&
        (statusData.bank == 1 || statusData.bank == 3)
      ) {
        const finalApproval = await this.sharedEligibility.finalApproval({
          loanId,
          userId,
        });
        if (finalApproval.message) return finalApproval;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  async updateWorkMailStatus(status, rawData, masterData) {
    try {
      let isNewEntry = true;
      let statusData = masterData?.status;
      let workMailData = masterData?.workMailData;
      let masterUpdate = masterData.masterUpdate;
      let approveById = rawData.approveById;
      let workMailAdminId = masterData.workMailAdminId;
      const workMail = statusData.workMail;
      const empId = masterData?.empId;
      if (workMail == 0 && !masterData.workMailId && empId) {
        const empOpt = { where: { id: empId } };
        const empData = await this.repository.getRowWhereData(
          ['workMailId'],
          empOpt,
        );
        const masterId = masterData?.id;
        if (empData != k500Error && empData?.workMailId && masterId) {
          const wmUpdate = await this.masterRepository.updateRowData(
            { workMailId: empData?.workMailId },
            masterId,
          );
          if (wmUpdate != k500Error)
            masterData.workMailId = empData?.workMailId;
        }
      }
      if (workMail == status)
        return k422ErrorMessage('Workmail already updated!');
      if (workMail == 0 && workMailAdminId == SYSTEM_ADMIN_ID)
        isNewEntry = false;
      if (workMail == status && workMailAdminId == approveById)
        isNewEntry = false;
      let updateData: any;
      if (status == '2') {
        if (workMail == 1 || workMail == 3)
          return k422ErrorMessage('Work mail already verified');
        updateData = await this.workMailRepository.updateRowData(
          rawData,
          masterData.workMailId,
        );
      } else if (isNewEntry) {
        if (workMailData.tempStatus == '0' && workMailData?.tempEmail)
          rawData.email = workMailData.tempEmail;
        const createData = await this.workMailRepository.createRowData(rawData);
        if (!createData || createData === k500Error) return kInternalError;
        updateData = await this.repository.updateRowData(
          { workMailId: createData.id },
          masterData.empId,
        );
        masterUpdate.workMailId = createData.id;
      } else {
        updateData = await this.workMailRepository.updateRowData(
          rawData,
          masterData.workMailId,
        );
      }
      if (updateData == k500Error || updateData[0] <= 0) return kInternalError;
      masterData.masterUpdate = masterUpdate;
      await this.approvedEmployementDate(
        masterData,
        status,
        'WORKMAIL',
        approveById,
      );
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  async updateCompanyStatus(userData, updateData, notifyUser = true) {
    try {
      const status = updateData.companyVerification;
      const masterData = userData.masterData;
      const empData = masterData.empData;
      const statusData = masterData.status;
      if (statusData.company == status)
        return k422ErrorMessage('Company already updated!');
      if (status == '2') {
        if (statusData.company == 1 || statusData.company == 3)
          return k422ErrorMessage('Company already verified!');
        const rejectData: any = {};
        rejectData.status = status;
        rejectData.verifiedDate = updateData.verifiedDate;
        rejectData.rejectReason = updateData.rejectReason;
        rejectData.approveById = updateData.companyStatusApproveById;
        //salary slip payload
        let rejectMasterData = { ...masterData };
        const masterRejection = masterData.rejection;
        rejectMasterData.masterUpdate = {
          rejection: {
            ...masterRejection,
            salarySlip: updateData.rejectReason,
          },
          workMailAdminId: rejectData.approveById,
        };
        await this.updateWorkMailStatus(status, rejectData, rejectMasterData);
        rejectMasterData.masterUpdate = {
          rejection: {
            ...masterRejection,
            workMail: updateData.rejectReason,
          },
          salarySlipAdminId: rejectData.approveById,
        };
        rejectData.salaryVerifiedDate = rejectData.verifiedDate;
        delete rejectData.verifiedDate;
        await this.updateSalarySlipData(rejectData, status, rejectMasterData);
      }
      const updateManualVerifiedCompany: any =
        await this.updateManualVerifiedCompany(status, empData);
      if (updateManualVerifiedCompany.message)
        return updateManualVerifiedCompany;
      const updateRes = await this.repository.updateRowData(
        updateData,
        masterData.empId,
      );
      if (updateRes == k500Error) return kInternalError;
      await this.approvedEmployementDate(masterData, status, 'COMPANY');
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async sendNotificationToUser(
    status,
    userData,
    rejectionReasonData,
    type,
    adminId,
  ) {
    try {
      let title;
      let body;
      userData.phone = await this.cryptService.decryptPhone(userData.phone);
      if (type == 'SALARYSLIP') {
        if (status == 3) {
          title = SSalarySlipVerification;
          body = SalarySlipVerificationSucess;
        } else if (status == 2) {
          title = SSalarySlipNotVerify;
          body = rejectionReasonData?.content;
        }
      } else if (type == 'WORKMAIL') {
        if (status == 3) {
          title = SWorkEmailVerification;
          body = WorkMailVerificationSucess;
        } else if (status == 2) {
          title = SWorkEmailNotVerify;
          body = rejectionReasonData.content;
        }
      } else if (type == 'COMPANY') {
        if (status == 3) {
          title = SCopanyVerfication;
          body = CompanyVerifiedSuccess;
        } else if (status == 2) {
          title = SCopanyNotVerfy;
          body = rejectionReasonData.content;
        }
      }

      // Push notification
      this.sharedNotification.sendNotificationToUser({
        userList: [userData.id],
        title,
        content: body,
        adminId,
      });

      //  SMS alert
      const fullName = userData.fullName;
      const smsOptions: any = { fullName, name: fullName };
      if (status == '2') smsOptions.reason = rejectionReasonData.content;

      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async updateManualVerifiedCompany(status, employeeData) {
    try {
      if (status == '2') return true;
      let updatedUrl = employeeData.companyUrl;
      if (!updatedUrl || updatedUrl.length === 0) updatedUrl = '_URL';
      const rowsUpdated = await this.manualVerifyCompanyRepo.updateRowWhereData(
        { isActive: true },
        {
          where: {
            companyName: { [Op.iRegexp]: employeeData.companyName },
            url: updatedUrl,
          },
        },
      );
      if (rowsUpdated[0] === 0) {
        this.manualVerifyCompanyRepo.create({
          companyName: employeeData.companyName,
          url: updatedUrl,
          isActive: true,
        });
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async getCompanyActivity(query) {
    const userId = query.userId;
    if (!userId) return kParamMissing('userId');

    ///get user data
    const options = { where: { id: userId }, useMaster: false };
    const userData = await this.userRepo.getRowWhereData(
      ['id', 'fullName'],
      options,
    );
    if (userData == k500Error) return k500Error;
    if (!userData) return k422ErrorMessage(kNoDataFound);

    ///get employee company name
    const employeeData = await this.employmentRepository.getRowWhereData(
      ['companyName', 'updatedCompanyName'],
      { where: { userId }, order: [['id', 'desc']], useMaster: false },
    );
    if (employeeData == k500Error) return k500Error;
    let companyName =
      employeeData?.updatedCompanyName ?? employeeData?.companyName ?? null;
    if (!companyName) return {};
    companyName = companyName.trim().toUpperCase();

    if (!query?.isRefresh) {
      const getRedisData =
        await this.commonSharedService.getCompanyActivityRedis(companyName);
      if (getRedisData?.count) return getRedisData;
    }

    ///get company id
    const getCompanyId = await this.CompanyRepo.getRowWhereData(['id'], {
      where: { companyName },
      order: [['createdAt', 'DESC']],
      useMaster: false,
    });
    if (getCompanyId === k500Error) throw new Error();
    else if (!getCompanyId?.id) return {};

    const cId = getCompanyId?.id;
    const loanOptions = {
      where: {
        loanStatus: { [Op.or]: ['Complete', 'Active'] },
        companyId: cId,
      },
      useMaster: false,
      distinct: true,
    };
    const attributes = [
      'id',
      'userId',
      'netApprovedAmount',
      'interestRate',
      'loanStatus',
      'eligibilityDetails',
    ];
    const loanData = await this.loanRepo.getCountWhereData(
      attributes,
      loanOptions,
    );
    if (loanData === k500Error) return kInternalError;
    if (!loanData?.count) return {};

    const loanIds = loanData?.rows.map((data) => data?.id);

    ///get emi details
    const emiData = await this.emiRepository.getTableWhereData(
      ['id', 'penalty_days', 'payment_status', 'loanId', 'payment_due_status'],
      { where: { loanId: loanIds }, useMaster: false },
    );
    if (emiData === k500Error) return kInternalError;
    if (!emiData?.length) return {};

    const userIds = loanData?.rows.map((data) => data?.userId);
    ///get user details
    const companyUserData = await this.userRepo.getTableWhereData(
      ['id', 'fullName'],
      { where: { id: userIds }, useMaster: false },
    );
    if (companyUserData === k500Error) return kInternalError;

    const prepareData: any = await this.prepareCompanyActivityData(
      loanData.rows,
      emiData,
      companyUserData,
    );

    ///get company name
    const rawQuery = `SELECT SUM("loanGmv") AS "totalCompanyGmv" 
    FROM "loanTransactions"
    WHERE "loanStatus" IN ('Active', 'Complete')
    AND "companyId" = '${cId}'`;
    const outputList = await this.repo.injectRawQuery(
      loanTransaction,
      rawQuery,
      { useMaster: false },
    );
    if (outputList == k500Error) throw new Error();
    const totalCompanyGmvAmt = outputList[0]?.totalCompanyGmv ?? 0;
    prepareData.companyName = companyName;
    prepareData.count = loanData.count;
    prepareData.totalCompanyGmvAmt = totalCompanyGmvAmt;

    await this.repo.updateRowWhereData(
      GoogleCompanyResultEntity,
      {
        loanCount: loanData?.count,
        CLTV: totalCompanyGmvAmt,
        avgInterest: prepareData?.avgInterest,
      },
      { where: { id: cId } },
    );
    await this.commonSharedService.setCompanyActivityRedis(
      companyName,
      prepareData,
    );
    return prepareData;
  }

  //prepare company activity data
  async prepareCompanyActivityData(data, emiDetails, userDetails) {
    try {
      const rows = [];
      let counts: any = {
        defaulterCount: 0,
        defaulterAmount: 0,
        delayCount: 0,
        delayAmount: 0,
        onTimeCount: 0,
        onTimeAmount: 0,
        inProgressCount: 0,
        inProgressAmount: 0,
        totalInterestRate: 0,
        totalDisburse: 0,
        totalDisburseAmount: 0,
        defaulterPercentage: 0,
        delayPercentage: 0,
        inProgressPercentage: 0,
        onTimePercentage: 0,
        avgInterest: 0,
      };

      if (data.length) {
        for (let index = 0; index < data.length; index++) {
          try {
            const element = data[index];
            const emiData = emiDetails.filter(
              (data) => data?.loanId == element?.id,
            );
            const userData = userDetails.find(
              (data) => data?.id == element?.userId,
            );

            const netApprovedAmount = element?.netApprovedAmount ?? 0;
            let status;
            const defaulter = [];
            const ontime = [];
            const delay = [];
            const inProgress = [];
            let delayDays = 0;
            emiData.forEach((el) => {
              try {
                const payment_due_status = el?.payment_due_status;
                const payment_status = el?.payment_status;

                if (payment_due_status == '1' && el?.penalty_days > delayDays)
                  delayDays = el?.penalty_days;

                if (payment_due_status === '1' && payment_status === '0')
                  defaulter.push(el);
                else if (payment_due_status === '1' && payment_status === '1')
                  delay.push(el);
                else if (payment_due_status === '0' && payment_status === '1')
                  ontime.push(el);
                else if (payment_due_status === '0' && payment_status === '0')
                  inProgress.push(el);
              } catch (error) {}
            });

            if (defaulter.length > 0) {
              counts.defaulterCount++;
              counts.defaulterAmount += +netApprovedAmount;
              status = 'defaulter';
            } else if (delay.length > 0) {
              counts.delayCount++;
              counts.delayAmount += +netApprovedAmount;
              status = 'delay';
            } else if (inProgress.length > 0) {
              counts.inProgressCount++;
              counts.inProgressAmount += +netApprovedAmount;
              status = 'In-progress';
            } else if (ontime.length > 0) {
              counts.onTimeCount++;
              counts.onTimeAmount += +netApprovedAmount;
              status = 'OnTime';
            }

            const ObjData = {};
            if (
              element.loanStatus === 'Active' ||
              element.loanStatus === 'Complete'
            ) {
              counts.totalDisburse += 1;
              counts.totalDisburseAmount += +element.netApprovedAmount;
            }

            const approvedAmount: any = +(element?.netApprovedAmount ?? 0);
            const annumInterest =
              element?.eligibilityDetails?.annumInterest ??
              element?.eligibilityDetails?.anummIntrest;

            const loanAnnualRate = +annumInterest;
            const dailyInterestRate = +element?.interestRate;

            const annualInterestRateValue = loanAnnualRate
              ? +loanAnnualRate.toFixed(2)
              : +(dailyInterestRate * 365).toFixed(2);
            counts.totalInterestRate += +annualInterestRateValue;

            ObjData['id'] = userData?.id ?? '-';
            ObjData['Loan Id'] = element.id ?? '-';
            ObjData['Name'] = userData?.fullName ?? '-';
            ObjData['Approved amount'] =
              this.StringService.readableAmount(approvedAmount);

            ObjData['Interest'] = `${annualInterestRateValue.toFixed(2)}%`;

            ObjData['Delay days'] = delayDays ?? 0;
            ObjData['Status'] = status ?? null;

            rows.push(ObjData);
          } catch (error) {}
        }

        // Calculate percentage
        counts.defaulterPercentage = Math.round(
          (counts?.defaulterAmount / counts?.totalDisburseAmount) * 100,
        );
        counts.delayPercentage = Math.round(
          (counts?.delayAmount / counts?.totalDisburseAmount) * 100,
        );
        counts.inProgressPercentage = Math.round(
          (counts?.inProgressAmount / counts?.totalDisburseAmount) * 100,
        );
        counts.onTimePercentage = Math.round(
          (counts?.onTimeAmount / counts?.totalDisburseAmount) * 100,
        );
        counts.avgInterest =
          (counts?.totalInterestRate / data.length).toFixed(2) + '%';

        // Format amount with commas and rupees sign
        counts.defaulterAmount = this.StringService.readableAmount(
          counts?.defaulterAmount,
        );
        counts.delayAmount = this.StringService.readableAmount(
          counts?.delayAmount,
        );
        counts.onTimeAmount = this.StringService.readableAmount(
          counts?.onTimeAmount,
        );
        counts.inProgressAmount = this.StringService.readableAmount(
          counts?.inProgressAmount,
        );
        counts.totalDisburseAmount = this.StringService.readableAmount(
          counts?.totalDisburseAmount,
        );
      }
      return { rows, ...counts };
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
    }
  }

  // filter active emi data
  findActiveEmi(emiData) {
    try {
      const temp = [...emiData];
      temp.sort((a, b) => a.id - b.id);
      for (let index = 0; index < temp.length; index++) {
        try {
          const element = temp[index];
          if (element.payment_status !== '1' || temp.length - 1 == index)
            return element;
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region
  async getUserEmployment(query) {
    try {
      const userId = query?.userId;
      if (!userId) return kParamMissing('userId');

      //get user employment details
      const data = await this.getUserEmploymentData(userId);
      if (!data?.employee) return {};

      return this.prepareEmployeeDetails(userId, data);
    } catch (error) {
      return k500Error;
    }
  }
  //#endregion

  //#region
  private async getUserEmploymentData(userId) {
    const data = {
      employee: null,
      master: null,
      salary: null,
      workmail: null,
    };

    ///get employee details
    data.employee = await this.getEmployeeData(userId);
    if (data.employee == k500Error) throw new Error();
    if (!data?.employee) return data;

    ///get user master details
    if (data?.employee?.masterId)
      data.master = await this.getEmployeeMasterData(data?.employee?.masterId);

    ///get user salary details
    if (data?.employee?.salarySlipId)
      data.salary = await this.getEmployeeSalaryData(
        data?.employee?.salarySlipId,
      );

    ///get user workmail details
    if (data?.employee?.workMailId)
      data.workmail = await this.getEmployeeWorkmailData(
        data?.employee?.workMailId,
      );

    if (
      data.workmail == k500Error ||
      data.salary == k500Error ||
      data.master == k500Error
    )
      throw new Error();

    return data;
  }
  //#endregion

  //#region
  private async getEmployeeData(userId) {
    const attributes = [
      'companyName',
      'userId',
      'sectorId',
      'designationId',
      'salary',
      'salaryDate',
      'salarySlipId',
      'workMailId',
      'id',
      'startDate',
      'otherInfo',
      'updatedCompanyName',
      'companyNameChangeBy',
      'masterId',
    ];
    const options = { where: { userId }, order: [['id', 'DESC']] };
    return await this.repository.getRowWhereData(attributes, options);
  }
  //#endregion

  //#region
  private async getEmployeeMasterData(id) {
    const attributes = ['id', 'status', 'otherInfo'];
    const options = { where: { id } };
    return await this.masterRepository.getRowWhereData(attributes, options);
  }
  //#endregion

  //#region
  private async getEmployeeSalaryData(id) {
    const attributes = [
      'id',
      'url',
      'netPayAmount',
      'companyName',
      'salaryVerifiedDate',
      'salarySlipDate',
      'status',
      'response',
      'approveById',
      'createdAt',
    ];
    const options = { where: { id } };
    return await this.salarySlipRepository.getRowWhereData(attributes, options);
  }
  //#endregion

  //#region
  private async getEmployeeWorkmailData(id) {
    const attributes = ['email', 'status', 'id', 'verifiedDate', 'approveById'];
    const options = { where: { id } };
    return await this.workMailRepository.getRowWhereData(attributes, options);
  }
  //#endregion

  //#region
  private async prepareEmployeeDetails(userId, data) {
    let response: any = {};

    ///prepare employee data
    response['Work Details'] = await this.prepareEmployeeData(userId, data);

    ///prepare workmail details
    const workmail = data?.workmail || null;
    if (workmail?.id)
      response['Work E-Mail'] = await this.prepareEmployeeWorkMail(workmail);

    ///prepare salary data
    const salary = data?.salary || null;
    if (salary?.id) {
      response['Salary Details'] = await this.prepareEmployeeSalary(salary);
    }

    return response;
  }
  //#endregion

  //#region
  private async prepareEmployeeData(userId, data) {
    ///prepare sector data
    let Sector = '-';
    if (data?.employee?.sectorId) {
      const sectorData: any = await this.commonSharedService.getSectorData(
        data?.employee?.sectorId,
      );
      if (sectorData == k500Error) Sector = null;
      Sector = sectorData?.sectorName || '-';
    }

    ///prepare designation
    let Designation = '-';
    if (data?.employee?.designationId) {
      const designationData: any =
        await this.commonSharedService.getDesignationData(
          data?.employee?.designationId,
        );
      Designation = designationData?.designationName || '-';
    }

    const lastPayDate: string = data?.employee?.otherInfo?.lastPayDate
      ? this.dateService.dateToReadableFormat(
          data?.employee?.otherInfo?.lastPayDate,
        ).readableStr
      : '';
    const nextPayDate: string = data?.employee?.otherInfo?.nextPayDate
      ? this.dateService.dateToReadableFormat(
          data?.employee?.otherInfo?.nextPayDate,
        ).readableStr
      : '';

    const updatedBy = data?.employee?.companyNameChangeBy
      ? (
          await this.commonSharedService.getAdminData(
            data?.employee?.companyNameChangeBy,
          )
        )?.fullName
      : '-';

    let updatedCompanyName = data?.master?.otherInfo?.userEnterCompanyName;
    if (!updatedCompanyName) {
      let emiHistory = await this.empHistoryRepo.getRowWhereData(
        ['companyName'],
        {
          where: {
            userId,
            companyNameChangeBy: { [Op.is]: null },
          },
          order: [['id', 'DESC']],
        },
      );
      if (emiHistory != k500Error && emiHistory?.companyName)
        updatedCompanyName = emiHistory?.companyName;
    }

    const employeeData = {
      Id: data?.employee?.id,
      'Company Name': data?.employee?.companyName,

      ///if updated company name is available
      ...(updatedCompanyName && updatedCompanyName != '-'
        ? { 'User Entered Company Name': updatedCompanyName }
        : {}),

      ///if updated company name is available
      ...(updatedBy && updatedBy != '-' ? { 'Updated By': updatedBy } : {}),
      Sector,
      Designation,
      'Contact number': data?.employee?.otherInfo?.userCompanyPhone || '-',
      'Starting date': data?.employee?.startDate
        ? this.typeService.getDateFormatted(data?.employee?.startDate)
        : '-',
      'Net pay amount': data?.master?.otherInfo?.salaryInfo
        ? this.StringService.readableAmount(
            +data?.master?.otherInfo?.salaryInfo,
          )
        : kRuppe + 0,
      'Last pay date': lastPayDate?.split('-')?.join('/') || '-',
      'Next pay date': nextPayDate?.split('-')?.join('/') || '-',
      'Entered salary': data?.employee?.salary
        ? this.StringService.readableAmount(+data?.employee?.salary)
        : kRuppe + 0,
      'Entered salary date': data?.employee?.salaryDate || '-',
      'Third Party Verified':
        data?.employee?.otherInfo?.thirdPartyVerified || '-',
    };
    return employeeData;
  }
  //#endregion

  //#region
  private async prepareEmployeeWorkMail(workmail) {
    return {
      'Work email': workmail?.email || '-',
      Status: SALARY_STATUS_OBJECT[+workmail?.status] || {
        text: 'Pending',
        class: 'bg-pending',
      },
      Date: workmail?.verifiedDate
        ? this.typeService.getDateFormatted(workmail?.verifiedDate)
        : '-',
      'Last action by': workmail?.approveById
        ? (await this.commonSharedService.getAdminData(workmail?.approveById))
            ?.fullName
        : '-',
    };
  }
  //#endregion

  //#region
  private async prepareEmployeeSalary(salary) {
    const response = salary?.response ? JSON.parse(salary.response) : null;
    return {
      'Upload Date': this.typeService.getDateFormatted(salary?.createdAt),
      'Salary Verified Date': salary?.salaryVerifiedDate
        ? this.typeService.getDateFormatted(salary?.salaryVerifiedDate)
        : '-',
      'Company Name': salary?.companyName || '-',
      'Document Type': response?.Type
        ? EMPLOYMENT_DETAILS_TYPES[response?.Type]
        : 'Other Document',
      View: salary?.url || '-',
      'Salary Slip Month': salary?.salarySlipDate
        ? this.typeService.getMonthAndYear(salary?.salarySlipDate)
        : '-',
      'Net Pay': '₹' + salary?.netPayAmount,
      Status: SALARY_STATUS_OBJECT[+salary?.status] || {
        text: 'Pending',
        class: 'bg-pending',
      },
      'Last Action By': salary?.approveById
        ? (await this.commonSharedService.getAdminData(salary?.approveById))
            ?.fullName
        : '-',

      ///if bank name is available
      ...(response?.Type === 'BankStatement' && response?.bank_name
        ? { 'Bank Name': response.bank_name }
        : {}),
    };
  }
  //#endregion

  //#region this api is deprecated (latest one is getUserEmployment)
  async getEmploymentDetails(query) {
    try {
      const userId = query.userId;
      if (!userId) return kParamsMissing;
      const attributes = [
        'companyName',
        'companyPhone',
        'companyUrl',
        'companyAddress',
        'sectorId',
        'designationId',
        'salary',
        'salaryDate',
        'id',
        'startDate',
        'endDate',
        'otherInfo',
        'updatedCompanyName',
        'companyNameChangeBy',
      ];
      const workMailInclude = {
        model: WorkMailEntity,
        attributes: ['email', 'status', 'id', 'verifiedDate', 'approveById'],
      };
      const salarySlipInclude = {
        model: SalarySlipEntity,
        attributes: [
          'id',
          'url',
          'netPayAmount',
          'salaryVerifiedDate',
          'salarySlipDate',
          'status',
          'response',
          'approveById',
          'createdAt',
        ],
      };
      const masterInclude = {
        model: MasterEntity,
        attributes: ['id', 'status', 'otherInfo'],
      };
      const include = [workMailInclude, salarySlipInclude, masterInclude];
      const options = {
        useMaster: false,
        include,
        where: { userId },
      };
      // temporary redis code commented due to PROD issue
      // const key = `${userId}_USER_EMPLOYMENT_DETAILS`;
      // let data = await this.redisService.getKeyDetails(key);
      // if (!data) {
      const data = await this.repository.getRowWhereData(attributes, options);
      if (!data) return {};
      if (data == k500Error) return kInternalError;
      // } else data = JSON.parse(data);

      const approvedStatus = ['1', '3', '4'];
      // if (
      //   data?.updatedCompanyName ||
      //   (approvedStatus.includes(data?.workMail?.status) &&
      //     approvedStatus.includes(data?.salarySlip?.status))
      // )
      //   await this.redisService.set(
      //     key,
      //     JSON.stringify(data),
      //     NUMBERS.SEVEN_DAYS_IN_SECONDS,
      //   );

      if (data?.startDate)
        data.startDate = this.typeService.getDateFormatted(data?.startDate);
      if (data.workMail) {
        if (data.workMail?.verifiedDate)
          data.workMail.verifiedDate = this.typeService.getDateFormatted(
            data.workMail?.verifiedDate,
          );

        const WorkApprovedName = await this.commonSharedService.getAdminData(
          data.workMail.approveById,
        );

        if (WorkApprovedName)
          data.workMail.adminName = WorkApprovedName?.fullName ?? null;
      }

      if (data?.salarySlip) {
        if (data.salarySlip?.salaryVerifiedDate)
          data.salarySlip.salaryVerifiedDate =
            this.typeService.getDateFormatted(
              data.salarySlip?.salaryVerifiedDate,
            );

        if (data.salarySlip?.salarySlipDate)
          data.salarySlip.salarySlipMonth = this.typeService.getMonthAndYear(
            data.salarySlip?.salarySlipDate,
          );
        const salaryApprovedName = await this.commonSharedService.getAdminData(
          data.salarySlip.approveById,
        );
        if (salaryApprovedName)
          data.salarySlip.adminName = salaryApprovedName?.fullName ?? null;
        data.salarySlip.createdAt = this.typeService.getDateFormatted(
          data.salarySlip?.createdAt,
        );
        const employeeDetails = JSON.parse(data.salarySlip.response);
        if (employeeDetails?.Type === 'Offer Letter') {
          data.salarySlip.type = 'Offer Letter';
        } else if (employeeDetails?.Type === 'BankStatement') {
          data.salarySlip.type = 'Bank Statement';
          if (employeeDetails?.bank_name)
            data.salarySlip.bankName = employeeDetails.bank_name;
        } else if (employeeDetails?.Type === 'Invalid document') {
          data.salarySlip.type = 'Invalid document';
        } else if (employeeDetails?.Type === 'Salary Slip') {
          data.salarySlip.type = 'Salary Slip';
        } else {
          data.salarySlip.type = 'Other Document';
        }
        delete data.salarySlip?.response;
        delete data.salarySlip?.salarySlipDate;
      }
      const statusData = data?.masterData?.status;
      // if  salary slip skiped
      if (data?.salarySlip === null && statusData?.salarySlip === 4) {
        const salarySlip: any = {};
        salarySlip.status = '4';
        data.salarySlip = salarySlip;
      }

      if (data.sectorId) {
        const sectorData: any = await this.commonSharedService.getSectorData(
          data.sectorId,
        );
        if (sectorData == k500Error) data.sector = null;
        data.sector = sectorData?.sectorName ?? null;
      }

      if (data.designationId) {
        const designationData: any =
          await this.commonSharedService.getDesignationData(data.designationId);
        if (designationData == k500Error) data.designation = null;
        data.designation = designationData?.designationName ?? null;
      }
      let lastPayDate: string = data?.otherInfo?.lastPayDate
        ? this.dateService.dateToReadableFormat(
            data?.otherInfo?.lastPayDate ?? '',
          ).readableStr
        : '';
      let nextPayDate: string = data?.otherInfo?.nextPayDate
        ? this.dateService.dateToReadableFormat(
            data?.otherInfo?.nextPayDate ?? '',
          ).readableStr
        : '';

      lastPayDate = lastPayDate?.split('-')?.join('/') ?? undefined;
      nextPayDate = nextPayDate?.split('-')?.join('/') ?? undefined;

      data.otherInfo = {
        ...data?.otherInfo,
        lastPayDate,
        nextPayDate,
      };

      if (data?.updatedCompanyName) {
        const companyChangeByAdmin =
          await this.commonSharedService.getAdminData(
            data?.companyNameChangeBy,
          );
        data.companyChangeByAdmin = companyChangeByAdmin?.fullName ?? '-';
        let userEnterCompanyName =
          data?.masterData?.otherInfo?.userEnterCompanyName;
        if (!userEnterCompanyName) {
          let emiHistory = await this.empHistoryRepo.getRowWhereData(
            ['companyName'],
            {
              where: { userId, companyNameChangeBy: { [Op.is]: null } },
              order: [['id', 'DESC']],
            },
          );
          if (emiHistory != k500Error && emiHistory?.companyName)
            userEnterCompanyName = emiHistory?.companyName;
        }
        if (userEnterCompanyName)
          data.userEnterCompanyName = userEnterCompanyName;
        delete data?.updatedCompanyName;
        delete data?.companyNameChangeBy;
      } else {
        delete data?.updatedCompanyName;
        delete data?.companyNameChangeBy;
        delete data?.companyChangeByAdmin;
      }

      delete data?.masterData;
      return data;
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  // #startregion user work basic details
  async fetchUserWorkBasicDetailsHistory(query) {
    try {
      const userId = query?.userId;
      if (!userId) return kParamMissing('userId');
      const empAttr = [
        'id',
        'updatedAt',
        'companyName',
        'companyUrl',
        'companyPhone',
        'commision',
        'startDate',
        'companyAddress',
        'otherInfo',
        'companyNameChangeBy',
        'updatedCompanyName',
      ];
      const empOptions: any = {
        where: {
          userId,
        },
        include: [
          { model: employmentDesignation, attributes: ['designationName'] },
          { model: employmentSector, attributes: ['sectorName'] },
          { model: employmentType, attributes: ['typeName'] },
        ],
        order: [['id', 'DESC']],
      };
      const empData = await this.empHistoryRepo.getTableWhereData(
        empAttr,
        empOptions,
      );
      if (empData === k500Error) return kInternalError;
      const finalData = [];
      for (let i = 0; i < empData.length; i++) {
        try {
          const ele = empData[i];
          let companyNameChangeBy = '-';
          if (ele?.companyNameChangeBy) {
            const adminData = await this.commonSharedService.getAdminData(
              ele?.companyNameChangeBy,
            );
            companyNameChangeBy = adminData?.fullName ?? '-';
          }
          const preparedObj: any = {
            id: ele?.id,
            updatedAt: ele?.updatedAt
              ? this.typeService.getDateFormated(ele?.updatedAt)
              : '-',
            companyName: ele?.companyName ?? '-',
            companyUrl: ele?.companyUrl != '' ? ele?.companyUrl : '-',
            companyPhone: ele?.companyPhone != '' ? ele?.companyPhone : '-',
            commision: ele?.commision ?? '-',
            startDate: ele?.startDate
              ? this.typeService.getDateFormated(ele?.startDate)
              : '-',
            companyAddress: ele?.companyAddress ?? '-',
            lastPayDate: ele?.otherInfo?.lastPayDate
              ? this.typeService.getDateFormated(ele?.otherInfo?.lastPayDate)
              : '-',
            nextPayDate: ele?.otherInfo?.nextPayDate
              ? this.typeService.getDateFormated(ele?.otherInfo?.nextPayDate)
              : '-',
            directorName: ele?.otherInfo?.directorName ?? '-',
            netPaySalary: ele?.otherInfo?.netPaySalary ?? '-',
            userCompanyPhone: ele?.otherInfo?.userCompanyPhone ?? '-',
            designation: ele?.designation?.designationName ?? '-',
            sector: ele?.sector?.sectorName ?? '-',
            employementType: ele?.employementTypeData?.typeName ?? '-',
            updatedCompanyName: ele?.updatedCompanyName ?? '-',
            companyNameChangeBy: companyNameChangeBy,
          };
          finalData.push(preparedObj);
        } catch (error) {}
      }
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async searchCompany(reqData) {
    try {
      // Params validation
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      const searchStr = reqData.searchStr;
      if (!searchStr) return kParamMissing('searchStr');
      const appType = reqData.appType;
      if (!validAppTypeValues.includes(appType)) {
        const userData = await this.userRepo.getRowWhereData(['appType'], {
          where: { id: userId },
        });
        if (userData === k500Error) return kInternalError;
        reqData.appType = userData.appType;
      }
      let list: any = [];
      list = await this.instaFinancial.searchCompany(searchStr);
      // if (!list?.length || list?.message) list = await this.proteanService.searchCompany(reqData);
      if (list?.message) return list;
      return [...new Set(list)];
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
}
