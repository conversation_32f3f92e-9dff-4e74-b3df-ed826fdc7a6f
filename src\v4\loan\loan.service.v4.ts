// Imports
import * as fs from 'fs';
import { Injectable } from '@nestjs/common';
import {
  DisbursementBanner,
  GlobalServices,
  MAX_DIS_TIME,
  MAX_DURATION,
  MINIMUM_INTEREST_FOR_FULL_PAY,
  APP_NAME,
  NBFC_ADDRESS,
  NBFC_NAME,
  SYSTEM_ADMIN_ID,
  UPI_SERVICE,
  disburseAmt,
  kInsuranceWaitingTag,
  valueInsurance,
  gIsPROD,
  UAT_PHONE_NUMBER,
  LOAN_AGREEMENT_COOLINGOFF_PERIOD,
  kInsuranceWaitingTagNBFC,
  GLOBAL_RANGES,
  kSelectedLoanAmount,
  GLOBAL_CHARGES,
  PERSONAL_LOAN_ICON,
  ECS_BOUNCE_CHARGE,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  kEmiIcon,
  KinsuranceBannerUrl,
  KInsurancePolicy,
  kLspMsg91Templates,
  kMsg91Templates,
  PAYMENT_VERIFY_RESPONSE,
  RedisKeys,
  UserStage,
} from 'src/constants/objects';
import {
  k400ErrorMessage,
  k422ErrorMessage,
  kBadRequest,
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
  kParamsMissing,
} from 'src/constants/responses';
import {
  DisbursementSuccessBody,
  DisbursementSuccessBodyOnTime,
  DisbursementSuccessTitle,
  DisbursementSuccessTitleOnTime,
  kAutoDebit,
  kAutoDebitNotInitilize,
  kAutoDebitNotSuccessFull,
  kCompleted,
  kInitiated,
  kInternalPolicy,
  kLoanMaintainSufficientBalance,
  kNoDataFound,
  kPleaseSelectValidNomineeRelationshipCode,
  kValidPhone,
  kPaymentMode,
  healthPolicy,
  kCollectionPhone,
  kDuplicateLoan,
  kRuppe,
  kHelpContact,
  kRazorpay,
  KICICIUPI,
  kSupportMail,
  nbfcInfoStr,
  kLspNoReplyMail,
  KLspSupportMail,
  kNoReplyMail,
  NBFCPlayStoreLink,
  lspPlaystoreLink,
  NBFCAppStoreLink,
  lspAppStoreLink,
  kLspHelpContactAfterDisbursement,
  lojPolicy,
  kSomthinfWentWrong,
  KICICIUPI2,
  KYESUPI,
  kReviewLink,
  kNbfcTermAndCondition,
  kNbfcPrivacyPolicy,
  kLspPrivacyPolicy,
  kLspTermAndCondition,
} from 'src/constants/strings';
import { EmiEntity } from 'src/entities/emi.entity';
import { esignEntity } from 'src/entities/esign.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { loanPurpose } from 'src/entities/loan.purpose.entity';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { UserSelfieEntity } from 'src/entities/user.selfie.entity';
import { LoanRepository } from 'src/repositories/loan.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { UserLoanDeclineRepository } from 'src/repositories/userLoanDecline.repository';
import { CalculationSharedService } from 'src/shared/calculation.service';
import { TypeService } from 'src/utils/type.service';
import { FileService } from 'src/utils/file.service';
import { UserRepository } from 'src/repositories/user.repository';
import { MasterEntity } from 'src/entities/master.entity';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import { EligibilitySharedService } from 'src/shared/eligibility.shared.service';
import { Op } from 'sequelize';

import { regUUID } from 'src/constants/validation';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { AutomationService } from 'src/thirdParty/automation/automation.service';
import { EMIRepository } from 'src/repositories/emi.repository';
import { BankingEntity } from 'src/entities/banking.entity';
import { KYCRepository } from 'src/repositories/kyc.repository';
import { CibilScoreRepository } from 'src/repositories/cibil.score.repository';
import { DisbursmentRepository } from 'src/repositories/disbursement.repository';
import { CryptService } from 'src/utils/crypt.service';
import { WhatsAppService } from 'src/thirdParty/whatsApp/whatsApp.service';
import { PromoCodeService } from 'src/shared/promo.code.service';
import {
  kloanApprovalTemplate,
  kKeyFactStatementPath,
} from 'src/constants/directories';
import { EmiSharedService } from 'src/shared/emi.service';
import { StringService } from 'src/utils/string.service';
import { SharedTransactionService } from 'src/shared/transaction.service';
import { EnvConfig } from 'src/configs/env.config';
import { RedisService } from 'src/redis/redis.service';
import { SequelOptions } from 'src/interfaces/include.options';
import { PaymentLinkEntity } from 'src/entities/paymentLink.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { APIService } from 'src/utils/api.service';
import { nPaymentVerify } from 'src/constants/network';
import * as FormData from 'form-data';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { DateService } from 'src/utils/date.service';
import { ESignSharedService } from 'src/shared/esign.shared.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { ChangeLogsEntity } from 'src/entities/change.logs.entity';
import { kAgreementExpiry, NUMBERS } from 'src/constants/numbers';
import { ElephantService } from 'src/thirdParty/elephant/elephant.service';
import { TallyService } from 'src/admin/tally/tally.service';

@Injectable()
export class LoanServiceV4 {
  constructor(
    private readonly automation: AutomationService,
    private readonly commonSharedService: CommonSharedService,
    private readonly fileService: FileService,
    private readonly loanDeclineRepo: UserLoanDeclineRepository,
    private readonly masterRepo: MasterRepository,
    private readonly repository: LoanRepository,
    private readonly typeService: TypeService,
    private readonly strService: StringService,
    private readonly userRepo: UserRepository,
    private readonly transactionRepo: TransactionRepository,
    private readonly loanRepo: LoanRepository,
    private readonly promoCodeService: PromoCodeService,
    private readonly cryptService: CryptService,
    private readonly whatsAppService: WhatsAppService,
    // Repositories
    private readonly emiRepo: EMIRepository,
    private readonly cibilScoreRepo: CibilScoreRepository,
    private readonly kycRepo: KYCRepository,
    private readonly disbursedRepo: DisbursmentRepository,
    // Repositories
    // Shared services
    private readonly sharedCalculation: CalculationSharedService,
    private readonly sharedEligiblityService: EligibilitySharedService,
    private readonly sharedNotificationService: SharedNotificationService,
    private readonly sharedEmi: EmiSharedService,
    private readonly sharedTransactionService: SharedTransactionService,
    private readonly redisService: RedisService,
    private readonly repoManager: RepositoryManager,
    private readonly apiService: APIService,
    private readonly dateService: DateService,
    private readonly esignSharedService: ESignSharedService,
    private readonly slack: SlackService,
    private readonly errorContextService: ErrorContextService,
    private readonly elephantService: ElephantService,
    private readonly tallyService: TallyService,
  ) {}

  async rejectReasonList(reqData) {
    try {
      // Params validation
      const loanId = reqData.loanId;
      if (!loanId) return kParamMissing('loanId');

      // Get reason list
      const attributes = ['id', 'userDeclineReasonTitle'];
      const options = {
        where: { userDeclineStatus: '0' },
      };
      const reasonList = await this.loanDeclineRepo.getTableWhereData(
        attributes,
        options,
      );
      if (reasonList == k500Error) return kInternalError;
      return reasonList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async decline(reqData) {
    try {
      // Params validation
      const loanId = reqData.loanId;
      if (!loanId) return kParamMissing('loanId');
      const reasonId = reqData.reasonId;
      if (!reasonId) return kParamMissing('reasonId');
      let reason = reqData?.reason;
      // if (!reason) return kParamMissing('reason');
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      if (reasonId != 5) {
        const dReason = await this.loanDeclineRepo.getRowWhereData(
          ['userDeclineReasonTitle'],
          { where: { id: reasonId } },
        );
        if (dReason === k500Error) return kInternalError;
        reason = dReason?.userDeclineReasonTitle;
      }

      const loanInclude: any = { model: loanTransaction };
      loanInclude.attributes = ['loanStatus'];
      const include = [loanInclude];
      const attributes = ['id', 'status', 'loanAcceptStatus', 'dates'];
      const options = { include, where: { loanId } };
      const masterData = await this.masterRepo.getRowWhereData(
        attributes,
        options,
      );
      if (masterData == k500Error) return kInternalError;
      if (!masterData) return k422ErrorMessage('No data found');
      const statusData = masterData.status ?? {};

      // Loan validation
      const loanStatus = statusData.loan;
      const loanData = masterData.loanData ?? {};
      // Loan active
      if (loanStatus == 6 || loanData.loanStatus == 'Active')
        return k422ErrorMessage(
          'You can not reject the loan as the loan is active',
        );
      // Loan complete
      else if (loanStatus == 7 || loanData.loanStatus == 'Complete')
        return k422ErrorMessage(
          'You can not reject the loan as the loan is already completed',
        );
      // e-Sign generated
      else if (statusData.eSign != -1)
        return k422ErrorMessage(
          'You can not reject the loan as the eSign is already generated',
        );

      // Update loan data
      let updatedData: any = {
        loanStatus: 'Rejected',
        manualVerification: '2',
        declineId: reasonId,
        userReasonDecline: reason,
        verifiedDate: this.typeService.getGlobalDate(new Date()).toJSON(),
        loanRejectDateTime: new Date().toJSON(),
      };
      let updateResult = await this.repository.updateRowData(
        updatedData,
        loanId,
      );
      if (updateResult == k500Error) return kInternalError;
      const userData = await this.userRepo.getRowWhereData(['id', 'stage'], {
        where: { id: userId },
      });
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);

      // Update master data
      statusData.loan = 2;
      statusData.eligibility = 2;
      let loanAcceptStatus = masterData?.loanAcceptStatus;
      const dates = masterData.dates ?? {};

      if (userData.stage === UserStage.LOAN_ACCEPT) {
        loanAcceptStatus = 2;
        dates.loanAccept = new Date().getTime();
      } else if (userData.stage === UserStage.MANDATE) {
        statusData.eMandate = 2;
        dates.eMandate = new Date().getTime();
      } else if (userData.stage === UserStage.ESIGN) {
        statusData.eSign = 2;
        dates.eSign = new Date().getTime();
      } else if (userData.stage === UserStage.DISBURSEMENT) {
        statusData.disbursement = 2;
        dates.disbursement = new Date().getTime();
      } else if (userData.stage === UserStage.BANKING) {
        statusData.bank = 2;
        dates.banking = new Date().getTime();
      } else if (userData.stage === UserStage.PAN) {
        statusData.pan = 2;
        dates.pan = new Date().getTime();
      } else if (userData.stage === UserStage.ON_HOLD) {
        statusData.onHold = 2;
        dates.onHold = new Date().getTime();
      }
      updatedData = {
        status: statusData,
        loanAcceptStatus,
        dates: dates,
      };
      updateResult = await this.masterRepo.updateRowData(
        updatedData,
        masterData.id,
      );
      if (updateResult == k500Error) return kInternalError;
      await this.sharedEligiblityService.checkAndRejectSteps(userId, reason);

      return { needUserInfo: true };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Update the salary date if user is eligible for that
  async updateEmiDate(reqData) {
    // Params validation
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    if (!regUUID(userId)) return kInvalidParamValue('userId');
    let emiDate = reqData.emiDate;
    if (!emiDate) return kParamMissing('emiDate');
    if (isNaN(emiDate)) return kInvalidParamValue('emiDate');
    if (emiDate < 1 || emiDate > 31) return kInvalidParamValue('emiDate');
    const prepareScreen = reqData?.prepareScreen == true ? true : false;
    const typeOfDevice = reqData.typeOfDevice;

    // Get loan data from database
    const attributes = ['id', 'loanStatus', 'emiSelection', 'loanAmount'];
    const options = { order: [['id', 'DESC']], where: { userId } };
    const loanData = await this.loanRepo.getRowWhereData(attributes, options);
    if (loanData === k500Error) return kInternalError;
    if (!loanData) return k422ErrorMessage(kNoDataFound);

    // Temporary fallback
    let canUpdate = true;
    // if (emiDate == 5) {
    //   const trace_key = `${loanData.id}_${RedisKeys.EMI_DATE_SELECTION}`;
    //   const existing_trace = await this.redisService.get(trace_key);
    //   if (!existing_trace) {
    //     const approvedSalaryDate = await this.getApprovedSalaryDate(
    //       loanData.id,
    //     );
    //     const target_date = new Date();
    //     const target_month = target_date.getMonth();
    //     target_date.setDate(approvedSalaryDate);
    //     const new_target_month = target_date.getMonth();
    //     if (target_month == new_target_month) {
    //       target_date.setDate(target_date.getDate() + 1);
    //     }
    //     emiDate = target_date.getDate();
    //     await this.redisService.set(trace_key, 'Trace exists !', 84000);
    //     canUpdate = false;
    //   }
    // }

    // Validation
    const emiSelection = loanData.emiSelection ?? {};
    if (!emiSelection?.eligibleEmiDates) {
      return k422ErrorMessage(
        'Salary date change can not proceed at this moment!',
      );
    }
    // Web is having older calender ui
    if (
      !emiSelection.eligibleEmiDates.includes(emiDate) &&
      typeOfDevice != '2'
    ) {
      return kInvalidParamValue('emiDate');
    }

    const loanId = loanData.id;
    reqData.loanId = loanId;

    // Temporary -> Fallback
    if (canUpdate) {
      const updatedData = {
        emiSelection: {
          selectedOn: Math.floor(new Date().getTime() / 1000),
          selectedEmiDate: emiDate,
        },
      };
      await this.loanRepo.updateRowData(updatedData, loanId);
    }

    // Refresh the emi calculation based on new salary date
    const score = await this.sharedEligiblityService.getAmountRange(loanId, {});
    if (score.message) return score;

    reqData.amount = +(
      reqData.amount ??
      score?.approvedAmount ??
      loanData?.loanAmount
    );
    if (prepareScreen) return await this.acceptAmountCharges(reqData);
    return { score };
  }

  private async getApprovedSalaryDate(loanId) {
    const verifiedAttr = ['newData'];
    const verifiedOptions = {
      order: [['id', 'DESC']],
      where: {
        newData: { [Op.ne]: '-' },
        loanId,
        type: 'Verification',
        subType: 'Salary Date',
      },
    };
    const changedData = await this.repoManager.getRowWhereData(
      ChangeLogsEntity,
      verifiedAttr,
      verifiedOptions,
    );
    if (changedData == k500Error) throw new Error();

    const attributes = ['salaryDate'];
    const options = { order: [['id', 'DESC']], where: { loanId } };
    const bankData = await this.repoManager.getRowWhereData(
      BankingEntity,
      attributes,
      options,
    );
    if (!bankData) return k422ErrorMessage(kNoDataFound);
    if (bankData == k500Error) throw new Error();
    if (!bankData.salaryDate) throw new Error();

    return bankData.salaryDate;
  }

  async acceptAmount(reqData) {
    try {
      // Params validation
      const loanId = reqData.loanId;
      if (!loanId) return kParamMissing('loanId');
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      const amount = reqData.amount;
      if (!amount) return kParamMissing('amount');
      const result = await this.sharedCalculation.acceptAmount(reqData);
      if (result?.ipDecline || result?.message) return result;
      /* Taking default value as true for not to affecting in between users
      when we go live with dynamic insurance flow */
      // KFS flow
      const kfsData = await this.getKeyFactStatement(loanId);
      if (kfsData.message) return kfsData;

      await this.redisService.set(
        `CRM_WAIT_LOAN_${loanId}`,
        JSON.stringify(loanId),
        NUMBERS.TEN_MINUTES_IN_SECONDS,
      );

      return await this.acceptKFS({ loanId });
      //KFS PREVIEW hide
      // return { needUserInfo: true, kfsData };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getKeyFactStatement(id) {
    try {
      const selfieInclude: any = { model: UserSelfieEntity };
      selfieInclude.attributes = ['image'];
      const userInclude = {
        model: registeredUsers,
        attributes: ['id', 'fullName', 'uniqueId'],
        include: [selfieInclude],
      };
      const attributes = [
        'id',
        'approvedDuration',
        'interestRate',
        'loanFees',
        'loanStatus',
        'manualVerification',
        'netEmiData',
        'netApprovedAmount',
        'stampFees',
        'userId',
        'charges',
        'insuranceDetails',
        'insuranceOptValue',
        'processingFees',
        'appType',
        'eligibilityDetails',
        'createdAt',
      ];
      const options = {
        where: { id },
        include: [userInclude],
      };
      const loanData = await this.repository.getRowWhereData(
        attributes,
        options,
      );
      if (loanData === k500Error) return kInternalError;
      const KFSData: any = await this.convertDataToObject(loanData);
      if (KFSData?.message) return KFSData;
      const path = kKeyFactStatementPath;
      const KFSDoc: any = await this.addDynamicValues(KFSData, path);
      if (KFSDoc.message) return KFSDoc;
      return KFSDoc;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async convertDataToObject(loanData) {
    try {
      const netApprovedAmount = +loanData?.netApprovedAmount;
      const showLendingPartner = loanData?.appType == 0 ? true : false;
      const userData = loanData?.registeredUsers;
      const loanCharges = loanData?.charges ?? {};
      const premiumAmount = loanData?.insuranceDetails?.totalPremium ?? 0;
      const emiDetails: any = this.esignSharedService.getEmiDetails(
        loanData?.netEmiData,
      );
      if (emiDetails === k500Error) return emiDetails;
      const emiData = emiDetails?.emiData ?? [];
      const totalInterest =
        parseFloat(
          emiDetails?.total?.interest.replace('₹', '').replace(/,/g, ''),
        ) ?? 0;
      const totalRepayAmount = emiDetails?.total?.amount ?? 0;

      let aprCharges = 0;
      const gstAmount = loanCharges?.gst_amt ?? 0;
      const percentageChange =
        ((totalInterest + +loanData?.loanFees - gstAmount + +premiumAmount) /
          netApprovedAmount) *
        100;
      aprCharges = (percentageChange * 365) / +loanData.approvedDuration;
      aprCharges = +aprCharges.toFixed(2);

      const processingFeePer = +(
        loanData?.processingFees ?? GLOBAL_CHARGES.PROCESSING_FEES
      );
      let processingFee = this.typeService.manageAmount(
        (netApprovedAmount * processingFeePer) / 100,
      );
      processingFee = this.typeService.amountNumberWithCommas(
        processingFee.toFixed(2),
      );

      const documentFeePer = loanCharges?.doc_charge_per ?? '';
      const documentFee = this.typeService.amountNumberWithCommas(
        loanCharges?.doc_charge_amt.toFixed(2),
      );

      const onlineConvenienceFee = this.typeService.manageAmount(
        loanCharges?.insurance_fee ?? 0,
      );

      // const collateralChargeAmount =
      //   loanData?.charges['collateral_charge_amt'] ?? 0 > 0
      //     ? +(loanData?.charges['collateral_charge_amt']).toFixed(2)
      //     : 0;

      const annualInterest =
        loanData?.eligibilityDetails?.anummInterest ??
        +(loanData.interestRate * 365).toFixed();

      const disbursementAmount = this.typeService.manageAmount(
        netApprovedAmount -
          (+(loanData?.loanFees ?? 0).toFixed(2) +
            +(loanData?.stampFees ?? 0).toFixed(2) +
            +premiumAmount.toFixed(2)),
        disburseAmt,
      );
      const totalCharge = loanData?.loanFees + premiumAmount;
      let totalDuePrincipal = 0;
      // Calculate Outstanding principal....
      emiData.map((emi, i) => {
        const principalAmount =
          +emi?.principal.replace('₹', '').replace(/,/g, '') || 0;
        const due =
          i == 0 ? netApprovedAmount : netApprovedAmount - totalDuePrincipal;
        emi.duePrincipal =
          kRuppe + this.typeService.amountNumberWithCommas(due);
        totalDuePrincipal += principalAmount;
      });
      const KFSData: any = {
        // NBFC and APP details....
        appName: APP_NAME,
        showLendingPartner,
        nbfcName: NBFC_NAME,
        nbfcAddress: NBFC_ADDRESS,
        nbfcLogo: EnvConfig.url.nbfcLogo,
        nbfcIrmodel: EnvConfig.nbfc.nbfcIrmodel,

        // User details....
        customerId: userData?.uniqueId,
        borrowerName: userData?.fullName,
        selfieImg: userData?.selfieData?.image,

        // Loan details....
        loanId: loanData?.id,
        loanAmount:
          kRuppe + this.typeService.amountNumberWithCommas(netApprovedAmount),
        loanTenure: loanData?.approvedDuration,
        interestRate: loanData?.interestRate,
        signingDate: this.typeService.getDateFormatted(new Date()),
        validTill: kAgreementExpiry,
        totalInterestAmount:
          kRuppe + this.typeService.amountNumberWithCommas(totalInterest),
        totalChargesAmount:
          kRuppe +
          this.typeService.amountNumberWithCommas(
            (loanData?.loanFees).toFixed(2),
          ),
        disbursementAmount:
          kRuppe + this.typeService.amountNumberWithCommas(disbursementAmount),
        totalRepayAmount,
        insuranceAmount:
          kRuppe +
          this.typeService.amountNumberWithCommas(premiumAmount.toFixed(2)),

        totalCharges:
          kRuppe +
          this.typeService.amountNumberWithCommas(totalCharge.toFixed(2)),
        // Fee/Charges details....
        annualInterest: annualInterest,
        processingFeePer,
        processingFee: kRuppe + processingFee,
        documentFeePer,
        documentFee: kRuppe + documentFee,
        onlineConvenienceFee: kRuppe + onlineConvenienceFee.toFixed(2),
        igstFee:
          kRuppe +
          this.typeService.amountNumberWithCommas(gstAmount.toFixed(2)),
        legalCharge:
          kRuppe +
          this.typeService.amountNumberWithCommas(GLOBAL_CHARGES.LEGAL_CHARGE),
        foreclosureChargePer: GLOBAL_CHARGES.FORECLOSURE_PERC,
        bounceCharge: kRuppe + ECS_BOUNCE_CHARGE,
        aprChargesPer: aprCharges,

        // EMI details....
        emiData,
        emiCount: emiData?.length,
        firstEmiDate: emiData?.[0]?.dueDate,
        firstEmiAmount: emiData?.[0]?.dueAmount,

        // Grievance details....
        grievanceOfficer: EnvConfig.nbfc.nbfcGrievanceOfficer,
        grievanceNumber: EnvConfig.number.grievanceNumber,
        companyGrievanceEmail: EnvConfig.mail.grievanceMail,
        nbfcPartners: EnvConfig.nbfc.nbfcPartner,
        nbfcGrievance: EnvConfig.nbfc.nbfcGrievance,

        // Other details....
        Cool_off_period: LOAN_AGREEMENT_COOLINGOFF_PERIOD,
        termsAndCondition: showLendingPartner
          ? kLspTermAndCondition
          : kNbfcTermAndCondition,
        privacyPolicy: showLendingPartner
          ? kLspPrivacyPolicy
          : kNbfcPrivacyPolicy,
      };
      // if (collateralChargeAmount) {
      // KFSData.collateralCharges = GLOBAL_CHARGES.COLLATERAL_CHARGE;
      //   KFSData.collateralChargeAmount =
      //     '₹' + this.typeService.amountNumberWithCommas(collateralChargeAmount);
      // }

      return KFSData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async addDynamicValues(dynamicValues: any, filePath: string) {
    try {
      if (dynamicValues.selfieImage) {
        const fileName = new Date().getTime().toString() + '.png';
        const imagePath = './upload/' + fileName;
        try {
          const base64Content = await this.typeService.getBase64FromImgUrl(
            dynamicValues.selfieImage,
          );
          await fs.writeFileSync(imagePath, base64Content, 'base64');
        } catch (error) {
          this.errorContextService.throwAndSetCtxErr(error);
        }
        const sty = 'style="border-radius:10px;height:90%;margin-top:10%"';
        dynamicValues.selfieCSS = sty;
        await this.fileService.removeFile(imagePath);
      }

      const file = await this.fileService.hbsHandlebars(
        filePath,
        dynamicValues,
      );
      if (file == k500Error) return file;

      return file;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async acceptKFS(reqData) {
    try {
      // Params validation
      const loanId = reqData.loanId;
      if (!loanId) return kParamMissing('loanId');

      const loanInclude: any = { model: loanTransaction };
      loanInclude.attributes = ['loanStatus', 'netApprovedAmount'];
      const include = [loanInclude];
      const attributes = ['id', 'status', 'userId', 'dates', 'kfsStatus'];
      const options = { include, where: { loanId } };
      const masterData = await this.masterRepo.getRowWhereData(
        attributes,
        options,
      );
      if (!masterData) return k422ErrorMessage(kNoDataFound);
      if (masterData == k500Error) return kInternalError;

      const masterId = masterData.id;
      const statusData = masterData.status ?? {};
      const dates = masterData.dates ?? {};
      const loanData = masterData.loanData ?? {};

      if (masterData.kfsStatus == 1)
        return k422ErrorMessage('Key fact statement can not accepted');
      else if (loanData.loanStatus != 'InProcess')
        return k422ErrorMessage('Key fact statement can not accepted');

      // // Update loan data
      let updatedData: any = { loanStatus: 'Accepted', kfsStatus: 1 };
      let updateResult = await this.repository.updateRowData(
        updatedData,
        loanId,
      );
      if (updateResult == k500Error) return kInternalError;

      // Update master data
      updatedData = { kfsStatus: 1, kfsAcceptDate: new Date() };
      updateResult = await this.masterRepo.updateRowData(updatedData, masterId);
      if (updateResult == k500Error) return kInternalError;

      return { needUserInfo: true };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getActiveLoanDetails(reqData) {
    try {
      // Params validation
      const loanId = reqData.loanId;
      if (!loanId) return kParamMissing('loanId');

      // Get loan data

      // Transaction
      const transInclude: any = { model: TransactionEntity };
      transInclude.attributes = ['id', 'paidAmount', 'status', 'utr', 'emiId'];
      // EMI
      const emiInclude: any = { model: EmiEntity };
      emiInclude.attributes = [
        'id',
        'emi_date',
        'emi_amount',
        'payment_done_date',
        'payment_status',
        'payment_due_status',
        'penalty',
        'penalty_days',
        'principalCovered',
        'interestCalculate',
        'partPaymentPenaltyAmount',
        'settledId',
        'waiver',
        'paid_waiver',
        'unpaid_waiver',
      ];
      // Disbursement
      const disbursementIn: any = { model: disbursementEntity };
      disbursementIn.attributes = [
        'amount',
        'utr',
        'account_number',
        'bank_name',
      ];
      // eSign
      const eSignInclude: any = { model: esignEntity };
      eSignInclude.attributes = ['signed_document_upload'];
      // Purpose
      const purposeInclude: any = { model: loanPurpose };
      purposeInclude.attributes = ['purposeName', 'image_url'];
      const include = [
        disbursementIn,
        eSignInclude,
        emiInclude,
        purposeInclude,
        transInclude,
      ];

      const attributes = [
        'id',
        'netEmiData',
        'loanFees',
        'stampFees',
        'interestRate',
        'loan_disbursement_date',
        'netApprovedAmount',
        'approvedDuration',
        'loanStatus',
        'isPartPayment',
        'nocURL',
        'insuranceId',
        'insuranceDetails',
        'charges',
        'processingFees',
        'appType',
      ];
      const options = { include, where: { id: loanId } };
      const loanData = await this.repository.getRowWhereData(
        attributes,
        options,
      );

      if (loanData == k500Error) return kInternalError;
      if (!loanData) return k422ErrorMessage(kNoDataFound);
      const emiData = loanData?.emiData;
      const attr = [
        'id',
        'emiId',
        'paidAmount',
        'completionDate',
        'status',
        'createdAt',
        'subscriptionDate',
        'updatedAt',
      ];
      const tempDate = [];
      let isSettled = false;
      let maxEMIId = 0;
      let maxPenaltyDays = 0;
      let settlementAmount = 0;
      emiData.forEach((ele) => {
        const total = +ele?.waiver + +ele?.paid_waiver + +ele?.unpaid_waiver;
        if (total > 0) isSettled = true;
        settlementAmount += total;
        if (ele?.payment_status === '0') tempDate.push(ele.emi_date);
        if (ele?.payment_status === '0' && ele?.payment_due_status === '1') {
          if (maxEMIId < ele.id) {
            maxEMIId = ele.id;
            maxPenaltyDays = ele?.penalty_days;
          }
        }
      });
      const option = {
        where: {
          loanId,
          subSource: kAutoDebit,
          subscriptionDate: tempDate,
        },
        order: [['id']],
      };
      const autoPayData = await this.transactionRepo.getTableWhereData(
        attr,
        option,
      );
      const autoPayDetail = [];
      autoPayData.forEach((d) => {
        try {
          const autoPayObj = [];
          let isPengingAutodebit = false;
          const emiObj = {};
          autoPayObj.push({
            name: 'Repayment requested to your bank',
            date: this.typeService.getGlobalDate(d.createdAt),
          });

          if (d?.status == 'INITIALIZED') {
            isPengingAutodebit = true;
            autoPayObj.push({
              name: 'Awaiting response from bank',
              isPending: d?.status == 'INITIALIZED' ? true : false,
            });
          } else
            autoPayObj.push({
              name: 'Repayment Status',
              date: d?.completionDate ?? d?.updatedAt,
              status: d?.status == kCompleted,
            });

          emiObj['id'] = d?.emiId;
          const emiDate = this.typeService.getGlobalDate(d.subscriptionDate);
          emiObj['EMI date'] = emiDate;
          emiObj['EMI Amount'] = d?.paidAmount;
          if (isPengingAutodebit) emiObj['stepperInfo'] = autoPayObj;
          let autoDebitMsg = '';
          if (isPengingAutodebit) autoDebitMsg = kLoanMaintainSufficientBalance;
          emiObj['autoPlaceMess'] = autoDebitMsg;
          const diff = this.typeService.dateDifference(
            emiDate,
            new Date(),
            'Hours',
          );
          emiObj['autoPlaceNotSuccess'] = isPengingAutodebit
            ? diff > 36
              ? kAutoDebitNotSuccessFull
              : ''
            : kAutoDebitNotInitilize;
          emiObj['pendingAutodebit'] = isPengingAutodebit;
          const find = autoPayDetail.find((f) => f['EMI id'] == d?.emiId);
          if (!find) autoPayDetail.push(emiObj);
        } catch (error) {}
      });

      const fullPayData = await this.sharedCalculation.getFullPaymentData({
        loanId,
      });
      if (fullPayData?.message) return fullPayData;

      let paymentSource = await this.commonSharedService.getServiceName(
        kPaymentMode,
      );
      if (!paymentSource) paymentSource = GlobalServices.PAYMENT_MODE;
      const data: any = { isUpiServiceAvailable: UPI_SERVICE };
      data.fullPayAmount = fullPayData.totalAmount;

      /// Get part min payment
      const TodayDate = new Date().toJSON();
      const range = this.typeService.getUTCDateRange(TodayDate, TodayDate);
      const dateRange = { [Op.gte]: range.fromDate, [Op.lte]: range.endDate };
      const partOption = {
        where: {
          loanId,
          status: 'INITIALIZED',
          type: 'PARTPAY',
          adminId: { [Op.ne]: SYSTEM_ADMIN_ID },
          createdAt: dateRange,
        },
        order: [['id', 'DESC']],
      };
      const partPayData = await this.transactionRepo.getRowWhereData(
        ['id', 'paidAmount', 'createdAt'],
        partOption,
      );

      let partPaidAmount = 0;
      /// Check Already paid after create link
      if (partPayData?.id) {
        const partPaidOption = {
          where: {
            loanId,
            status: 'COMPLETED',
            id: { [Op.gt]: partPayData?.id },
            createdAt: dateRange,
          },
          order: [['id', 'DESC']],
        };
        const partPaidData = await this.transactionRepo.getRowWhereData(
          ['paidAmount', 'createdAt'],
          partPaidOption,
        );
        partPaidAmount = partPaidData?.paidAmount ?? 0;
      }

      /// Check for part payment enable/disable
      data.isPartPayment = false;
      data.minPartAmount = 0;
      data.collectionPhone = EnvConfig.number.collectionNumber;
      if (maxPenaltyDays > 90 && loanData.isPartPayment !== 0) {
        data.isPartPayment = true;
        if (partPaidAmount > 0) partPayData.paidAmount = 1000;
        data.minPartAmount = partPayData?.paidAmount ?? 1000;
        if (data.fullPayAmount < 1000) data.minPartAmount = data.fullPayAmount;
      }
      data.minPartAmount = parseInt(data.minPartAmount);

      data.paymentOptions = [
        {
          name: 'Card / Netbanking',
          source: paymentSource,
          subSource: 'APP',
          androidStatus: true,
          iosStatus: true,
          serviceMode: 'WEB',
          linkSource: 'INAPP',
        },
      ];

      const insurance = await this.getInsuranceData(loanData?.id);
      // Add other loan details with formatting
      this.syncDashboardInfo(loanData, data, autoPayDetail, insurance);
      const repaymentData: any = await this.funEMIAndRepaymentData({
        loanId,
      });
      if (repaymentData.message) data.transactionData = [];
      else data.transactionData = repaymentData;
      if (data.loanStatus == 'Complete' && isSettled)
        data.loanStatus = 'Settled';
      if (isSettled) data.settlementAmount = settlementAmount;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funEMIAndRepaymentData(data) {
    try {
      if (!data.loanId) return kParamMissing('loanId');
      const attributes = [
        'id',
        'loanStatus',
        'userId',
        'netEmiData',
        'interestRate',
        'loan_disbursement_date',
      ];

      const transactionInclude = {
        model: TransactionEntity,
        required: false,
        order: [['id', 'DESC']],
        attributes: [
          'id',
          'emiId',
          'paidAmount',
          'completionDate',
          'source',
          'type',
          'subSource',
          'utr',
          'principalAmount',
          'interestAmount',
          'penaltyAmount',
          'userId',
          'createdAt',
        ],
        where: { status: 'COMPLETED' },
      };
      const emiInlude = {
        model: EmiEntity,
        attributes: ['id', 'emi_date', 'pay_type', 'payment_due_status'],
      };

      const options = {
        where: {
          id: data.loanId,
        },
        include: [transactionInclude, emiInlude],
      };
      const loanData = await this.loanRepo.getRowWhereData(attributes, options);
      if (loanData === k500Error) return kInternalError;
      const transactionData = loanData?.transactionData ?? [];
      transactionData.sort((a, b) => b.id - a.id);
      const finalData = [];
      transactionData.forEach((ele) => {
        try {
          const emiData = loanData?.emiData;
          emiData.sort((a, b) => a.id - b.id);
          ele.emiNum = '';
          emiData.forEach((el, index) => {
            try {
              const emiDate = this.typeService.getGlobalDate(el.emi_date);
              const paymentDate = this.typeService.getGlobalDate(
                ele.completionDate,
              );
              if (
                (ele.emiId == el.id || ele.type == 'FULLPAY') &&
                emiDate.getTime() > paymentDate.getTime() &&
                el.payment_due_status == 0
              )
                ele.payType = 'PRE-PAID';
              else if (
                (ele.emiId == el.id || ele.type == 'FULLPAY') &&
                emiDate.getTime() <= paymentDate.getTime() &&
                el.payment_due_status == '0'
              )
                ele.payType = 'PAID-ontime';
              else if (
                (ele.emiId == el.id || ele.type == 'FULLPAY') &&
                emiDate.getTime() < paymentDate.getTime() &&
                el.payment_due_status == 1
              )
                ele.payType = 'PAID-delayed';
              if (ele?.emiId == el.id) ele.emiNum = `${index + 1}`;
              if (ele.type == 'FULLPAY' && el.pay_type == 'FULLPAY') {
                if (ele.emiNum == '') ele.emiNum = `${index + 1}`;
                else ele.emiNum += ` & ${index + 1}`;
              }
            } catch (error) {}
          });
          if (ele.type == 'REFUND') ele.payType = 'PAID';
          finalData.push(ele);
        } catch (error) {}
      });
      return finalData.sort((a, b) => b.id - a.id);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private syncDashboardInfo(loanData, data, autoPayDetail, insuranceData) {
    try {
      const appType = loanData?.appType;
      const appInsuranceLable =
        appType == 1 ? kInsuranceWaitingTagNBFC : kInsuranceWaitingTag;
      const transList = loanData.transactionData ?? [];
      transList.sort((b, a) => a.id - b.id);
      data.totalRepaidAmount = 0;
      transList.forEach((el) => {
        try {
          if (el.status == kCompleted && el.type != 'REFUND') {
            data.totalRepaidAmount += el.paidAmount ?? 0;
            try {
              loanData?.emiData.forEach((emi) => {
                if (emi.id === el.emiId) emi.utr = el.utr;
              });
            } catch (error) {}
          } else if (el.status == kInitiated && el.subSource == kAutoDebit)
            data.autoPayData = `Autodebit for your EMI due of #*Rs.${el.paidAmount}# has been initiated, Please keep sufficient amount in your bank account to avoid ECS penalty charges. Auto debit payment will reflect on the app by #*end of the day.`;
        } catch (error) {}
      });
      loanData?.emiData.forEach((emi) => {
        if (!emi?.utr && emi.payment_status == '1') {
          const find = transList.find(
            (f) => f.status == kCompleted && !f.emiId,
          );
          emi.utr = find?.utr;
        }
      });

      // EMI details
      const emiData = [];
      let totalOutstandingAmount = 0;
      let totalInterest = 0;
      loanData?.emiData.forEach((element) => {
        try {
          totalInterest += +element.interestCalculate;
          totalOutstandingAmount += element.principalCovered ?? 0;
          totalOutstandingAmount += element.interestCalculate ?? 0;
          totalOutstandingAmount +=
            (element?.penalty ?? 0) + (element?.partPaymentPenaltyAmount ?? 0);

          const find = autoPayDetail.find((f) => f?.id == element.id);
          element.info = find ? find : {};
          emiData.push(element);
        } catch (error) {}
      });
      emiData.sort((a, b) => a?.id - b?.id);
      data.emiData = emiData;
      data.totalOutstandingAmount = parseFloat(
        totalOutstandingAmount.toFixed(2),
      );
      const insuranceId = loanData?.insuranceId;
      const insurance = loanData?.insuranceDetails;
      const charges = loanData?.charges;
      const processingFeePerc = loanData.processingFees;
      const approvedLoanAmount = +loanData.netApprovedAmount;
      const processingFees = (approvedLoanAmount * processingFeePerc) / 100;

      // Dashboard data
      const dashboardData = {};
      const stampFees = (+loanData['stampFees']).toFixed(2);
      dashboardData['Bank account'] =
        loanData?.disbursementData[0]?.account_number ?? '-';
      dashboardData['Bank name'] =
        loanData?.disbursementData[0]?.bank_name ?? '-';
      dashboardData['UTR'] = loanData?.disbursementData[0]?.utr ?? '-';
      dashboardData['Loan amount'] = '₹ ' + loanData['netApprovedAmount'];
      dashboardData['No. of loan day'] = loanData['approvedDuration'];
      dashboardData['Interest'] = totalInterest.toFixed(2);
      dashboardData['Interest per day'] =
        parseFloat(loanData['interestRate']).toFixed(2) + '%';
      dashboardData['Processing fee'] = '₹ ' + processingFees.toFixed(2);
      dashboardData['Stamp duty fees + Service charges'] = '₹ ' + stampFees;
      dashboardData['Net amount disbursed'] =
        '₹ ' + ((loanData?.disbursementData[0]?.amount ?? 0) / 100).toFixed(2);
      dashboardData['Documentation charges'] =
        '₹ ' + (charges?.doc_charge_amt).toFixed(2);
      dashboardData['GST @18%'] = '₹ ' + (charges?.gst_amt).toFixed(2);
      dashboardData['insuranceId'] = insuranceId;
      if (insuranceId)
        dashboardData['Insurance premium amount'] =
          '₹ ' + (insurance?.totalPremium).toFixed(2);
      if (charges?.insurance_fee)
        dashboardData['Online convenience fees'] =
          '₹ ' + (charges?.insurance_fee).toFixed(2);

      data.dashboardData = dashboardData;
      data.purpose = loanData?.purpose?.purposeName ?? '-';
      data.purposeImage = loanData?.purpose?.image_url ?? null;
      data.loanStatus = loanData?.loanStatus ?? '-';
      data.loanAgreement = loanData?.eSignData?.signed_document_upload ?? '';
      data.nocURL = loanData?.nocURL ?? '';
      // this is tempery for testing
      if (loanData?.insuranceId) {
        if (
          insuranceData?.insurancePolicy1 ||
          insuranceData?.insurancePolicy2
        ) {
          if (insuranceData.insurancePolicy1) {
            data.insurancePolicy1 = 'Health protector policy';
            data.insuranceURL = insuranceData.insurancePolicy1;
          }
          if (insuranceData?.insurancePolicy2) {
            data.insurancePolicy2 = 'Emi protector policy';
            data.insuranceURL1 = insuranceData?.insurancePolicy2;
          }
        } else data.insuranceLable = appInsuranceLable;
      }

      data.disbursedDate = this.typeService.getGlobalDate(
        loanData.loan_disbursement_date,
      );
      data.paymentUIInfo = this.getPaymentUIInfo(emiData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region
  private async getInsuranceData(loanId) {
    const data = { insurancePolicy1: '', insurancePolicy2: '' };
    try {
      const result = await this.elephantService.getInsuranceDataOfLoan(loanId);
      if (!result || result?.message) return data;
      if (result?.insuranceURL) data.insurancePolicy1 = result?.insuranceURL;
      if (result?.insuranceURL1) data.insurancePolicy2 = result?.insuranceURL1;
    } catch (error) {}
    return data;
  }
  //#endregion

  private getPaymentUIInfo(emiList: any[]) {
    try {
      const uiInfo: any = {};

      for (let index = 0; index < emiList.length; index++) {
        try {
          const emiData = emiList[index];
          const isPaid = emiData.payment_status == '1';

          if (!isPaid && !uiInfo.dueDate) {
            uiInfo.dueDate = this.typeService.getGlobalDate(emiData.emi_date);
            uiInfo.dueAmount =
              parseFloat(emiData.emi_amount) + (emiData.penalty ?? 0.0);
            uiInfo.dueAmount = Math.floor(uiInfo.dueAmount);
            uiInfo.isDelayed = emiData.payment_due_status == '1';
            uiInfo.overdueDays = emiData.penalty_days ?? 0;
          }
        } catch (error) {}
      }

      return uiInfo;
    } catch (error) {
      return {};
    }
  }

  async getHistories(reqData) {
    try {
      // Params validation
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');

      const purposeInclude: any = { model: loanPurpose };
      purposeInclude.attributes = ['purposeName', 'image_url', 'lsp_image_url'];
      const include = [purposeInclude];
      const attributes = [
        'id',
        'loanStatus',
        'netApprovedAmount',
        'remark',
        'createdAt',
      ];
      const options = {
        include,
        where: { userId },
        order: [['createdAt', 'DESC']],
      };
      const loanList = await this.repository.getTableWhereData(
        attributes,
        options,
      );
      if (loanList == k500Error) return kInternalError;

      const finalizedList = [];
      for (let index = 0; index < loanList.length; index++) {
        try {
          const loan = loanList[index];
          const obj: any = {
            id: loan.id,
            loanStatus: loan.loanStatus,
            netApprovedAmount: Math.floor(
              +(loan?.netApprovedAmount ?? 0),
            ).toString(),
            createdAt: loan.createdAt,
            loanPurpose: loan?.purpose?.purposeName ?? 'Personal Loan',
            image_url: loan?.purpose?.image_url,
            lsp_image_url: loan?.purpose?.lsp_image_url ?? PERSONAL_LOAN_ICON,
          };
          if (loan?.remark) obj.remark = kInternalPolicy;
          if (obj?.loanStatus == 'Complete') obj.loanStatus = 'Completed';
          finalizedList.push(obj);
        } catch (error) {}
      }
      return finalizedList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region when loan Active then call api to change status
  async loanIsActive(loanId) {
    try {
      if (!loanId) return kParamMissing('loanId');
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = ['id', 'status', 'dates'];
      const include = [masterInclude];
      const options = { where: { id: loanId, loanStatus: 'Active' }, include };
      const att = ['id'];
      const find = await this.repository.getRowWhereData(att, options);
      if (!find || find === k500Error) return kInternalError;
      const status = find?.masterData?.status ?? {};
      const dates = find?.masterData?.dates ?? {};
      const id = find?.masterData?.id;
      if (id) {
        if (status.loan != 6) status.loan = 6;
        if (status.disbursement != 1) status.disbursement = 1;
        if (dates.disbursement == 0) dates.disbursement = new Date().getTime();
        await this.masterRepo.updateRowData({ status, dates }, id);
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region check If Any User Not CloseLoan
  async checkIfAnyUserNotCloseLoan(limit = 10) {
    try {
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = ['status', 'id', 'loanId'];
      masterInclude.where = {
        status: {
          [Op.or]: [
            { loan: { [Op.or]: [1, 3] } },
            { loan: 6, disbursement: 0 },
          ],
        },
      };
      const option = {
        where: { loanStatus: 'Active' },
        order: [['id', 'desc']],
        limit: limit ?? 10,
        include: [masterInclude],
      };
      const att = ['id', 'loanStatus'];
      try {
        const result = await this.repository.getTableWhereData(att, option);
        if (result?.length)
          this.slack.sendMsg({
            text: ` checkIfAnyUserNotCloseLoan1 issue`,
            threads: [`Data not close1 -> ${JSON.stringify(result)}`],
          });
        if (!result || result === k500Error) return kInternalError;
        for (let index = 0; index < result.length; index++) {
          try {
            const ele = result[index];
            const status = ele?.masterData?.status;
            const id = ele?.masterData?.id;
            const loanId = ele?.masterData?.loanId;
            if (
              id &&
              loanId == ele.id &&
              ele?.loanStatus === 'Active' &&
              status.loan != 6
            ) {
              status.loan = 6;
              status.disbursement = 1;
              await this.masterRepo.updateRowData({ status }, id);
            }
          } catch (error) {}
        }
      } catch (error) {}

      try {
        masterInclude.where = { status: { loan: 6 } };
        option.where.loanStatus = 'Complete';
        const result = await this.repository.getTableWhereData(att, option);
        if (result?.length)
          this.slack.sendMsg({
            text: ` checkIfAnyUserNotCloseLoan2 issue`,
            threads: [`Data not close2 -> ${JSON.stringify(result)}`],
          });
        if (!result || result === k500Error) return kInternalError;
        for (let index = 0; index < result.length; index++) {
          try {
            const ele = result[index];
            const status = ele?.masterData?.status;
            const id = ele?.masterData?.id;
            const loanId = ele?.masterData?.loanId;
            if (id && loanId == ele.id && status.loan == 6) {
              status.loan = 7;
              await this.masterRepo.updateRowData({ status }, id);
            }
          } catch (error) {}
        }
      } catch (error) {}
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region acceptInsurance
  async funAcceptInsurance(body) {
    try {
      // Params validation
      const loanId = body?.loanId;
      if (!loanId) return kParamMissing('loanId');
      const userId = body.userId;
      if (!userId) return kParamMissing('userId');
      const code = body?.relationshipCode;
      const reg = /^\+?\d{10,12}$/;
      if (!body?.firstName || !body?.lastName || !body?.phone || !code)
        return kParamMissing();
      if (!reg.test(body?.phone)) {
        return k422ErrorMessage(kValidPhone);
      }
      const dob = body?.dob;
      if (!dob) return kParamMissing('dob');
      if (isNaN(new Date(dob)?.getTime())) return kInvalidParamValue('dob');

      let gender = 'M';
      const findCode = valueInsurance.relationshipCode.find(
        (f) => f.code === code,
      );
      if (findCode) gender = findCode?.gender ?? 'M';
      else return k422ErrorMessage(kPleaseSelectValidNomineeRelationshipCode);
      // this condition apply only Spouse
      if (code == '1') {
        const options = { where: { id: userId } };
        const att = ['gender'];
        const find = await this.userRepo.getRowWhereData(att, options);
        if (find && find != k500Error)
          gender = (find?.gender).toUpperCase() === 'MALE' ? 'F' : 'M';
      }
      gender = gender.toUpperCase();

      let num: string = (body?.phone ?? '').toString().replace(/\D/g, '');
      if (num.length > 10) num = num.slice(-10);

      const phoneResponse = await this.automation.verifyContacts({
        numbers: [num],
      });
      if (phoneResponse?.message) return kInternalError;
      if (phoneResponse?.length != 1) return kInternalError;
      if (!phoneResponse[0]?.name) {
        return k422ErrorMessage(
          `${num} is not valid number, Please enter any other valid number`,
        );
      }
      let truecaller_details = '';
      try {
        truecaller_details = JSON.stringify(phoneResponse);
      } catch (error) {}
      const nomineeDetail = {
        Nominee_First_Name: body?.firstName,
        Nominee_Last_Name: body?.lastName,
        Nominee_Contact_Number: num,
        Nominee_Home_Address: null,
        Nominee_gender: gender == 'M' ? 'Male' : 'Female',
        Nominee_Salutation: gender == 'M' ? 'Mr' : 'Ms',
        Nominee_Email: null,
        Nominee_Relationship_Code: code,
        Nominee_dob: dob,
        whatsAppURL: `https://wa.me/${num}`,
        truecaller_details,
      };

      const update = await this.loanRepo.updateRowData(
        { nomineeDetail },
        loanId,
      );
      if ((update ?? '').toString() != '1') return kInternalError;
      const kfsData = await this.getKeyFactStatement(loanId);
      if (kfsData.message) return kfsData;
      return { needUserInfo: true, kfsData };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get Address List
  async getCibilAddressesList(body) {
    try {
      // Params validation
      const loanId = body?.loanId;
      if (!loanId) return kParamMissing('loanId');
      const userId = body.userId;
      if (!userId) return kParamMissing('userId');

      const bankInc: any = { model: BankingEntity };
      bankInc.attributes = ['id', 'accountDetails'];
      const attributes = ['id', 'loanStatus', 'bankingId'];
      const include = [bankInc];
      const options = { where: { id: loanId, userId }, include };
      const loanData = await this.repository.getRowWhereData(
        attributes,
        options,
      );
      if (loanData === k500Error) return kInternalError;
      if (!loanData) return k422ErrorMessage(kNoDataFound);

      if (!loanData?.bankingData?.accountDetails)
        return k422ErrorMessage('Account details not found');
      const accountDetails = JSON.parse(loanData.bankingData.accountDetails);

      const kycAttributes = ['aadhaarAddress'];
      let kycData = await this.kycRepo.getRowWhereData(kycAttributes, {
        where: { userId },
        order: [['id', 'DESC']],
      });

      const rawAadhaarAddress = kycData?.aadhaarAddress;
      if (!rawAadhaarAddress)
        return k422ErrorMessage('Aadhaar address not found');

      const aadhaarAddress =
        this.typeService.getUserAadharAddress(rawAadhaarAddress);

      const scoreAttributes = ['id', 'addresses'];
      const scoreOptions = {
        order: [['id', 'DESC']],
        where: { type: '1', status: '1', userId: userId },
      };
      const scoreData = await this.cibilScoreRepo.getRowWhereData(
        scoreAttributes,
        scoreOptions,
      );
      if (scoreData == k500Error) return kInternalError;
      if (!scoreData) return k422ErrorMessage(kNoDataFound);
      scoreData.addresses.forEach((element) => {
        element.address = `${element?.line1 ? element?.line1 + ', ' : ''}${
          element?.line2 ? element?.line2 + ', ' : ''
        }${element?.line3 ? element?.line3 + ', ' : ''}${
          element?.line4 ? element?.line4 + ', ' : ''
        }${element?.line5 ? element?.line5 : ''}`;
        delete element.line1;
        delete element.line2;
        delete element.line3;
        delete element.line4;
        delete element.line5;
        delete element.pinCode;
        delete element.stateCode;
        delete element.dateReported;
        delete element.addressCategory;
        delete element.enquiryEnriched;
      });
      if (accountDetails?.address)
        scoreData.addresses.unshift({
          index: 'BANK',
          address: accountDetails?.address,
        });
      if (aadhaarAddress)
        scoreData.addresses.unshift({
          index: 'AADHAAR',
          address: aadhaarAddress,
        });
      return scoreData.addresses;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async funGetLoanData(query) {
    try {
      const userId = query?.userId;
      if (!userId) return kParamsMissing;
      const userInclude = {
        model: registeredUsers,
        attributes: ['id', 'fullName', 'email', 'phone', 'hashPhone'],
      };
      const eSignInclude = {
        model: esignEntity,
        attributes: ['id', 'signed_document_upload'],
      };
      const disInclude = {
        model: disbursementEntity,
        attributes: [
          'id',
          'createdAt',
          'amount',
          'updatedAt',
          'status',
          'utr',
          'account_number',
          'bank_name',
        ],
        where: { status: 'processed' },
      };
      const purposeInclude = {
        model: loanPurpose,
        attributes: ['id', 'purposeName'],
      };
      const loanData: any = await this.loanRepo.getRowWhereData(
        [
          'id',
          'createdAt',
          'loan_disbursement_date',
          'esign_id',
          'appType',
          'netApprovedAmount',
        ],
        {
          where: {
            userId: userId,
            loanStatus: 'Active',
          },
          include: [disInclude, userInclude, purposeInclude, eSignInclude],
        },
      );
      if (!loanData && loanData == k500Error) return kInternalError;
      return loanData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funLoanNotDisbursementBanner(query) {
    try {
      if (!query?.userId) return kParamsMissing;
      const userId = query?.userId;
      const loanData: any = await this.funGetLoanData(query);

      if (loanData?.message) return loanData;
      const createdAt = loanData.createdAt;
      const disbursementData = loanData.disbursementData;
      disbursementData.sort((b, a) => a.id - b.id);
      let loanDisDate = disbursementData[0]?.createdAt;
      const utr = disbursementData[0]?.utr;
      loanDisDate = new Date(loanDisDate);
      const dateDifference = this.typeService.dateDifference(
        loanDisDate,
        createdAt,
        'Seconds',
      );
      if (dateDifference == null) return false;
      const mins: any = dateDifference / 60;

      const appType = loanData.appType;
      const userData = [];
      userData.push({ userId, appType });
      const passData: any = {
        userData,
        image: DisbursementBanner,
        isMsgSent: true,
        isManualTitle: true,
      };
      let response;
      if (mins <= MAX_DIS_TIME)
        response = await this.disburementIn30min(
          mins,
          DisbursementSuccessTitleOnTime,
          DisbursementSuccessBodyOnTime,
          true,
          null,
        );
      else
        response = await this.disburementIn30min(
          mins,
          DisbursementSuccessTitle,
          DisbursementSuccessBody,
          false,
          utr,
        );
      if (response.message) return kInternalError;
      passData.title = response.title;
      passData.content = response.content;
      const smsData: any = await this.disburesmentSmsData(loanData);

      if (smsData.message) return kInternalError;
      passData.id = smsData.id;
      delete smsData.id;
      passData.smsOptions = smsData;
      await this.sharedNotificationService.sendNotificationToUser(passData);
      await this.sendDisbursementMailToUser(loanData);

      // check whatsapp number register
      let number = this.cryptService.decryptPhone(
        loanData?.registeredUsers?.phone,
      );

      const hashPhone = loanData?.registeredUsers?.hashPhone;
      const hashPhones = [hashPhone];
      const nonWhatsAppHashPhone =
        await this.whatsAppService.getNonWhatsAppUsers(hashPhones);

      if (nonWhatsAppHashPhone.includes(hashPhone)) return;
      await this.preDataAndSendDisbursementWhatsappMsg(loanData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async disburementIn30min(mins, title, content, isOnTime = false, utr) {
    try {
      if (isOnTime) {
        const completedMins = Math.floor(mins);
        const remaingSecs: any = (mins - completedMins) * 60;
        content = content
          .replace('##MIN##', mins)
          .replace('##SEC##', remaingSecs);
      } else content = content.replace('##UTR##', utr);
      return { title, content };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async disburesmentSmsData(loanData) {
    try {
      const appType = loanData?.appType;
      const templateId =
        appType == 1
          ? kMsg91Templates.DISBUREMENT
          : kLspMsg91Templates.DISBUREMENT;

      // get data from redis
      let key = `TEMPLATE_DATA_BY_TEMPID`;
      let templeteData = await this.redisService.get(key);
      templeteData = templeteData ?? null;
      templeteData = JSON.parse(templeteData) ?? [];
      let id;
      //filter data from template id
      if (templeteData.length) {
        templeteData = templeteData.filter(
          (el) => el.templateId == templateId || el.lspTemplateId == templateId,
        );
        if (templeteData.length > 0) {
          id = templeteData[0].id;
        }
      }
      const userData = loanData.registeredUsers;
      const disData = loanData.disbursementData[0];
      const readableDate = disData?.updatedAt
        ? this.dateService.readableDate(disData.updatedAt, true)
        : '';
      return {
        id,
        name: userData.fullName,
        amount: disData.amount / 100,
        transaction: disData.utr,
        time: readableDate,
        appType,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async sendDisbursementMailToUser(loanData) {
    try {
      const appType = loanData?.appType;
      const templatePath = await this.commonSharedService.getEmailTemplatePath(
        kloanApprovalTemplate,
        appType,
        null,
        null,
      );
      let htmlData = (await fs.readFileSync(templatePath)).toString();
      const purposeData = loanData.purpose;
      const userData = loanData.registeredUsers;
      const esingData = loanData?.eSignData;
      const signed_document_upload = esingData?.signed_document_upload;
      let ledgerUrl: any = '';
      try {
        ledgerUrl = await this.tallyService.getLedgerLoanDetails({
          loanId: loanData.id,
          pdfDownload: true,
        });
      } catch (error) {}
      const disData = loanData.disbursementData[0];
      htmlData = htmlData.replace('##NAME##', userData.fullName);
      htmlData = htmlData.replace('##NBFC##', EnvConfig.nbfc.nbfcName);
      htmlData = htmlData.replace('##NBFCINFO##', nbfcInfoStr);
      htmlData = htmlData.replace(
        '##NBFCCAMEL##',
        EnvConfig.nbfc.nbfcCamelCaseName,
      );
      htmlData = htmlData.replace(
        '##PURPOSE##',
        purposeData?.purposeName ?? '',
      );
      htmlData = htmlData.replace('##AccountNO##', disData.account_number);
      htmlData = htmlData.replace('##BANKNAME##', disData.bank_name);
      htmlData = htmlData.replace(
        '##FINALAMOUNT##',
        loanData.netApprovedAmount,
      );
      htmlData = htmlData.replace(
        '##FINALAMOUNT1##',
        (disData.amount / 100).toFixed().toString(),
      );
      htmlData = htmlData.replace('##BANKTRANSACTION##', disData.utr);
      htmlData = htmlData.replace('##LOANID##', loanData.id);
      htmlData = htmlData.replace('##NBFCINFO##', nbfcInfoStr);
      htmlData = this.replaceAll(
        htmlData,
        '##NBFCSHORTNAME##',
        EnvConfig.nbfc.nbfcCamelCaseName,
      );
      htmlData = htmlData.replace(
        '##APPNAME##',
        EnvConfig.nbfc.appCamelCaseName,
      );
      const attachments: any = [
        {
          filename: 'Loan-Agreement.pdf',
          path: signed_document_upload,
          contentType: 'application/pdf',
        },
      ];
      if (ledgerUrl && ledgerUrl !== k500Error && !ledgerUrl?.message)
        attachments.push({
          filename: 'Loan-Statement.pdf',
          path: ledgerUrl,
          contentType: 'application/pdf',
        });
      let replyTo = kSupportMail;
      let fromMail = kNoReplyMail;
      if (appType == '0') {
        fromMail = kLspNoReplyMail;
        replyTo = KLspSupportMail;
      }
      await this.sharedNotificationService.sendMailFromSendinBlue(
        userData.email,
        'Loan Approval',
        htmlData,
        userData.id,
        [],
        attachments,
        fromMail,
        replyTo,
      );
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //get loan details only
  async getLoanDetails(query) {
    try {
      const loanId = query?.loanId;
      const appType = query?.appType;
      if (!loanId) return kParamMissing('loanId');

      // Disbursement data
      const disbInc: any = { model: disbursementEntity };
      disbInc.attributes = ['amount', 'utr', 'account_number', 'bank_name'];
      // eSign doc
      const eSignInc: any = { model: esignEntity };
      eSignInc.attributes = ['signed_document_upload'];
      // emi data
      const emiInc: any = { model: EmiEntity };
      emiInc.attributes = [
        'interestCalculate',
        'waiver',
        'paid_waiver',
        'unpaid_waiver',
      ];

      // Purpose
      const purposeInc: any = { model: loanPurpose };
      purposeInc.attributes = ['purposeName', 'lsp_image_url'];
      const include = [emiInc, disbInc, eSignInc, purposeInc];
      const attributes = [
        'id',
        'loanFees',
        'stampFees',
        'interestRate',
        'loan_disbursement_date',
        'netApprovedAmount',
        'approvedDuration',
        'loanStatus',
        'nocURL',
        'insuranceId',
        'insuranceDetails',
        'charges',
        'isLoanClosure',
      ];
      const options = { where: { id: loanId }, include };
      const loanData = await this.repository.getRowWhereData(
        attributes,
        options,
      );
      if (loanData === k500Error) return kInternalError;
      if (!loanData) return k422ErrorMessage(kNoDataFound);
      const emiData = loanData?.emiData;
      const disData = loanData?.disbursementData[0];
      const eSign = loanData?.eSignData;
      const purposeData = loanData?.purpose;
      const insurance = loanData?.insuranceDetails ?? {};
      const insuranceId = loanData?.insuranceId;
      const charges = loanData?.charges ?? {};
      const convenienceFee = charges?.insurance_fee;
      const riskAssessmentCharge = charges?.risk_assessment_charge;
      const docCharge = charges?.doc_charge_amt ?? 0;
      const gstPr = charges?.gst_per;
      const stampFees = loanData?.stampFees;
      const approvedAmount = (+(loanData?.netApprovedAmount ?? 0)).toFixed();
      const isLoanClosure = loanData?.isLoanClosure ?? false;

      const loanFees = Math.round(
        +(loanData?.loanFees ?? 0) -
          +(charges?.gst_amt ?? 0) -
          +docCharge -
          +(convenienceFee ?? 0) -
          +(riskAssessmentCharge ?? 0),
      );

      let totalCharges: any = convenienceFee + Math.round(docCharge) + loanFees;
      totalCharges += Math.round(totalCharges * (gstPr / 100));
      totalCharges += Math.round(insurance?.totalPremium ?? 0);
      const interestTag = `@${(+loanData?.interestRate).toFixed(
        2,
      )}% per day for ${loanData?.approvedDuration ?? 0} days`;
      const gstTag = `Processing Charge${
        convenienceFee ? ' + Online Convenience Charge' : ''
      }${docCharge ? ' + Documentation charge' : ''}${
        riskAssessmentCharge ? ' + Risk Assessment Charge' : ''
      }`;

      let totalInterest = 0;
      let isSettled = false;
      let settlementAmount = 0;
      emiData.forEach((ele) => {
        try {
          totalInterest += +(ele?.interestCalculate ?? 0);
          const total = +ele?.waiver + +ele?.paid_waiver + +ele?.unpaid_waiver;
          if (total > 0) isSettled = true;
          settlementAmount += total;
        } catch (error) {}
      });

      //color code
      const skyColor = '1BC8C9';
      const redColor = 'FF4E60';
      const loan = {};
      const loanCharges = {};
      const loanDocuments = {};
      const lspDisbursDetails = {};
      // Fun to add values to the object conditionally.
      function addCharge(key, value, includeCharge = true, lsp = false) {
        const checkFormattedValue = value ? `##${value}##` : `##-##`;

        loan[key] = checkFormattedValue;

        if (lsp) lspDisbursDetails[key] = checkFormattedValue;

        if (includeCharge) loanCharges[key] = checkFormattedValue;
      }
      addCharge('Loan Id', loanData.id, false);
      addCharge(
        'Approved amount',
        this.strService.readableAmount(approvedAmount),
        false,
        true,
      );

      if (appType == 0) {
        lspDisbursDetails['Total charges'] = `${this.strService.readableAmount(
          totalCharges,
        )}\n#*View breakdown*#`;
      }

      const formattedProcessingFee = this.strService.readableAmount(loanFees);
      addCharge('Processing Charge', formattedProcessingFee);

      if (convenienceFee) {
        const formattedOnlineConvenienceFee = this.strService.readableAmount(
          Math.round(convenienceFee),
        );
        addCharge('Online Convenience Charge', formattedOnlineConvenienceFee);
      }

      if (docCharge) {
        const formattedDocumentationCharges = this.strService.readableAmount(
          Math.round(docCharge),
        );
        addCharge('Documentation Charge', formattedDocumentationCharges);
      }

      if (riskAssessmentCharge)
        addCharge(
          'Risk Assessment Charge',
          this.strService.readableAmount(Math.round(riskAssessmentCharge)),
          true,
        );

      if (charges?.gst_amt) {
        let gstKey = 'GST @18%';
        gstKey += `\n#*||${skyColor}||${gstTag}*#`;
        const formattedGSTAmount = this.strService.readableAmount(
          charges?.gst_amt,
        );
        addCharge(gstKey, formattedGSTAmount);
      }

      if (insuranceId) {
        const formattedInsurancePremiumAmount = insurance?.totalPremium
          ? this.strService.readableAmount(
              Math.round(insurance?.totalPremium ?? 0),
            )
          : '';
        addCharge('Insurance premium amount', formattedInsurancePremiumAmount);
      }

      if (stampFees)
        addCharge(
          'Stamp duty charge',
          this.strService.readableAmount(Math.round(stampFees)),
          false,
        );

      addCharge(
        'Disbursement loan amount',
        this.strService.readableAmount(Math.round(disData?.amount ?? 0) / 100),
        false,
        true,
      );

      const interestKey = `Total interest amount\n#*||${skyColor}||${interestTag}*#`;
      addCharge(
        interestKey,
        this.strService.readableAmount(Math.round(totalInterest)),
        false,
        true,
      );

      const disbursementDate = new Date(
        loanData?.loan_disbursement_date,
      ).toLocaleString('default', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      });

      addCharge('Disbursement date', disbursementDate, false);

      addCharge('Bank name', disData?.bank_name, false);
      addCharge('Account number', disData?.account_number, false);
      addCharge('UTR', disData?.utr, false);

      if (appType == 0) {
        loan['Loan purpose'] = purposeData?.purposeName ?? '-';
        loan['Image url'] = purposeData?.lsp_image_url;
        loan['Total charges'] = this.strService.readableAmount(totalCharges);
      }
      if (isSettled)
        loan['Settlement amount'] =
          `#*||${redColor}||##` +
          this.strService.readableAmount(Math.round(settlementAmount)) +
          '##*#';

      loanDocuments['Loan agreement'] = eSign?.signed_document_upload;
      if (loanData?.nocURL) loanDocuments['NOC'] = loanData?.nocURL;
      if (insuranceId) {
        const insuranceData = await this.getInsuranceData(loanData?.id);
        if (insuranceData?.insurancePolicy1)
          loanDocuments[healthPolicy] = insuranceData?.insurancePolicy1;
        if (insuranceData?.insurancePolicy2)
          loanDocuments[lojPolicy] = insuranceData?.insurancePolicy2;
      }
      let loanStatus =
        loanData?.loanStatus == 'Complete' && isSettled && !isLoanClosure
          ? 'Settled'
          : loanData?.loanStatus;

      if (loanStatus == 'Complete') loanStatus = 'Completed';

      return {
        'Loan status': loanStatus,
        loan,
        loanCharges,
        loanDocuments,
        lspDisbursDetails,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // get user loan Repayment transactions
  async getLoanRepaymentDetails(query) {
    try {
      const loanId = query?.loanId;
      if (!loanId) return kParamMissing('loanId');

      //Transaction data
      const attributes = [
        'id',
        'emiId',
        'paidAmount',
        'completionDate',
        'type',
        'source',
        'subSource',
        'transactionId',
        'penaltyAmount',
        'response',
        'sgstOnPenalCharge',
        'cgstOnPenalCharge',
        'bounceCharge',
      ];
      const status = kCompleted;
      const subStatus = {
        [Op.or]: [
          {
            [Op.ne]: 'REVERSE_SETTLEMENT',
          },
          { [Op.eq]: null },
        ],
      };
      const options = {
        where: { loanId, status, subStatus },
        order: [['id', 'DESC']],
      };
      const traData = await this.transactionRepo.getTableWhereData(
        attributes,
        options,
      );

      if (traData === k500Error) return kInternalError;
      if (!traData) return k422ErrorMessage(kNoDataFound);
      //EMI data
      const emiAttr = [
        'id',
        'emi_date',
        'payment_due_status',
        'penalty_update_date',
        'pay_type',
        'penalty_days',
      ];
      const emiOps = { where: { loanId }, order: [['id']] };
      const emiData = await this.emiRepo.getTableWhereData(emiAttr, emiOps);

      if (emiData === k500Error) return kInternalError;

      const traLength = traData.length;
      const emiLength = emiData.length;
      const transactions = [];
      let isDelayed = false;
      for (let index = 0; index < traLength; index++) {
        try {
          const tra = traData[index];
          if (
            tra?.source === KICICIUPI ||
            tra?.source === KICICIUPI2 ||
            tra?.source === KYESUPI
          )
            tra.source = 'UPI';
          const payDate = this.typeService.getGlobalDate(tra?.completionDate);
          const type = tra?.type;
          const penaltyAmount = this.typeService.manageAmount(
            tra?.penalCharge ??
              0 + tra?.sgstOnPenalCharge ??
              0 + tra?.cgstOnPenalCharge ??
              0,
            disburseAmt,
          );
          const paidAmount = this.typeService.manageAmount(
            (type == 'REFUND'
              ? Math.abs(tra?.paidAmount ?? 0)
              : tra?.paidAmount ?? 0) - penaltyAmount,
          );
          const response = tra?.response ? JSON.parse(tra.response) : {};
          const method =
            response?.method ??
            (response?.items ? response?.items[0]?.method : tra?.subSource) ??
            '-';
          const mode =
            method == 'emandate' || method == 'AUTODEBIT' ? 'e-NACH' : method;

          let emiNum;
          let payType;
          let delay;
          for (let i = 0; i < emiLength; i++) {
            try {
              const emi = emiData[i];
              const emiDate = this.typeService.getGlobalDate(emi.emi_date);
              if (tra?.emiId == emi.id || type == 'FULLPAY') {
                if (
                  emiDate.getTime() > payDate.getTime() &&
                  emi.payment_due_status == '0'
                )
                  payType = 'Pre Paid';
                else if (
                  emiDate.getTime() == payDate.getTime() &&
                  emi.payment_due_status == '0'
                )
                  payType = 'Ontime';
                else if (
                  emiDate.getTime() < payDate.getTime() &&
                  emi.payment_due_status == '1'
                )
                  payType = 'Delayed';
                else if (emi.payment_due_status == '1') payType = 'Delayed';
                else if (emi.payment_due_status == '0') payType = 'Ontime';
              }
              if (emi.payment_due_status == '1' && !isDelayed) isDelayed = true;
              if (tra?.emiId == emi.id) {
                emiNum = `${i + 1}`;
                delay = emi?.penalty_days ?? 0;
              } else if (type == 'FULLPAY' && emi?.pay_type == 'FULLPAY') {
                if (!emiNum) emiNum = `${i + 1}`;
                else emiNum += ` & ${i + 1}`;
              }
            } catch (error) {}
          }

          //To display first and last emi if forecloseLoan
          if (emiNum?.includes('&')) {
            const emiParts = emiNum.split('&').map((e) => e.trim());
            if (emiParts.length > 2) {
              emiNum = `${emiParts[0]} to ${emiParts[emiParts.length - 1]}`;
            }
          }

          if (type == 'FULLPAY')
            delay = this.typeService.getOverDueDay(emiData);
          if (type == 'REFUND') payType = 'Paid';

          //#*||FF4E60||value*# for red color
          //##value## for bold
          const redColor = 'FF4E60';
          let payStatus = '';
          let payTextStatus = '';
          if (payType == 'Ontime' || payType == 'Pre Paid') {
            payStatus = `#*||3EC13B||##${payType}##*#`;
            payTextStatus = `#*||3EC13B||##${payType}##*#`;
          } else if (payType == 'Delayed') {
            payStatus = `#*||FF9C41||##${payType}##*#`;
            payTextStatus = `#*||${redColor}||##${payType}##*#`;
          } else if (payType == 'Paid') {
            payStatus = `#*||2DD2D3||##${payType}##*#`;
            payTextStatus = `#*||3EC13B||##${payType}##*#`;
          } else payStatus = `##${payType ?? '-'}##`;

          const payment: any = {};
          const paymentDetail: any = {};
          payment['EMI'] = '##' + emiNum + '##';
          paymentDetail['Status'] = payTextStatus;
          const date = this.typeService.getDateFormatted(payDate, '/');
          payment['Date'] = '##' + date + '##';
          paymentDetail['Date'] = '##' + date + '##';
          // paid amount
          payment['Amount'] = `##${this.strService.readableAmount(
            type == 'REFUND'
              ? Math.abs((tra?.paidAmount).toFixed(2) ?? 0)
              : tra?.paidAmount.toFixed(2) ?? 0,
          )}##`;
          paymentDetail['Amount'] = `##${this.strService.readableAmount(
            paidAmount,
          )}##`;

          // delay day
          if (delay && penaltyAmount) {
            if (delay == 1) delay = 1 + ' day';
            else delay = delay + ' days';
            paymentDetail['Delayed by'] = `#*||${redColor}||##${delay}##*#`;
          }

          if (penaltyAmount) {
            /// penalty
            paymentDetail['Penalty Amount'] =
              `#*||${redColor}||##` +
              this.strService.readableAmount(penaltyAmount) +
              '##*#';
            /// total paid amount
            const key = `Total Paid Amount\n#*||${redColor}||Inclusive of penalty*#`;
            paymentDetail[key] = `##${this.strService.readableAmount(
              paidAmount + penaltyAmount,
            )}##`;
          }

          paymentDetail['Transaction ID'] = `##${tra?.transactionId ?? '-'}##`;
          payment['Type'] = `##${type ?? '-'}##`;
          payment['Status'] = payStatus;
          paymentDetail['Payment via'] = `##${tra?.source ?? '-'}##`;
          paymentDetail['Payment mode'] = `##${mode.toUpperCase()}##`;
          payment.paymentDetail = paymentDetail;
          transactions.push(payment);
        } catch (error) {}
      }
      if (traLength == 0) {
        const find = emiData.find((f) => f.payment_due_status == '1');
        if (find) isDelayed = true;
      }

      return { title: 'Payment details', isDelayed, payment: transactions };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // get user loan emi and repayment data
  async getLoanEmiRepaymentDetails(query) {
    try {
      const loanId = query?.loanId;
      if (!loanId) return kParamMissing('loanId');

      // Transaction data
      const transInc: any = { model: TransactionEntity };
      transInc.where = { status: { [Op.or]: ['INITIALIZED', 'COMPLETED'] } };
      transInc.required = false;
      transInc.attributes = [
        'id',
        'utr',
        'type',
        'emiId',
        'status',
        'adminId',
        'source',
        'subSource',
        'createdAt',
        'paidAmount',
        'completionDate',
        'subscriptionDate',
      ];
      // Purpose
      const purposeInc: any = { model: loanPurpose };
      purposeInc.attributes = ['purposeName', 'image_url'];
      // EMI data
      const emiInc: any = { model: EmiEntity };
      emiInc.attributes = [
        'id',
        'penalty',
        'emi_date',
        'pay_type',
        'emi_amount',
        'totalPenalty',
        'penalty_days',
        'bounceCharge',
        'payment_status',
        'fullPayPenalty',
        'principalCovered',
        'interestCalculate',
        'payment_due_status',
        'payment_done_date',
        'partPaymentPenaltyAmount',
        'emiNumber',
      ];
      // Banking
      const bankInc: any = { model: BankingEntity };
      bankInc.attributes = ['bank', 'accountNumber'];
      const attributes = [
        'id',
        'loanStatus',
        'insuranceId',
        'isPartPayment',
        'interestRate',
        'userId',
        'netApprovedAmount',
      ];
      const include = [emiInc, transInc, purposeInc, bankInc];
      const options = { where: { id: loanId }, include };
      const loanData = await this.repository.getRowWhereData(
        attributes,
        options,
      );
      if (loanData === k500Error) return kInternalError;
      if (!loanData) return k422ErrorMessage(kNoDataFound);
      return await this.getEMIandRepayDetail(loanData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //get emi data and repayment data
  private async getEMIandRepayDetail(loanData) {
    try {
      const loanId = loanData.id;
      const userId = loanData?.userId;
      const bank = loanData.bankingData;
      const emiData = loanData?.emiData;
      emiData.sort((a, b) => a.id - b.id);
      const transData = loanData?.transactionData ?? [];
      transData.sort((a, b) => b.id - a.id);
      const emiDates = emiData.map((emi) => {
        if (emi?.payment_status === '0') return emi.emi_date;
      });
      const traObj = { transData, emiData, emiDates };
      const traDetails = this.transactionAutodebitEMIInfo(traObj);
      const totalRepaidAmt = this.typeService.manageAmount(
        traDetails?.totalRepaidAmount ?? 0,
        disburseAmt,
      );
      const autoPayDetail = traDetails?.autoPayDetail ?? [];
      const data = { emiData, autoPayDetail, transData };
      const emiDetails = this.getEMIandAutoPayDetail(data);
      const emiList = emiDetails?.emiDetails;
      const totalPayableAmt = this.typeService.manageAmount(
        emiDetails?.totalPayableAmount,
      );
      const totalDueAmt = this.typeService.manageAmount(
        emiDetails?.totalDueAmount,
      );
      const penaltyDays = emiDetails?.penaltyDays;
      const isDelayed = emiDetails?.isDelayed;
      const fullPayData = await this.sharedCalculation.getFullPaymentData({
        loanId,
      });
      if (fullPayData?.message) return fullPayData;
      const fullPayAmount = +fullPayData?.totalAmount;
      let paymentSource = await this.commonSharedService.getServiceName(
        kPaymentMode,
      );
      if (!paymentSource) paymentSource = GlobalServices.PAYMENT_MODE;
      const approvedAmount = Math.floor(loanData?.netApprovedAmount ?? 0);
      const finalData: any = {};
      finalData['Loan ID'] = loanData.id;
      finalData['Loan Purpose'] = loanData.purpose?.purposeName ?? '-';
      finalData['Purpose Image'] = loanData.purpose?.image_url;
      finalData['Loan Status'] = loanData.loanStatus;
      finalData['isDelayed'] = isDelayed;
      finalData['Bank'] = bank.bank;
      finalData['Account number'] = bank.accountNumber;
      finalData['isUpiServiceAvailable'] = UPI_SERVICE;
      finalData['Total payable amount'] =
        this.strService.readableAmount(totalPayableAmt);
      finalData['Total repaid amount'] =
        this.strService.readableAmount(totalRepaidAmt);
      finalData['Total due amount'] =
        this.strService.readableAmount(totalDueAmt);
      finalData['Full Pay Amount'] =
        this.strService.readableAmount(fullPayAmount);
      finalData['Loan amount'] = this.strService.readableAmount(approvedAmount);
      finalData['fullPayAmount'] = fullPayAmount;

      // payment status in percentage
      const duePr = Math.round(100 - (totalRepaidAmt * 100) / totalPayableAmt);
      const repaidPr = Math.round((totalRepaidAmt * 100) / totalPayableAmt);
      const progressPr =
        Math.round((totalRepaidAmt * 100) / totalPayableAmt) / 100 >= 1.0
          ? 1
          : Math.round((totalRepaidAmt * 100) / totalPayableAmt) / 100;
      finalData.Due = duePr;
      finalData.Repaid = repaidPr;
      finalData.Progress = progressPr;
      finalData['emiData'] = emiList; // EMI data
      finalData['paymentOptions'] = [
        {
          name: 'Card / Netbanking',
          source: paymentSource,
          subSource: 'APP',
          androidStatus: true,
          iosStatus: true,
          serviceMode: 'WEB',
          linkSource: 'INAPP',
        },
      ];

      /// Get part min payment
      const TodayDate = new Date().toJSON();
      const range = this.typeService.getUTCDateRange(TodayDate, TodayDate);
      const dateRange = { [Op.gte]: range.fromDate, [Op.lte]: range.endDate };
      const partOption = {
        where: {
          loanId,
          status: 'INITIALIZED',
          type: 'PARTPAY',
          adminId: { [Op.ne]: SYSTEM_ADMIN_ID },
          createdAt: dateRange,
        },
        order: [['id', 'DESC']],
      };
      const partPayData = await this.transactionRepo.getRowWhereData(
        ['id', 'paidAmount', 'createdAt'],
        partOption,
      );
      if (partPayData === k500Error) return kInternalError;
      let partPaidAmount = 0;
      /// Check Already paid after create link
      if (partPayData?.id) {
        const partPaidOption = {
          where: {
            loanId,
            status: kCompleted,
            id: { [Op.gt]: partPayData?.id },
            createdAt: dateRange,
          },
          order: [['id', 'DESC']],
        };
        const partPaidData = await this.transactionRepo.getRowWhereData(
          ['id', 'paidAmount', 'createdAt'],
          partPaidOption,
        );
        if (partPaidData === k500Error) return kInternalError;
        partPaidAmount = partPaidData?.paidAmount ?? 0;
      }

      /// Check for part payment enable/disable
      let isPartPayment = false;
      let minPartAmount = 0;
      finalData.collectionPhone = kCollectionPhone;
      if (penaltyDays > 90 && loanData.isPartPayment !== 0) {
        isPartPayment = true;
        if (partPaidAmount > 0) partPayData.paidAmount = 1000;
        minPartAmount = partPayData?.paidAmount ?? 1000;
        if (fullPayAmount < 1000) minPartAmount = fullPayAmount;
      }
      finalData.isPartPayment = isPartPayment;
      finalData.minPartAmount = this.strService.readableAmount(minPartAmount);

      // Fullpay button is enable or not
      finalData.isFullPayment = true;
      const interestRate = +(
        loanData?.interestRate ?? GLOBAL_RANGES.MAX_PER_DAY_INTEREST_RATE
      );
      if (interestRate <= MINIMUM_INTEREST_FOR_FULL_PAY) {
        const totalUnpaidEMIs = emiData.filter(
          (el) => el.payment_status != '1',
        );
        if (totalUnpaidEMIs.length == 1) finalData.isFullPayment = false;
      }

      //insurance
      const insuranceId = loanData?.insuranceId;
      if (insuranceId) {
        const insurance = await this.getInsuranceData(loanData?.id);
        if (!insurance?.insurancePolicy1 && !insurance?.insurancePolicy2)
          finalData.insuranceLabel = kInsuranceWaitingTag;
      }
      const promoCodes: any = await this.promoCodeService.getUsersPromoCode(
        { userId },
        [],
      );
      if (promoCodes?.message) finalData.promoCodes = [];
      else finalData.promoCodes = promoCodes;
      //for showing web page for repayment
      let isWebViewPaymentUrl = false;
      const curDate = this.typeService.getGlobalDate(new Date()).toJSON();
      const transOptions = {
        where: {
          source: {
            [Op.or]: [KICICIUPI, KICICIUPI2, KYESUPI],
          },
          userId,
          status: 'FAILED',
          completionDate: curDate,
        },
      };
      const upiFailCount = await this.transactionRepo.getCountsWhere(
        transOptions,
      );
      if (
        upiFailCount != k500Error &&
        finalData?.promoCodes?.length === 0 &&
        !isPartPayment &&
        upiFailCount < 5
      )
        isWebViewPaymentUrl = true;

      finalData.isWebViewPaymentUrl = isWebViewPaymentUrl;
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //get emi data and payment detail
  private getEMIandAutoPayDetail(data) {
    try {
      const emiData = data?.emiData ?? [];
      const traData = data?.transData ?? [];
      const autoPayData = data?.autoPayDetail ?? [];
      let totalPayableAmount = 0;
      let totalDueAmount = 0;
      let penaltyDays;
      let isDelayed = false;
      const emiDetails = [];
      let needPay = false;
      emiData.sort((a, b) => a.id - b.id);
      emiData.forEach((ele) => {
        try {
          const emiId = ele.id;
          const payStatus = ele?.payment_status;
          const dueStatus = ele?.payment_due_status;
          let totalEMIAmount = 0;
          totalEMIAmount += +(ele?.emi_amount ?? 0);
          totalEMIAmount += ele?.penalty ?? 0;
          if (payStatus == '0') totalDueAmount += totalEMIAmount;
          // Outstanding Amount
          totalPayableAmount += ele?.principalCovered ?? 0;
          totalPayableAmount += ele?.interestCalculate ?? 0;
          totalPayableAmount += ele?.penalty ?? 0;
          totalPayableAmount += ele?.partPaymentPenaltyAmount ?? 0;
          const delayDays = ele?.penalty_days;
          if (payStatus == '0' && dueStatus == '1' && !penaltyDays)
            penaltyDays = delayDays;
          totalEMIAmount = this.typeService.manageAmount(totalEMIAmount);
          const emiAmount = this.typeService.manageAmount(+ele.emi_amount);

          //EMI Pay Button
          let payButton = false;
          const emi_date = new Date(ele.emi_date).getTime();
          const today = this.typeService.getGlobalDate(new Date()).getTime();
          const emi_amount = this.strService.readableAmount(
            (+ele.emi_amount).toFixed(),
          );
          const bounceCharge = ele?.bounceCharge ?? 0;
          let withoutECS = +ele?.penalty - bounceCharge;
          if (withoutECS < 0) withoutECS = 0;
          const penalty = withoutECS
            ? this.strService.readableAmount(withoutECS)
            : '';

          let pay_emi_info;
          /// this prepare othe info
          if (emi_date <= today && needPay == true && payStatus == '0') {
            payButton = true;
            pay_emi_info = this.getPayButtonInfo(
              ele,
              emi_amount,
              penalty,
              totalDueAmount,
              emiData,
            );
          }

          /// first unpaid emi pay button enable
          if (needPay == false && payStatus == '0') {
            payButton = true;
            needPay = true;
            /// get pay emi info
            pay_emi_info = this.getPayButtonInfo(
              ele,
              emi_amount,
              penalty,
              totalDueAmount,
              null,
            );
          }
          if (pay_emi_info) pay_emi_info['id'] = ele.id;
          // EMI data
          let emi: any = {};
          let paymentDetails = {};
          emi['id'] = emiId;
          emi['EMI date'] = ele.emi_date;
          emi['EMI done date'] = ele?.payment_done_date;
          emi['EMI amount'] = this.strService.readableAmount(emiAmount);

          if (withoutECS < 0) withoutECS = 0;
          else withoutECS = this.typeService.manageAmount(+(ele?.penalty ?? 0));
          if (withoutECS)
            emi['Penalty'] = this.strService.readableAmount(withoutECS);
          if (ele?.bounceCharge) emi['EMI amount'] = emi_amount;
          if (penalty) emi['Penalty'] = penalty;
          if (bounceCharge)
            emi['ECS bounce charge'] =
              this.strService.readableAmount(bounceCharge);

          emi['Total EMI Amount'] =
            this.strService.readableAmount(totalEMIAmount);
          emi['totalEMIAmount'] = totalEMIAmount;
          emi['Delayed'] = dueStatus == '1' ? true : false;
          if (dueStatus == '1' || delayDays) {
            emi['Delay Days'] = delayDays;
            if (!isDelayed) isDelayed = true;
          }
          emi['Paid'] = payStatus == '1' ? true : false;
          emi['payButton'] = payButton;
          const autoDebit = autoPayData.find((a) => a?.emiId == emiId);
          emi['info'] = autoDebit ? autoDebit : {};
          if (payStatus == '1') {
            let trans: any = {};
            if (ele?.pay_type == 'FULLPAY')
              trans = traData.find(
                (f) => f.type == 'FULLPAY' && f.status == kCompleted,
              );
            else
              trans = traData.find(
                (f) =>
                  f.emiId == emiId &&
                  f.type != 'REFUND' &&
                  f.status == kCompleted,
              );
            paymentDetails['Status'] = `#*||FF4E60||##${
              dueStatus == '1' ? 'Delayed' : 'Ontime'
            }##*#`;
            paymentDetails['Date'] = `##${
              trans?.completionDate
                ? this.typeService.getDateFormatted(trans?.completionDate, '/')
                : '-'
            }##`;
            paymentDetails['Amount'] = `##${this.strService.readableAmount(
              trans?.paidAmount ?? 0,
            )}##`;
            if (delayDays) {
              paymentDetails[
                'Delayed by'
              ] = `#*||FF4E60||##${delayDays} days##*#`;
              paymentDetails[
                'Penalty Amount'
              ] = `#*||FF4E60||##${this.strService.readableAmount(
                ele?.totalPenalty ?? 0,
              )}##*#`;
            }
            const tpaidAmt =
              (ele?.principalCovered ?? 0) +
              (ele?.interestCalculate ?? 0) +
              (ele?.partPaymentPenaltyAmount ?? 0) +
              (ele?.fullPayPenalty ?? 0);

            let key = `Total Paid Amount`;
            if (dueStatus == '1')
              key = `${key}\n#*||FF4E60||Inclusive of penalty*#`;

            paymentDetails[key] = `##${this.strService.readableAmount(
              tpaidAmt ?? 0,
            )}##`;

            if (trans) {
              paymentDetails['Transaction ID'] = `##${trans?.utr ?? '-'}##`;
              if (
                trans?.source === KICICIUPI ||
                trans?.source === KICICIUPI2 ||
                trans?.source === KYESUPI
              )
                trans.source = 'UPI';
              paymentDetails['Payment via'] = `##${trans?.source ?? '-'}##`;
              if (trans?.subSource == 'AUTODEBIT') trans.subSource = 'e-NACH';
              paymentDetails['Payment mode'] = `##${trans?.subSource ?? '-'}##`;
            }
          }
          emi['pay_emi_info'] = pay_emi_info;
          emi.paymentDetails = paymentDetails;
          emiDetails.push(emi);
        } catch (error) {}
      });

      return {
        emiDetails,
        totalPayableAmount,
        totalDueAmount,
        penaltyDays,
        isDelayed,
      };
    } catch (error) {
      return {};
    }
  }

  //get transaction data and autoPayDetail
  private transactionAutodebitEMIInfo(data) {
    try {
      const emiData = data?.emiData;
      const emiDates = emiData.map((emi) => {
        if (emi?.payment_status === '0') return emi.emi_date;
      });
      const transData = data?.transData;
      let totalRepaidAmount = 0;
      const autoPayDetail = [];
      const transLength = transData.length;
      for (let index = 0; index < transLength; index++) {
        try {
          const tra = transData[index];
          const status = tra.status;
          const paidAmount = tra.paidAmount;
          const subSource = tra?.subSource;
          const subsDate = tra?.subscriptionDate;
          const compDate = tra?.completionDate;
          const emiId = tra?.emiId;
          const type = tra?.type;

          //autoDebit data
          if (
            subSource == kAutoDebit &&
            emiDates.includes(subsDate) &&
            tra?.adminId == SYSTEM_ADMIN_ID &&
            type == 'EMIPAY'
          ) {
            try {
              const stepperInfo = [];
              let isPengingAutodebit = false;
              stepperInfo.push({
                name: 'Repayment requested to your bank',
                date: new Date(tra?.createdAt).toLocaleString('default', {
                  day: 'numeric',
                  month: 'short',
                  year: 'numeric',
                }),
              });
              if (status == kInitiated) {
                isPengingAutodebit = true;
                stepperInfo.push({
                  name: 'Awaiting response from bank',
                  isPending: status == kInitiated ? true : false,
                });
              } else
                stepperInfo.push({
                  name: 'Repayment Status',
                  date: new Date(compDate).toLocaleString('default', {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric',
                  }),
                  status: status == kCompleted,
                });
              const emiDate = this.typeService.getGlobalDate(subsDate);
              const emiInfo = {};
              emiInfo['emiId'] = emiId;
              let autoDebitMsg = '';
              if (isPengingAutodebit) {
                emiInfo['stepperInfo'] = stepperInfo;
                autoDebitMsg = kLoanMaintainSufficientBalance;
              }
              emiInfo['autoPlaceMess'] = autoDebitMsg;
              const diff = this.typeService.dateDifference(emiDate, new Date());
              emiInfo['autoPlaceNotSuccess'] = isPengingAutodebit
                ? diff >= 1
                  ? kAutoDebitNotSuccessFull
                  : ''
                : kAutoDebitNotInitilize;
              emiInfo['pendingAutodebit'] = isPengingAutodebit;
              const find = autoPayDetail.find((f) => f?.emiId == emiId);
              if (!find) autoPayDetail.push(emiInfo);
            } catch (error) {}
          }

          //completed transaction
          if (status == kCompleted) totalRepaidAmount += paidAmount ?? 0;
        } catch (error) {}
      }
      return { autoPayDetail, totalRepaidAmount };
    } catch (error) {
      return {};
    }
  }

  //#region add purpose id
  delay = (ms) => new Promise((res) => setTimeout(res, ms));

  async addPurpose(body: any) {
    try {
      await this.delay(500);
      const userId = body.userId;
      const purposeId = body.purposeId;
      const note = (body.note ?? '').trim();
      const attributes = ['id', 'loanStatus', 'purposeId', 'interestRate'];
      /// user active deviceId
      const userAtt = [
        'recentDeviceId',
        'fullName',
        'email',
        'phone',
        'hashPhone',
        'uniqueId',
      ];
      const include = [{ model: registeredUsers, attributes: userAtt }];
      // recentDeviceId
      const options = { where: { userId }, order: [['id', 'desc']], include };
      const loanData = await this.loanRepo.getRowWhereData(attributes, options);
      if (!loanData || loanData === k500Error) return kInternalError;
      const status = loanData.loanStatus;
      let id = loanData.id;
      const finalData: any = { purposeId };
      if (note) finalData.loanPurpose = note;
      /// update
      if (status === 'InProcess' && !loanData.purposeId) {
        finalData.completedLoan = await this.getCompletedLoanCount(userId);
        const result = await this.loanRepo.updateRowData(finalData, id);
        if (!result || result === k500Error) return kInternalError;
      } else if (status === 'Rejected' || status === 'Complete') {
        //// get complited loan count
        finalData.completedLoan = await this.getCompletedLoanCount(userId);
        const activeDeviceId = loanData?.registeredUsers?.recentDeviceId;
        finalData.loanStatus = 'InProcess';
        finalData.userUniqueId = loanData?.registeredUsers?.uniqueId;
        finalData.userId = userId;
        finalData.interestRate = loanData?.interestRate;
        finalData.loanAmount = GLOBAL_RANGES.MAX_LOAN_AMOUNT;
        finalData.duration = MAX_DURATION;
        finalData.activeDeviceId = activeDeviceId;
        const userData = loanData?.registeredUsers;
        finalData.fullName = userData?.fullName ?? null;
        finalData.email = userData?.email ?? null;
        finalData.phone = userData?.phone ?? null;
        finalData.hashPhone = userData?.hashPhone ?? null;
        const result = await this.loanRepo.create(finalData);

        if (!result || result === k500Error) return kInternalError;
        id = result.id;
      }
      const toDay = this.typeService.getGlobalDate(new Date());
      const where = { id: { [Op.ne]: id }, loanStatus: 'InProcess', userId };
      const result = await this.loanRepo.getTableWhereData(['id'], { where });
      let stage = '';
      try {
        const userData = await this.userRepo.getRowWhereData(['id', 'stage'], {
          where: { id: userId },
        });
        if (userData === k500Error) stage = '';
        stage = (userData?.stage ?? '').toString();
      } catch (error) {}

      if (result && result != k500Error && result.length > 0) {
        for (let index = 0; index < result.length; index++) {
          try {
            const loanId = result[index].id;
            await this.loanRepo.updateWhere(
              {
                loanStatus: 'Rejected',
                lastStage: stage,
                manualVerification: '2',
                manualVerificationAcceptId: SYSTEM_ADMIN_ID,
                remark: kDuplicateLoan,
                verifiedDate: toDay.toJSON(),
              },
              loanId,
              { loanStatus: 'InProcess', userId },
            );
          } catch (error) {}
        }
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  // Track 4.1
  //get disbursed details

  async getDisbursementDetails(query) {
    try {
      const loanId = query?.loanId;
      const userId = query?.userId;
      if (!loanId || !userId) return kParamMissing();

      const disbursmentInc: any = { model: disbursementEntity };
      const attributes = [
        'id',
        'payout_id',
        'amount',
        'utr',
        'status',
        'mode',
        'account_number',
        'userId',
        'loanId',
        'bank_name',
      ];
      const include = [
        {
          model: loanTransaction,
          attributes: ['id', 'loan_disbursement_date', 'masterId'],
        },
      ];
      const options = {
        where: { loanId, userId, status: 'processed' },
        include,
      };
      const disbursedData: any = await this.disbursedRepo.getRowWhereData(
        attributes,
        options,
      );
      if (disbursedData == k500Error) return kInternalError;

      const masterData = await this.repoManager.getRowWhereData(
        MasterEntity,
        ['dates'],
        { where: { id: disbursedData?.loan?.masterId } },
      );
      if (masterData == k500Error) throw new Error();

      const data: any = {};
      if (disbursedData) {
        data['disbursedAmount'] = (disbursedData?.amount / 100).toFixed(2);
        data['disbursedDate'] = new Date(masterData?.dates?.disbursement);
        data['transactionId'] = disbursedData?.payout_id;
        data['paidVia'] = disbursedData?.mode;
        data['bank'] = disbursedData?.bank_name;
        data['accountNo'] = disbursedData?.account_number;
        data['bannerUrls'] = KinsuranceBannerUrl;
      }

      const loanDisbursed = disbursedData ? true : false;
      return {
        loanDisbursed,
        data,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get complited loan count
  private async getCompletedLoanCount(userId) {
    try {
      const where = { userId, loanStatus: 'Complete' };
      const count = await this.loanRepo.getCountsWhere({ where });
      if (count !== k500Error) return count;
      return 0;
    } catch (error) {
      return 0;
    }
  }
  //#endregion

  async preDataAndSendDisbursementWhatsappMsg(loanData) {
    try {
      const adminId = loanData?.adminId ?? SYSTEM_ADMIN_ID;
      const loanId = loanData?.id;
      const appType = loanData?.appType;
      const amount = this.typeService.amountNumberWithCommas(
        loanData?.disbursementData[0]?.amount / 100,
      );
      const transactionId = loanData?.disbursementData[0]?.utr;
      const userId = loanData?.registeredUsers?.id;
      const customerName = loanData?.registeredUsers?.fullName;
      const email = loanData?.registeredUsers?.email;
      let number = this.cryptService.decryptPhone(
        loanData?.registeredUsers?.phone,
      );
      if (!gIsPROD) number = UAT_PHONE_NUMBER[0];
      const agreementURL = loanData?.eSignData?.signed_document_upload;
      const disburementDate = loanData?.disbursementData[0]?.updatedAt;
      const readableDate = this.dateService.readableDate(disburementDate, true);
      const whatsappOption = {
        customerName,
        email,
        number,
        disbursedAmount: amount,
        transactionId: transactionId,
        disburseDate: readableDate,
        loanId,
        userId,
        agreementURL,
        adminId,
        title: 'Disbursement',
        requestData: 'disbursement',
        appType,
      };

      const reviewLinkOptions = {
        appType,
        userId,
        loanId,
        adminId,
        customerName,
        number,
        title: kReviewLink,
        requestData: kReviewLink,
      };
      this.whatsAppService.sendWhatsAppMessageMicroService(whatsappOption);
      this.whatsAppService.sendWhatsAppMessageMicroService(reviewLinkOptions);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region get pay button info
  private getPayButtonInfo(
    emi_details,
    emi_amount,
    penalty,
    amount,
    emi_list: any,
  ) {
    const pay_emi_info = {};
    if (emi_list) {
      const today = this.typeService.getGlobalDate(new Date()).getTime();
      const filter = emi_list.filter(
        (f) =>
          f.payment_status == '0' && new Date(f.emi_date).getTime() <= today,
      );
      if (filter.length > 0) {
        let total_amount = 0;
        filter.forEach((ele) => {
          const emi_amount = this.strService.readableAmount(
            (+ele.emi_amount + +ele?.penalty).toFixed(),
          );
          const emi_emiNumber = ele?.emiNumber;
          total_amount += +ele.emi_amount + +ele.penalty;
          pay_emi_info['EMI ' + emi_emiNumber + ' Amount'] = emi_amount;

          const delayDays = ele?.penalty_days;
          if (delayDays || ele?.penalty) {
            const more_info = {};
            /// delay day
            if (delayDays)
              more_info['Delayed by'] =
                delayDays + ' Day' + (delayDays > 1 ? 's' : '');

            /// get penalty
            const bounceCharge = ele?.bounceCharge ?? 0;
            let penalty = +ele?.penalty - bounceCharge;
            if (penalty < 0) penalty = 0;
            if (penalty)
              more_info['Penalty'] = this.strService.readableAmount(penalty);

            /// bounce charge
            if (bounceCharge)
              more_info['ECS'] = this.strService.readableAmount(bounceCharge);

            pay_emi_info['emi_' + emi_emiNumber + '_more_info'] = more_info;
          }
        });
        pay_emi_info['Total amount to be repay\n#*Inclusive of Penalty*#'] =
          this.strService.readableAmount(total_amount);
        pay_emi_info['isGreaterEMI'] = true;
        pay_emi_info['amount'] = total_amount;
      } else return null;
    } else {
      pay_emi_info['EMI Amount'] = emi_amount;
      if (penalty) pay_emi_info['Penalty Amount'] = penalty;

      /// delay day
      const delayDays = emi_details?.penalty_days;
      if (delayDays)
        pay_emi_info['Delayed by'] =
          delayDays + ' Day' + (delayDays > 1 ? 's' : '');

      /// bounce charge
      const bounceCharge = emi_details?.bounceCharge ?? 0;
      if (bounceCharge)
        pay_emi_info['ECS'] = this.strService.readableAmount(bounceCharge);

      const key =
        'Total amount to be repay' +
        (penalty ? '\n#*Inclusive of Penalty*#' : '');
      pay_emi_info[key] = this.strService.readableAmount(amount);
      pay_emi_info['isGreaterEMI'] = false;
      pay_emi_info['amount'] = amount;
    }
    return pay_emi_info;
  }
  //#endregion

  // start region get loan and emi data from UId
  async fetchDataFromUId(query) {
    try {
      const key = query.key;
      if (!key) return kParamsMissing;
      const loanId = key / 484848;
      if (isNaN(loanId)) return k422ErrorMessage('Provide valid key');
      const transInclude: SequelOptions = { model: TransactionEntity };
      transInclude.required = false;
      transInclude.where = { status: kCompleted };
      transInclude.attributes = ['id', 'paidAmount'];
      const loanAttr = [
        'id',
        'loan_disbursement_date',
        'interestRate',
        'loanStatus',
        'netApprovedAmount',
        'loanCompletionDate',
        'appType',
        'loanClosureMailCount',
        'settlementMailCount',
        'fullName',
        'userId',
      ];

      const emiInclude: any = {
        model: EmiEntity,
      };
      emiInclude.attributes = [
        'id',
        'emi_date',
        'partPaymentPenaltyAmount',
        'payment_due_status',
        'payment_status',
        'penalty',
        'penalty_days',
        'emi_amount',
        'principalCovered',
        'interestCalculate',
        'settledId',
      ];
      const loanData = await this.loanRepo.getRowWhereData(loanAttr, {
        where: { id: loanId },
        include: [emiInclude, transInclude],
      });
      if (loanData === k500Error) return kInternalError;
      if (!loanData) return k422ErrorMessage(kNoDataFound);
      const TotalRepayAmount = loanData?.transactionData.reduce(
        (total, element) => {
          return total + (element?.paidAmount ?? 0);
        },
        0,
      );

      let isLoanClosure = false;
      let isLoanSettled = false;
      // Checking for link sent by admin(loan closure offer or Loan Settlement).
      if (
        (loanData?.loanClosureMailCount ?? 0) > 0 ||
        (loanData?.settlementMailCount ?? 0) > 0
      ) {
        const link = await this.repoManager.getRowWhereData(
          PaymentLinkEntity,
          ['mailType'],
          {
            where: { loanId, isActive: true },
            order: [['id', 'DESC']],
          },
        );
        isLoanSettled = link && link?.mailType == '1' ? true : false;
        isLoanClosure = link && link?.mailType == '2' ? true : false;
      }

      const todayDate = this.typeService.getGlobalDate(new Date());
      const repayAmount = this.strService.readableAmount(TotalRepayAmount);
      const approvedAmount = this.strService.readableAmount(
        loanData?.netApprovedAmount,
      );
      const tempObj: any = {
        userId: loanData?.userId,
        name: loanData?.fullName ?? '',
        fullpayAmount: 0,
        totalEmiAmount: 0,
        emiDate: null,
        penaltyAmount: 0,
        penaltyDays: 0,
        emiId: null,
        loanId: loanData.id,
        isLoanCompleted: loanData.loanStatus == 'Complete',
        playStoreLink:
          loanData?.appType == 1 ? NBFCPlayStoreLink : lspPlaystoreLink,
        appStoreLink:
          loanData?.appType == 1 ? NBFCAppStoreLink : lspAppStoreLink,
        totalRepayAmount: repayAmount,
        approvedAmount: approvedAmount,
        loanCompletionDate: loanData?.loanCompletionDate
          ? this.typeService.getDateFormated(loanData?.loanCompletionDate, '/')
          : '-',
        customerCareNumber:
          loanData?.appType == 0
            ? kLspHelpContactAfterDisbursement
            : kHelpContact,
        customerCareMail:
          loanData?.appType == 0 ? KLspSupportMail : kSupportMail,
        isLoanClosure,
        isLoanSettled,
      };
      let emiData = loanData.emiData;
      emiData = emiData.sort(
        (a, b) =>
          new Date(a.emi_date).getTime() - new Date(b.emi_date).getTime(),
      );
      if (!emiData) return kInternalError;
      const previousEmis = [];
      const upcommingEmis = [];
      let isDefaulter = false;
      let paidEmiCounts = 0;
      for (let i = 0; i < emiData.length; i++) {
        try {
          const emiItem = emiData[i];
          if (emiItem.payment_due_status === '1') isDefaulter = true;
          if (
            emiItem.payment_status === '0' &&
            new Date(emiItem.emi_date).getTime() < new Date(todayDate).getTime()
          ) {
            previousEmis.push(emiItem);
          } else if (emiItem.payment_status === '0')
            upcommingEmis.push(emiItem);
          if (emiItem.payment_status === '1') paidEmiCounts += 1;
        } catch (error) {}
      }
      tempObj.loanStatus = isDefaulter ? 'Delay' : 'On-Time';
      if (tempObj.isLoanCompleted) return tempObj;

      loanData.loanId = loanData?.id;
      //  full pay amount calculation
      const fullPayData = await this.sharedCalculation.getFullPaymentData(
        loanData,
      );
      tempObj.fullpayAmount = fullPayData.totalAmount;
      if (emiData.length == paidEmiCounts) tempObj.isLoanCompleted = true;
      if (previousEmis.length <= 1) {
        if (previousEmis.length == 1) {
          tempObj.totalEmiAmount =
            +previousEmis[0].emi_amount + +previousEmis[0].penalty;
          tempObj.penaltyAmount = +(previousEmis[0].penalty ?? 0);
          tempObj.penaltyDays = previousEmis[0].penalty_days ?? '0';
          tempObj.emiDate = previousEmis[0].emi_date;
          tempObj.emiId = previousEmis[0].id;
        } else if (upcommingEmis.length) {
          tempObj.totalEmiAmount =
            +upcommingEmis[0].emi_amount + +upcommingEmis[0].penalty;
          tempObj.penaltyAmount = +(upcommingEmis[0].penalty ?? 0);
          tempObj.penaltyDays = upcommingEmis[0].penalty_days ?? '0';
          tempObj.emiDate = upcommingEmis[0].emi_date;
          tempObj.emiId = upcommingEmis[0].id;
        }
      }
      let payment_service = await this.commonSharedService.getServiceName(
        'PAYMENT_MODE',
      );
      const netBankingSource = payment_service ?? kRazorpay;
      tempObj.netBankingSource = netBankingSource;
      return tempObj;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async acceptAmountCharges(reqData) {
    // Params validation
    const userId = reqData?.userId;
    const appType = reqData?.appType;
    if (!userId) return kParamMissing('userId');
    const loanId = reqData?.loanId;
    if (!loanId) return kParamMissing('loanId');
    const amount: number = reqData?.amount;
    if (!amount) return kParamMissing('amount');
    const typeOfDevice = reqData?.typeOfDevice;
    let MIN_LOAN_AMOUNT = GLOBAL_RANGES.MIN_LOAN_AMOUNT;
    const MAX_LOAN_AMOUNT = GLOBAL_RANGES.MAX_LOAN_AMOUNT;
    if (amount < MIN_LOAN_AMOUNT && EnvConfig.nbfc.nbfcType == '1')
      MIN_LOAN_AMOUNT = amount;

    const loanData = await this.getLoanData(reqData);
    if (loanData.message) return loanData;

    if (
      (amount < MIN_LOAN_AMOUNT || amount > loanData?.loanAmount) &&
      amount <= MAX_LOAN_AMOUNT
    ) {
      return k422ErrorMessage(kSelectedLoanAmount);
    }

    if (loanData?.loanStatus != 'InProcess')
      return k422ErrorMessage('Loan is not in progress');

    let emiDate = loanData?.emiSelection?.selectedEmiDate;
    // Purpose -> Analysis
    if (!emiDate) {
      const bankingData = await this.repoManager.getRowWhereData(
        BankingEntity,
        ['salaryDate'],
        loanData.bankingId,
      );
      if (bankingData && bankingData != k500Error) {
        const default_emi_date = this.sharedEmi.getdefaultEMIDate(
          loanData,
          bankingData,
        );
        if (default_emi_date) {
          emiDate = default_emi_date;
        }
      }
    }

    await this.repository.updateRowData({ netApprovedAmount: amount }, loanId);

    const insuranceOptValue = reqData?.isInsurancePolicy ?? false;

    const calculation = await this.sharedEmi.refreshCalculation(loanId, null, {
      emiDate,
      insuranceOptValue,
      selectedTenure: reqData?.selectedTenure,
    });
    if (calculation?.message) return calculation;

    calculation.typeOfDevice = typeOfDevice;
    calculation.getKFS = reqData?.getKFS;
    calculation.getAgreement = reqData?.getAgreement;

    return await this.prepareAcceptScreen(calculation, appType);
  }
  //#endregion

  private async getLoanData(reqData) {
    try {
      const userId = reqData?.userId;
      if (!userId) return kParamMissing('userId');
      const loanId = reqData?.loanId;
      if (!loanId) return kParamMissing('loanId');
      const attr = [
        'completedLoan',
        'bankingId',
        'id',
        'userId',
        'emiSelection',
        'loanStatus',
        'loanAmount',
        'verifiedSalaryDate',
      ];
      const opts = { where: { id: loanId, userId } };
      const loanData = await this.repository.getRowWhereData(attr, opts);
      if (loanData === k500Error) return kInternalError;
      else if (!loanData) return kBadRequest;
      return loanData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async prepareAcceptScreen(data, appType) {
    try {
      const result: any = {};
      const amount = data?.netApprovedAmount ?? 0;
      const approvedAmount = this.typeService.amountNumberWithCommas(amount);
      const tenure = data?.approvedDuration;
      const charges = data?.charges ?? {};
      const processPr = data?.processingFees;
      const interestRate = data?.interestRate;
      const anummIntrest = data?.anummIntrest ?? interestRate;
      let processingFees: any = this.typeService.manageAmount(
        (amount * processPr) / 100,
      );
      const gstPr = charges?.gst_per;
      let gstCharge = charges?.gst_amt ?? 0;
      const docPr = charges?.doc_charge_per;
      let docCharge = charges?.doc_charge_amt ?? 0;
      let insuranceFee = charges?.insurance_fee ?? 0;
      const riskPr = charges?.risk_assessment_per ?? 1;
      let riskCharge = charges?.risk_assessment_charge ?? 0;
      const stampFees = data?.stampFees ?? 0;
      const insurance = data?.insuranceDetails ?? {};
      const loanId = data?.loanId;
      const getKFS = data?.getKFS ?? false;
      const getAgreement = data?.getAgreement ?? false;
      const typeOfDevice = data?.typeOfDevice;
      const netEmiData = this.getEmiList(data?.netEmiData ?? []);
      const emiData = netEmiData?.emiList;
      const colleteralPer = charges?.collateral_charge_per;
      let collateralCharge = charges?.collateral_charge_amt ?? 0;
      const isCollateral = data?.isCollateral ?? false;
      const sixMonthCollateralTenure = data?.sixMonthCollateralTenure ?? {};
      const collateralEmiCalculation = data?.collateralEmiCalculation ?? {};

      let aprCharges = 0;
      const percentageChange =
        ((netEmiData.totalInterest +
          +data?.loanFees -
          gstCharge +
          +(insurance?.totalPremium ?? 0)) /
          amount) *
        100;
      aprCharges = (percentageChange * 365) / +tenure;
      aprCharges = +aprCharges.toFixed(2);

      const premium = Math.round(insurance?.totalPremium ?? 0);
      let disburseAmount =
        amount -
        processingFees -
        stampFees -
        docCharge -
        insuranceFee -
        gstCharge -
        riskCharge;

      result['minAmount'] = GLOBAL_RANGES.MIN_LOAN_AMOUNT;
      result['eligibleAmount'] =
        data?.approvedLoanAmount ?? data?.loanAmount ?? data?.netApprovedAmount;
      result['approvedAmount'] = `${kRuppe}${approvedAmount}`;
      let gstOn = 'Processing Charge';
      const chargesInfo = {};
      processingFees = this.typeService.amountNumberWithCommas(processingFees);
      if (!processingFees.includes('.')) processingFees += '.00';
      chargesInfo[
        `Processing Charge @${processPr}%`
      ] = `-${kRuppe}${processingFees}`;
      if (insuranceFee) {
        insuranceFee = this.typeService.amountNumberWithCommas(insuranceFee);
        if (!insuranceFee.includes('.')) insuranceFee += '.00';
        chargesInfo['Online Convenience Charge'] = `-${kRuppe}${insuranceFee}`;
        gstOn += ' + Online Convenience Charge';
      }

      docCharge = this.typeService.amountNumberWithCommas(docCharge);
      if (!docCharge.includes('.')) docCharge += '.00';
      chargesInfo[`Documentation Charge @${docPr}%`] = `-${kRuppe}${docCharge}`;
      gstOn += ' + Documentation Charge';

      if (riskCharge) {
        riskCharge = this.typeService.amountNumberWithCommas(riskCharge);
        if (!riskCharge.includes('.')) riskCharge += '.00';
        chargesInfo[
          `Risk Assessment Charge @${riskPr}%`
        ] = `-${kRuppe}${riskCharge}`;
        gstOn += ' + Risk Assessment Charge';
      }

      gstCharge = this.typeService.amountNumberWithCommas(gstCharge);
      if (!gstCharge.includes('.')) gstCharge += '.00';
      chargesInfo[`GST @${gstPr}% \n#*${gstOn}*#`] = `-${kRuppe}${gstCharge}`;

      if (data.insuranceOptValue == true) {
        chargesInfo['Loan protect insurance premium'] =
          appType != 0
            ? `-${this.strService.readableAmount(premium.toFixed(2))}`
            : `-${this.strService.readableAmount(premium)}`;
        disburseAmount -= premium;
      }
      if (collateralCharge) {
        collateralCharge =
          this.typeService.amountNumberWithCommas(collateralCharge);
        chargesInfo[
          `Cash Collateral @${colleteralPer}% \n#*The collateral amount will be \nadjusted in your last EMI*#`
        ] = `-${kRuppe}${collateralCharge}`;
      }
      if (GlobalServices.INSURANCE_SERVICE != 'NONE')
        result['insurancePolicy'] = KInsurancePolicy;

      result['chargesInfo'] = chargesInfo;
      result['isExpandableChargesView'] = true;
      result['gstOn'] = gstOn;

      result.emiData = emiData;
      const getOrdinal = (n) =>
        n === 1 ? '1st' : n === 2 ? '2nd' : n === 3 ? '3rd' : `${n}th`;
      const emiDates =
        emiData && emiData.length
          ? [
              {
                key: `${getOrdinal(1)} EMI`,
                value: emiData[0].emiDate,
                isEdit: true,
              },
              {
                key: `${getOrdinal(emiData.length)} EMI #*(Last EMI)*#`,
                value: emiData[emiData.length - 1].emiDate,
              },
            ]
          : [];
      result.emiDates = emiDates;
      const totalInterest = this.typeService.amountNumberWithCommas(
        netEmiData.totalInterest,
      );
      result['totalInterest'] = `${kRuppe}${totalInterest}`;
      result['forcefullEmiSelection'] = data?.canSelectEmiDate ? true : false;
      const emiSelectedDate = data?.emiSelectedDate;
      result['emiSelectedDate'] = emiSelectedDate;
      result['insuranceOptValue'] = GlobalServices.INSURANCE_OPT_VALUE ?? false;
      if (
        GlobalServices.INSURANCE_SERVICE &&
        GlobalServices.INSURANCE_SERVICE != 'NONE'
      ) {
        result['insurance'] = data.insurance;
        result['isInsurance'] = true;
      }

      disburseAmount = this.typeService.manageAmount(
        disburseAmount,
        disburseAmt,
      );
      result[
        'netDisbursementAmount'
      ] = `${kRuppe}${this.typeService.amountNumberWithCommas(disburseAmount)}`;
      result['calenderData'] =
        this.commonSharedService.getCalenderDataForEMI(emiSelectedDate);
      result['interestRate'] = `${anummIntrest}% Per annum`;

      result[
        'latePaymentTag'
      ] = `In the event of delayed EMI payment, regular interest accumulates on the outstanding EMI principal amount, in addition to any applicable penal charges.`;

      result[
        'totalInterestText'
      ] = `The annual percentage rate on your loan is ${aprCharges}%`;

      result[
        'loanOfferConsent'
      ] = `I have reviewed and accept the Bank details, EMI details, #*KFS*# and #*Loan Agreement*#.`;

      result.isCollateral = isCollateral;

      result.emiMonths = {
        key: 'EMIs',
        value: data?.emis,
        isEdit: false,
        loanTenure: `${tenure} days`,
        emisIcon: kEmiIcon,
        monthsTitle: 'Repayment Period (EMIs)',
        monthsSubTitle: 'Select the number of EMIs for your loan',
        ...(isCollateral && {
          monthsCategory: [
            {
              noOfMonths: data?.emis,
              monthsAndTenures: `${data?.emis} EMIs  #*(${tenure} days)*#`,
            },
            {
              noOfMonths: sixMonthCollateralTenure?.totalEMIs,
              monthsAndTenures: `${sixMonthCollateralTenure?.totalEMIs} EMIs  #*(${sixMonthCollateralTenure?.totalDays} days)*#`,
            },
          ].sort((a, b) => a.noOfMonths - b.noOfMonths),
          ...collateralEmiCalculation,
        }),
      };

      if (getKFS) {
        const kfsData = await this.getKeyFactStatement(loanId);
        if (kfsData.message) return kfsData;
        result['kfsData'] = kfsData;
      }

      if (getAgreement) {
        // Validate and prepare data
        const preparedData: any =
          await this.esignSharedService.validateAgreementRequest(
            loanId,
            true,
            true,
          );
        if (preparedData == k500Error) return kInternalError;
        if (preparedData == false) return k422ErrorMessage(kSomthinfWentWrong);

        const loan_agreement = preparedData.loan_agreement;
        const agreementData = await this.esignSharedService.createAgreement(
          preparedData,
        );
        if (agreementData == k500Error || agreementData?.message)
          throw new Error();
        else if (agreementData == false)
          return k422ErrorMessage(kSomthinfWentWrong);

        const agreementPdf = await this.esignSharedService.sendAgreementRequest(
          preparedData,
          agreementData.agreementPath,
          typeOfDevice,
        );
        // Save invitation response
        if (agreementPdf == k500Error) throw new Error();
        else if (agreementPdf == false)
          return k422ErrorMessage(kSomthinfWentWrong);

        if (loan_agreement) {
          await this.repository.updateRowData({ loan_agreement: null }, loanId);
          const res = await this.fileService.deleteCloudFile(loan_agreement);
          if (res != 204) throw new Error();
        }

        result['loanAgreement'] = agreementPdf.signed_document;

        const updatedData = {
          loan_agreement: agreementPdf.signed_document,
        };
        await this.repository.updateRowData(updatedData, loanId);
      }
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
    }
  }

  getEmiList(netEmiData) {
    try {
      const emiList = [];
      let totalInterest = 0;
      netEmiData.forEach((el, index) => {
        try {
          const emiNo = (index + 1).toString();
          const emiAmt = this.typeService.amountNumberWithCommas(el?.Emi ?? 0);
          const principalAmt = this.typeService.amountNumberWithCommas(
            el?.PrincipalCovered ?? 0,
          );
          const interestAmt = this.typeService.amountNumberWithCommas(
            el?.InterestCalculate ?? 0,
          );
          totalInterest += el?.InterestCalculate ?? 0;
          const emi = {
            emiNo,
            emiDate: this.dateService.dateToReadableFormat(
              el?.Date,
              'DD/MM/YYYY',
            ).readableStr,
            principalAmt: `${kRuppe}${principalAmt}`,
            interestAmt: `${kRuppe}${interestAmt}`,
            totalEmiAmt: `${kRuppe}${emiAmt}`,
          };
          emiList.push(emi);
        } catch (error) {}
      });
      return { emiList, totalInterest };
    } catch (error) {
      return {};
    }
  }

  //startregion for sending NOC to  user on his request
  async funNocRequestByUser(query) {
    try {
      const loanId = query?.loanId;
      const nocRequestKey = loanId + 'NOC_DETALS';
      if (!loanId) return kParamMissing('loanId');
      const loanAtr = ['nocURL'];
      const loanOptions = {
        where: {
          id: loanId,
          loanStatus: 'Complete',
        },
      };
      const loanData = await this.repository.getRowWhereData(
        loanAtr,
        loanOptions,
      );
      if (loanData === k500Error) return kInternalError;
      if (loanData?.nocURL) return { nocURL: loanData?.nocURL };

      //if NOC not sent
      const data = await this.sharedTransactionService.createUserNoc({
        loanId,
      });
      await this.redisService.del(nocRequestKey); //delete redis key
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  private escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
  }

  private replaceAll(str, find, replace) {
    return str.replace(new RegExp(this.escapeRegExp(find), 'g'), replace);
  }

  //#region
  async addScreenshot(reqData) {
    let { imageUrl, emiId } = reqData;
    if (!imageUrl) return kParamMissing('imageUrl');
    if (!emiId) return kParamMissing('emiId');

    const emiData = await this.repoManager.getRowWhereData(
      EmiEntity,
      ['emi_amount'],
      { where: { id: emiId } },
    );
    if (emiData == k500Error)
      return k422ErrorMessage(PAYMENT_VERIFY_RESPONSE.default);

    // Converting URL --> Image
    const filePath = await this.fileService.fileUrlToFile(imageUrl);
    const body = new FormData();
    const mediaPath = await fs.readFileSync(filePath);
    body.append('file', mediaPath, filePath);
    body.append('amount', emiData?.emi_amount);

    // Calling Python API to Check Screenshot Uploaded By User is of Transaction or Not.
    const response = await this.apiService.post(nPaymentVerify, body);
    if (response?.valid == false || response == k500Error)
      return k422ErrorMessage(PAYMENT_VERIFY_RESPONSE.default);

    await this.fileService.removeFile(filePath);

    let isAutoDebitFailed = false;
    if (response?.data?.payment_status > 1) {
      if (response?.data?.payment_status == 3) {
        const key = `${emiId}_AUTODEBIT_PAYMENT_VERIFY`;
        await this.redisService.hSet(
          key,
          { data: JSON.stringify(response?.data) },
          NUMBERS.ONE_DAY_IN_SECONDS,
        );
        isAutoDebitFailed = true;
      }

      return {
        ...k400ErrorMessage(
          PAYMENT_VERIFY_RESPONSE[response?.data?.payment_status],
        ),
        isAutoDebitFailed,
      };
    }
    return this.updateEmiPaymentData(emiId, imageUrl);
  }
  //#endregion

  //#region update emi screen short
  private async updateEmiPaymentData(emiId, imageUrl) {
    // Updating EMI
    let emiUpdate = await this.emiRepo.updateRowData(
      { adScreenshotUrl: imageUrl },
      emiId,
    );
    if (emiUpdate == k500Error)
      return k422ErrorMessage(PAYMENT_VERIFY_RESPONSE.default);
    return PAYMENT_VERIFY_RESPONSE.success;
  }
  //#endregion
}
