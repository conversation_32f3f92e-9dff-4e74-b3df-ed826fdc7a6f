// Imports
import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({})
export class CallKaroEntity extends Model<CallKaroEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: false,
  })
  stage: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Response from CallKaro API',
  })
  response: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Call SID from CallKaro',
  })
  call_sid: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  date: Date;
}
