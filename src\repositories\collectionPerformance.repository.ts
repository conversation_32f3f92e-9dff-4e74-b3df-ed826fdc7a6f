import { Inject, Injectable } from '@nestjs/common';
import { COLLECTION_PERFORMANCE_REPORT } from '../constants/entities';
import { RepositoryManager } from './repository.manager';
import { CollectionPerformanceReport } from 'src/entities/collectionPerformanceReport.entity';

@Injectable()
export class CollectionPerformanceReportRepository {
  constructor(
    @Inject(COLLECTION_PERFORMANCE_REPORT)
    private readonly repository: typeof CollectionPerformanceReport,
    private readonly repoManager: RepositoryManager,
  ) {}

  async getTableWhereData(attributes: string[], options: any) {
    return await this.repoManager.getTableWhereData(
      this.repository,
      attributes,
      options,
    );
  }

  async bulkCreate(data) {
    return this.repoManager.bulkCreate(this.repository, data);
  }

  async deleteWhereData(options: any) {
    return await this.repoManager.deleteWhereData(this.repository, options);
  }

  async getTableWhereDataWithCounts(attributes: string[], options: any) {
    return this.repoManager.getTableCountWhereData(
      this.repository,
      attributes,
      options,
    );
  }

  async updateRowData(updatedData: any, id: number | number[]) {
    return await this.repoManager.updateRowData(this.repository, updatedData, {
      where: { id },
    });
  }
  
  async updateRowWhereData(updatedData: any, options) {
    return await this.repoManager.updateRowWhereData(
      this.repository,
      updatedData,
      options,
    );
  }

  //#region  update where Data
  async updateData(updateData: any, where: any) {
    return await this.repoManager.updateData(
      this.repository,
      updateData,
      where,
    );
  }
  //#endregion
  

  async getRowWhereData(attributes: string[], options: any) {
    return await this.repoManager.getRowWhereData(
      this.repository,
      attributes,
      options,
    );
  }
}
