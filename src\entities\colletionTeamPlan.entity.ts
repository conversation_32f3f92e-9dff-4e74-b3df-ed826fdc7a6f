import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { admin } from './admin.entity';

@Table({})
export class CollectionTeamPlanEntity extends Model<CollectionTeamPlanEntity> {
  @Column({
    type: DataType.SMALLINT,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
    allowNull: false,
  })
  isActive: boolean;

  @Column({
    type: DataType.SMALLINT,
    allowNull: false,
    comment: 'Minimum delay in days for assigning a case',
  })
  minDelay: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: false,
    comment: 'Maximum delay in days for assigning a case',
  })
  maxDelay: number;

  @ForeignKey(() => admin)
  @Column({ type: DataType.INTEGER, allowNull: false })
  updatedBy: number;

  @BelongsTo(() => admin)
  adminData: admin;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    defaultValue: [],
    allowNull: false,
  })
  employees: number[];

  @Column({
    type: DataType.DOUBLE,
    defaultValue: 0,
    allowNull: false,
  })
  goalPercentage: number;
}
