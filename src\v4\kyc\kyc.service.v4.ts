// Imports
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { Op } from 'sequelize';
import {
  gIsPROD,
  SIGNDESK,
  NAME_MISS_MATCH_PER,
  SYSTEM_ADMIN_ID,
  ZOOP,
  MIN_AGE,
  isUAT,
  GLOBAL_FLOW,
  GLOBAL_RANGES,
  SERVER_MODE,
  BAD_CIBIL_SCORE_REASON_ID,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  CLOUD_FOLDER_PATH,
  getmAadhaarData,
  UserStage,
} from 'src/constants/objects';
import { MockResponses } from 'src/constants/objects';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
} from 'src/constants/responses';
import {
  kAddAadhareNumberRoute,
  kKYCInfo,
  kWebviewRoute,
  kAadhaarService,
  kEnterValidAadhaarNumber,
  kNoDataFound,
  kSigndesk,
  kDigiLocker,
  BAD_CIBIL_SCORE_MSG,
  kMinAgeCriteria,
  kMaxAgeCriteria,
  kErrorMsgs,
  kSomthinfWentWrong,
} from 'src/constants/strings';
import { regPanCard } from 'src/constants/validation';
import { KYCEntity } from 'src/entities/kyc.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { KYCRepository } from 'src/repositories/kyc.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { MissMacthRepository } from 'src/repositories/missMatchName.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { EligibilitySharedService } from 'src/shared/eligibility.shared.service';
import { SigndeskService } from 'src/thirdParty/signdesk/signdesk.service';
import { ZoopService } from 'src/thirdParty/zoop/zoop.service';
import { FileService } from 'src/utils/file.service';
import { TypeService } from 'src/utils/type.service';
import { ValidationService } from 'src/utils/validation.service';
import { CibilService } from 'src/shared/cibil.service';
import { AdminService } from 'src/admin/admin/admin.service';
import { AddressesRepository } from 'src/repositories/addresses.repository';
import { DigiLockerService } from 'src/thirdParty/digilocker/digilocker.service';
import { CibilScoreRepository } from 'src/repositories/cibil.score.repository';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { EnvConfig } from 'src/configs/env.config';
import { UserServiceV4 } from '../user/user.service.v4';
import { CryptService } from 'src/utils/crypt.service';
import { RedisService } from 'src/redis/redis.service';
import { CommonService } from 'src/utils/common.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { QAAutomationUserList } from 'src/constants/automation';
import { FetchDataEntity } from 'src/entities/fetchData.entity';
import { DigiTapService } from 'src/thirdParty/digitap/digitap.service';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { NUMBERS } from 'src/constants/numbers';
const fs = require('fs');

@Injectable()
export class KycServiceV4 {
  constructor(
    private readonly fileService: FileService,
    private readonly masterRepo: MasterRepository,
    private readonly misMatchRepo: MissMacthRepository,
    private readonly repository: KYCRepository,
    private readonly typeService: TypeService,
    private readonly userRepo: UserRepository,
    private readonly zoopService: ZoopService,
    private readonly digiTapService: DigiTapService,
    private readonly commonSharedService: CommonSharedService,
    private readonly sigDeskservice: SigndeskService,
    private readonly validation: ValidationService,
    @Inject(forwardRef(() => EligibilitySharedService))
    private readonly eligibilitySharedService: EligibilitySharedService,
    @Inject(forwardRef(() => CibilService))
    private readonly cibilService: CibilService,
    @Inject(forwardRef(() => AdminService))
    private readonly adminService: AdminService,
    private readonly addressesRepo: AddressesRepository,
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userService: UserServiceV4,
    private readonly cryptService: CryptService,
    // Database
    private readonly repoManager: RepositoryManager,
    // Third party services
    private readonly digiLocker: DigiLockerService,
    private readonly cibilScoreRepo: CibilScoreRepository,
    private readonly redisService: RedisService,
    private readonly commonService: CommonService,
    private readonly errorContextService: ErrorContextService,
    private readonly slackService: SlackService,
  ) {}

  async validatemAadhaar(reqData) {
    try {
      const userId = reqData?.userId;
      const otp = reqData?.otp;
      let internalResponse;
      if (otp) {
        const response = await this.aadhaarOtpVerify(reqData);
        if (response?.message) return response;
        reqData.internalResponse = response;
      }
      internalResponse = reqData.aadhaarResponse ?? reqData.internalResponse;
      if (!internalResponse) return kParamMissing('internalResponse');
      if (typeof internalResponse != 'object')
        internalResponse = JSON.parse(internalResponse);

      // Converting response into simple object
      const mAadhaarResponse: any = await this.fineTunemAadhaarResponse(
        internalResponse,
      );

      if (mAadhaarResponse.message) return mAadhaarResponse;

      // Checks whether aadhaar number exists in another user or not
      const aadhaarNumber =
        mAadhaarResponse?.basicDetails?.aadhaarNumber || reqData?.aadhaarNumber;

      if (
        reqData?.aadhaarResponse?.responseData?.aadhaar_service !=
        'ZOOP_DIGILOCKER'
      ) {
        const existingData: any = await this.isAadhaarExists(
          userId,
          aadhaarNumber,
        );

        if (existingData.message) return existingData;

        // this fun check already approved aadhaar number match with new ones
        // this fun use for rekyc, delete account and old defuter
        const alreadyApproved = await this.checkUserAadhaarAlreadyApproved(
          userId,
          aadhaarNumber,
        );
        if (alreadyApproved?.message) return alreadyApproved;
      }

      // Get lat long of the aadhaar address
      const aadhaarLatLong = await this.getAadhaarCoordinates(
        mAadhaarResponse.aadhaarAddress,
      );

      // Validate user data
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = ['id', 'status', 'dates', 'otherInfo'];
      const include = [masterInclude];
      const attributes = [
        'kycId',
        'completedLoans',
        'createdAt',
        'isRedProfile',
        'leadId',
        'state',
        'isCibilConsent',
      ];
      const options = { include, where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);
      if (!userData.kycId) return k422ErrorMessage(kNoDataFound);

      const statusData = userData.masterData.status ?? {};
      const dates = userData.masterData.dates ?? {};
      const masterId = userData.masterData.id;
      const isRedProfile = userData?.isRedProfile === 2;
      const masterData = userData.masterData;
      // if (statusData.aadhaar == '1' || statusData.aadhaar == '3')
      //   return k422ErrorMessage('Aadhaar already verified');

      // Update KYC data
      const basicDetails = mAadhaarResponse.basicDetails ?? {};

      // store category score point
      await this.commonSharedService.getRiskCategoryByScoreData({
        userId,
        masterData,
        aadhaarState: mAadhaarResponse?.aadhaarAddress?.state,
        aadhaarDOB: basicDetails.dob,
        liveState: userData.state,
      });

      let pincode = this.typeService.getPincodeFromAddress(
        mAadhaarResponse.aadhaarResponse,
      );
      let customerRefId = null;
      const aadhaarNo = this.cryptService.getMD5Hash(aadhaarNumber);
      if (
        reqData?.aadhaarResponse?.responseData?.aadhaar_service !=
        'ZOOP_DIGILOCKER'
      ) {
        customerRefId = aadhaarNumber
          ? Number(aadhaarNumber.substring(0, 8))
          : null;
      }
      let updatedData: any = {
        aadhaarLatLong,
        aadhaarNo,
        customerRefId: customerRefId ?? 0,
        aadhaarState: mAadhaarResponse?.aadhaarAddress?.state,
        aadhaarDOB: basicDetails.dob,
        profileImage: basicDetails.profileImage,
        maskedAadhaar: this.getMaskedAadhaarNumber(aadhaarNumber),
        aadhaarStatus: '1',
        aadhaarVerifiedAdmin: SYSTEM_ADMIN_ID,
        aadhaarResponse: mAadhaarResponse?.aadhaarResponse,
        aadhaarAddress: JSON.stringify(mAadhaarResponse.aadhaarAddress),
        pincode,
        kyc_mode:
          mAadhaarResponse.aadhaarServiceMode ??
          (await this.commonSharedService.getServiceName(kAadhaarService)),
      };
      if (this.isMaskedAadhaar(aadhaarNumber)) {
        delete updatedData.aadhaarNo, delete updatedData.maskedAadhaar;
      }
      const kycMode = updatedData.kyc_mode;
      if (kycMode == kDigiLocker) {
        const aadhaarResponse = JSON.parse(mAadhaarResponse?.aadhaarResponse);
        const match =
          aadhaarResponse?.digiReresponse?.authResponse?.scope?.split(
            'pan-PANCR-',
          );
        if (match && match?.length > 1) {
          const pan = match[1]?.split(' ')[0];
          const isValidate = regPanCard(pan);
          if (isValidate) {
            const panCardNumber = pan;
            const maskedPan =
              pan.substring(0, 2) +
              'xxx' +
              pan[5] +
              'xx' +
              pan.substring(8, 10);
            const hashPan = this.cryptService.getMD5Hash(pan);
            const kycUpdateData = {
              panCardNumber,
              maskedPan,
              hashPan,
            };
            const result = await this.repository.updateRowData(
              kycUpdateData,
              userData.kycId,
            );
            if (result == k500Error) return kInternalError;
            //delete kyc data from redis
            await this.redisService.del(`KYC_DATA_${userId}`);
            await this.redisService.del(`APP_USER_PROFILE_DATA_${userId}`);
            const kycData = {
              userId: userId,
              pan: pan,
              consentMode: kDigiLocker,
            };
            const validatePan = await this.validatePan(kycData, true);
            if (validatePan.statusData && validatePan.dates) {
              statusData.pan = validatePan?.statusData?.pan;
              dates.pan = validatePan?.dates?.pan;
            }
          }
        }
      }
      if (aadhaarLatLong) {
        const latLngObject = JSON.parse(aadhaarLatLong);
        const lat = latLngObject['lat'];
        const lng = latLngObject['lng'];
        if (lat !== undefined && lng !== undefined)
          updatedData.aadhaarLatLongPoint = `${lat},${lng}`;
      }

      // Checks user is eligible as per age criteria or not
      const ageCriteria: any = this.typeService.isEligibleAsPerAge(
        mAadhaarResponse.basicDetails.dob,
      );
      if (ageCriteria?.type) {
        updatedData.aadhaarStatus = '-1';
        delete updatedData?.aadhaarVerifiedAdmin;
      }
      let updateResult = await this.repository.updateRowData(
        updatedData,
        userData.kycId,
      );
      if (updateResult == k500Error) return kInternalError;
      //delete kyc data from redis
      await this.redisService.del(`KYC_DATA_${userId}`);
      await this.redisService.del(`APP_USER_PROFILE_DATA_${userId}`);
      //add address in all address entity
      try {
        const aadhaarAddress = this.typeService.getUserAadharAddress(
          updatedData?.aadhaarAddress,
        );
        await this.createOrUpdateAddress({
          userId,
          address: aadhaarAddress,
          type: '0',
          status: '1',
          subType: 'AADHAAR',
        });
      } catch (error) {}
      // Update user data
      updatedData = {
        fullName: basicDetails.name,
        gender: basicDetails.gender,
        state: (mAadhaarResponse?.aadhaarAddress?.state ?? '').toUpperCase(),
        city: (
          mAadhaarResponse?.aadhaarAddress?.districtName ?? ''
        ).toUpperCase(),
      };
      updateResult = await this.userRepo.updateRowData(updatedData, userId);
      if (updateResult == k500Error) return kInternalError;
      // here we have to update name again

      // Update master data
      updatedData = { status: statusData, dates };
      updatedData.status.aadhaar = 1;
      updatedData.dates.aadhaar = new Date().getTime();
      // if old defulter then approved
      if (isRedProfile) {
        updatedData.status.selfie = 1;
        updatedData.dates.selfie = new Date().getTime();
      }
      //  else {
      //   // AWS compare selfie with aadhar image
      //   const selfie = await this.commonSharedService.validateWithAadhareImage(
      //     userId,
      //     statusData,
      //   );
      //   // Update master data
      //   updatedData.status.selfie = selfie;
      //   updatedData.dates.selfie = new Date().getTime();
      // }
      if (ageCriteria?.type) {
        updatedData.status.aadhaar = -1;
        delete updatedData?.dates?.aadhaar;
      }
      updateResult = await this.masterRepo.updateRowData(updatedData, masterId);
      if (updateResult == k500Error) return kInternalError;

      // Checks user is not eligible as per age criteria then
      if (ageCriteria?.type) {
        var birthDate = new Date(mAadhaarResponse.basicDetails.dob);
        var targetDate = new Date(
          birthDate.getFullYear() + MIN_AGE,
          birthDate.getMonth(),
          birthDate.getDate(),
        );
        await this.adminService.changeBlacklistUser({
          userId,
          reasonId: ageCriteria?.type == '2' ? 70 : 71,
          reason: ageCriteria?.type == '2' ? kMinAgeCriteria : kMaxAgeCriteria,
          type: ageCriteria?.type,
          adminId: SYSTEM_ADMIN_ID,
          status: '1',
          nextApplyDate: targetDate,
        });

        return { needUserInfo: true };
      }

      /// check name validations
      const nameCheck = await this.eligibilitySharedService.nameValidation(
        userId,
      );
      if (nameCheck === false) return { needUserInfo: true };

      // if not old defaulter then Check eligibility
      // Check eligibility as per state salary
      if (!isRedProfile) {
        const eligibility =
          await this.eligibilitySharedService.validateStateWiseEligibility({
            userId,
          });
        if (eligibility.message) return eligibility;
      }
      const eligibility: any =
        await this.eligibilitySharedService.checkUserEligiblity(userId);
      if (eligibility.message) return eligibility;
      if (eligibility == false) return { needUserInfo: true };

      // CIBIL HIT AFTER KYC COMPLETE
      if (GLOBAL_FLOW.KYC_CIBIL_HIT && userData.isCibilConsent == 1) {
        const cibilResult = await this.cibilService.cibilPersonalLoanScore({
          userId,
          loanId: userData.kycId,
        });

        if (cibilResult.isLoanDeclined == true) {
          const adminId = SYSTEM_ADMIN_ID;
          let targetDate = new Date();
          targetDate.setDate(
            targetDate.getDate() + GLOBAL_RANGES.COOL_OFF_WITH_BAD_CIBIL_SCORE,
          );

          // Reject loan at this step
          await this.adminService.changeBlacklistUser({
            userId,
            adminId,
            type: '2',
            status: '0',
            reason: cibilResult?.reason ?? BAD_CIBIL_SCORE_MSG,
            reasonId: cibilResult?.reasonId ?? BAD_CIBIL_SCORE_REASON_ID,
            nextApplyDate: targetDate,
          });
        } else if (cibilResult.isLoanDeclined == false) {
          this.eligibilitySharedService.calculateLeadScore({ userId });
        }
      }

      const state = { isProcessing: false };
      return { needUserInfo: true, state };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Converting response into simple object
  private async fineTunemAadhaarResponse(internalResponse) {
    const aadhaarNumber = (internalResponse.responseData.uid ?? '').toString();
    delete internalResponse?.responseData?.uid;
    const fullResponse = internalResponse.responseData ?? '';
    let profileImage;
    if (fullResponse.residentPhoto)
      profileImage = await this.fileService.base64ToURL(
        fullResponse.residentPhoto,
        'jpg',
        CLOUD_FOLDER_PATH.aadhaar,
      );
    if (profileImage?.message) return profileImage;
    delete fullResponse?.residentPhoto;

    // Delete xmlStr due to compliance and storage
    if (fullResponse?.digiReresponse?.xmlStr)
      delete fullResponse.digiReresponse.xmlStr;
    const aadhaarResponse = JSON.stringify(fullResponse);
    const aadhaarAddress = {
      country: 'India',
      dist: fullResponse.districtName ?? '',
      state: fullResponse.stateName ?? '',
      po: fullResponse.poName ?? '',
      loc: fullResponse.locality ?? '',
      vtc: fullResponse.vtcName ?? '',
      subdist: fullResponse.subDistrictName ?? '',
      street: fullResponse.street ?? '',
      house: fullResponse.building ?? '',
      landmark: fullResponse.landmark ?? '',
    };
    const basicDetails = {
      name: fullResponse.name ?? '',
      dob: fullResponse.dob ?? '',
      aadhaarNumber,
      gender: (fullResponse.gender ?? 'MALE').toUpperCase(),
      mobile: fullResponse.mobile ?? '',
      profileImage,
    };
    return {
      basicDetails,
      aadhaarAddress,
      aadhaarResponse,
      aadhaarServiceMode: fullResponse.aadhaar_service,
    };
  }

  // Checks whether aadhaar number exists in another user or not
  private async isAadhaarExists(userId, aadhaarNumber) {
    try {
      const aadhaarNo = this.cryptService.getMD5Hash(aadhaarNumber);

      const queryString = `SELECT "userId", "aadhaarNo" FROM public."KYCEntities" WHERE "aadhaarNo" = '${aadhaarNo}' AND "userId" != '${userId}' LIMIT 1;`;
      const kycList = await this.repoManager.injectRawQuery(
        KYCEntity,
        queryString,
        { source: 'REPLICA' },
      );

      if (kycList == k500Error) return kInternalError;
      if (kycList.length > 0 && gIsPROD)
        return {
          ...k422ErrorMessage(
            'Aadhaar number already in use, try again with another aadhaar number',
          ),
          existingUserId: kycList[0]?.userId,
        };
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Get lat long of the aadhaar address
  private async getAadhaarCoordinates(aadhaarAddress) {
    try {
      const response = await this.typeService.getLocationFromAddress(
        JSON.stringify(aadhaarAddress),
      );
      if (response == k500Error) return;
      return JSON.stringify(response.results[0].geometry.location);
    } catch (error) {}
  }

  async createOrUpdateAddress(data: {
    userId: string;
    address: string;
    shipping?: string;
    type: string;
    status: string;
    probability?: { address?: string; probability?: number; type?: number };
    subType?: string;
  }) {
    try {
      const { userId, address, shipping, type, status, probability, subType } =
        data;
      const attributes = ['id', 'address'];
      const options = { where: { userId } };

      let addresses = await this.addressesRepo.getTableWhereData(
        attributes,
        options,
      );
      if (addresses === k500Error) addresses = [];
      const existingData = addresses?.find((addr) => addr.address === address);
      if (existingData) {
        const updateData = { status: '0', subType };
        await this.addressesRepo.updateRowData(updateData, existingData.id);
      } else {
        const creationData = {
          response: shipping ? JSON.stringify(shipping) : null,
          userId,
          address,
          type,
          status,
          subType,
        };

        if (probability) creationData['probability'] = probability;

        await this.addressesRepo.createRowData(creationData);
      }
    } catch (error) {}
  }

  // Convert aadhaar number into masked aadhaar number
  private getMaskedAadhaarNumber(aadhaarNumber) {
    return 'xxxx xxxx ' + aadhaarNumber.substring(8, 12);
  }

  async aadhaarOtpRequest(data) {
    // Validation -> Parameters
    const aadhaarNumber = data?.aadhaarNumber;
    const userId = data?.userId;
    const typeOfDevice = data?.typeOfDevice;
    const appType = data?.appType;
    if (!userId) return kParamMissing('userId');
    if (!aadhaarNumber) return kParamMissing('aadhaarNumber');

    // Check existing data of aadhaar number
    const existingData: any = await this.isAadhaarExists(userId, aadhaarNumber);

    if (existingData.message) return existingData;

    // this fun use for rekyc, delete account and old defuter
    const alreadyApproved = await this.checkUserAadhaarAlreadyApproved(
      userId,
      aadhaarNumber,
    );
    if (alreadyApproved?.message) return alreadyApproved;

    const userAttr = ['id', 'typeOfDevice', 'phone', 'hashPhone', 'isDeleted'];
    const userData = await this.userRepo.getRowWhereData(userAttr, {
      where: { id: userId },
    });
    if (userData === k500Error) throw new Error();

    // Check user's attempt for KYC
    const kycAttr = [
      'id',
      'attemptData',
      'aadhaarStatus',
      'maskedPan',
      'panCardNumber',
      'pan',
      'panStatus',
      'panResponse',
      'panRejectReason',
      'panUploadedAdmin',
      'panVerifiedAdmin',
      'createdAt',
      'hashPan',
    ];
    const kycData = await this.repository.getRowWhereData(kycAttr, {
      where: { userId },
      order: [['id', 'DESC']],
      limit: 1,
    });
    if (kycData === k500Error) throw new Error();
    else if (!kycData) return k422ErrorMessage(kNoDataFound);
    // User will go with zoop for re-attempt
    let isReAttempted = false;
    let attemptCount: 0;
    if (kycData) {
      const attemptData = kycData.attemptData ?? { count: 0 };
      attemptCount = attemptData.count ?? 0;
      if (attemptData.count >= 2) isReAttempted = true;
    }
    attemptCount++;

    // Create / Update record -> KYC table
    let newKycId = kycData?.id;
    if (kycData.id) {
      const createdData = {
        maskedPan: kycData?.maskedPan,
        hashPan: kycData?.hashPan,
        panCardNumber: kycData?.panCardNumber,
        pan: kycData?.pan,
        panStatus: kycData?.panStatus,
        panResponse: kycData?.panResponse,
        panRejectReason: kycData?.panRejectReason,
        panVerifiedAdmin: kycData?.panVerifiedAdmin,
        panUploadedAdmin: kycData?.panUploadedAdmin,
        userId,
        maskedAadhaar: this.getMaskedAadhaarNumber(aadhaarNumber),
        aadhaarNo: this.cryptService.getMD5Hash(aadhaarNumber),
        aadhaarNumber,
        attemptData: {
          count: attemptCount,
          lastAttemptOn: new Date().toJSON(),
        },
      };

      // Make today's date one year old
      const date = new Date();
      date.setFullYear(date.getFullYear() - 1);

      // New entry for re-kyc after one year
      if (kycData?.aadhaarStatus == 1 && kycData?.createdAt <= date) {
        const createResult = await this.repository.createRowData(createdData);

        newKycId = createResult?.id;
        const updateUser = await this.userRepo.updateRowData(
          { kycId: newKycId },
          userId,
        );
        if (createResult === k500Error || updateUser == k500Error)
          throw new Error();
      }
      //If user deletes account and comes
      else if (userData?.isDeleted == 0 || userData?.isDeleted == 2) {
        if (kycData?.aadhaarStatus == -1) {
          const updateData = await this.repository.updateRowData(
            createdData,
            kycData?.id,
          );
          if (updateData == k500Error) throw new Error();
        } else {
          const createResult = await this.repository.createRowData(createdData);

          newKycId = createResult?.id;
          const updateUser = await this.userRepo.updateRowData(
            { kycId: newKycId },
            userId,
          );
          if (createResult === k500Error || updateUser == k500Error)
            throw new Error();
        }
      }
      // If any error or service issue update the recenet entry for re-kyc
      else {
        const updateData = await this.repository.updateRowData(
          createdData,
          kycData?.id,
        );
        if (updateData == k500Error) throw new Error();
      }
    }

    let aadhaar_service: any = await this.commonSharedService.getServiceName(
      kAadhaarService,
    );
    let encAadhaar = await this.cryptService.encryptText(aadhaarNumber);

    ///need to update here
    // if (data.typeOfDevice === '2' || data?.reqFromWA) aadhaar_service = 'ZOOP';
    // Flow -> Digilocker
    let response: any;
    if (
      aadhaar_service == 'DIGILOCKER' ||
      aadhaar_service == 'DIGILOCKER_IN_HOUSE'
      // && !isReAttempted
    ) {
      const logTrackData = {
        phone: userData?.phone,
        hashPhone: userData?.hashPhone,
        result: null,
        requestData: JSON.stringify({ encAadhaar, userId }),
        type: 'DIGILOCKER_AADHAR_OTP',
      };
      const entry = await this.repoManager.createRowData(
        FetchDataEntity,
        logTrackData,
      );

      response = await this.digiLocker.invitation({
        aadhaarNumber,
        userId,
        aadhaar_service,
        typeOfDevice,
        appType,
        entry,
      });

      if (response) return response;
    } else if (aadhaar_service == 'ZOOP_DIGILOCKER' || isReAttempted) {
      response = await this.zoopService.getZoopDigiReqId();
      if (response === k500Error) throw new Error();
      if (response?.request_id) {
        const updateResult = await this.repoManager.updateRowData(
          KYCEntity,
          { customerReqId: response.request_id },
          newKycId,
        );
        if (updateResult === k500Error) throw new Error();
      }
      const intitalUrl = `https://gateway.zoop.one/digilocker/v1/start/${response.request_id}?mode=TAB`;

      const logTrackData = {
        phone: userData?.phone,
        hashPhone: userData?.hashPhone,
        result: null,
        requestData: JSON.stringify({ encAadhaar, userId }),
        type: 'ZOOP_DIGILOCKER_AADHAR_OTP',
      };
      const entry = await this.repoManager.createRowData(
        FetchDataEntity,
        logTrackData,
      );
      response = await this.digiLocker.invitation({
        aadhaarNumber,
        userId,
        aadhaar_service,
        intitalUrl,
        typeOfDevice,
        appType,
        entry,
        isReAttempted,
      });
      if (response) return response;
    } else if (aadhaar_service == 'DIGITAP') {
      const body = {
        redirectionUrl: EnvConfig.digiTap.redirectUrl,
        uniqueId: `${userId}${new Date().getTime()}`,
        userId,
      };
      response = await this.digiTapService.generateUrl(body);
      if (response?.message) return response;
      const model = response?.model;
      const transactionId = model?.unifiedTransactionId;
      const intitalUrl = model?.url;
      if (!intitalUrl || !transactionId)
        return k422ErrorMessage(kSomthinfWentWrong);
      const updateResult = await this.repoManager.updateRowData(
        KYCEntity,
        { customerReqId: transactionId },
        newKycId,
      );
      if (updateResult === k500Error) throw new Error();

      const logTrackData = {
        phone: userData?.phone,
        hashPhone: userData?.hashPhone,
        result: null,
        requestData: JSON.stringify({ encAadhaar, userId }),
        type: 'DIGITAP',
      };
      const entry = await this.repoManager.createRowData(
        FetchDataEntity,
        logTrackData,
      );
      response = await this.digiTapService.invitation({
        aadhaarNumber,
        userId,
        aadhaar_service,
        transactionId,
        intitalUrl,
        typeOfDevice,
        appType,
        entry,
      });
      if (response) return response;
    } else if (aadhaar_service == 'ZOOP') {
      const logTrackData = {
        phone: userData?.phone,
        hashPhone: userData?.hashPhone,
        result: null,
        requestData: JSON.stringify({ encAadhaar, userId }),
        type: 'ZOOP_AADHAR_OTP',
      };
      const entry = await this.repoManager.createRowData(
        FetchDataEntity,
        logTrackData,
      );

      response = await this.zoopService.checkAadhaarRequest(aadhaarNumber);

      if (entry?.id) {
        await this.repoManager.updateRowData(
          FetchDataEntity,
          { response: JSON.stringify(response) },
          entry?.id,
        );
      }
    }
    if (response?.message) return response;
    if (response?.success) {
      const reference_id = response?.request_id;
      const transaction_id = '';

      await this.repoManager.updateRowData(
        KYCEntity,
        { aadhaarReferenceId: reference_id },
        newKycId,
      );

      return { needBottomSheet: true, reference_id, transaction_id };
    } else return k422ErrorMessage(kErrorMsgs.SERVICE_UNAVAILABLE);
  }

  async validatePan(reqData, isBanking = false) {
    try {
      // Params validation
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      const isReAttempt = reqData.isReAttempt == true;
      const kycInclude: any = { model: KYCEntity };
      kycInclude.attributes = ['panCardNumber', 'panResponse', 'hashPan'];
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = ['dates', 'loanId', 'status'];
      const include = [kycInclude, masterInclude];
      const attributes = [
        'fullName',
        'isRedProfile',
        'kycId',
        'masterId',
        'appType',
        'phone',
        'hashPhone',
      ];
      const options = { include, where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (!userData) return k422ErrorMessage(kNoDataFound);
      if (userData == k500Error) return kInternalError;

      const appType = userData?.appType;
      let panNumber = userData.kycData?.panCardNumber;
      let hashPan = userData.kycData?.hashPan;
      let panResponse = userData.kycData?.panResponse ?? null;
      const statusData = userData.masterData?.status ?? {};
      const dates = userData.masterData?.dates ?? {};
      if (!hashPan) {
        const text = `*HASH PAN NOT FOUND*`;
        const threads = [`Body details -> ${JSON.stringify({ userData })}`];
        this.slackService.sendMsg({ text, threads });
        // return kInternalError;
      }
      if (statusData.pan == 1 || statusData.pan == 3)
        return { needUserInfo: true };

      // Temporarily stopping the fetching of PAN details from CIBIL to prevent incorrect PAN retrieval
      // const cibilData = await this.cibilScoreRepo.getRowWhereData(
      //   ['validCibilData', 'ids'],
      //   { where: { status: '1', type: '1', userId }, order: [['id', 'DESC']] },
      // );
      // if (cibilData == k500Error) return kInternalError;
      // if (
      //   Array.isArray(cibilData?.ids) &&
      //   cibilData?.validCibilData == 0 &&
      //   !isReAttempt &&
      //   !isBanking
      // ) {
      //   for (let i = 0; i < cibilData?.ids.length; i++) {
      //     const ele = cibilData?.ids[i];
      //     if (ele?.idType === '01') {
      //       panNumber = ele.idNumber;
      //       break;
      //     }
      //   }
      // }

      if (!panNumber) return k422ErrorMessage('Pan number not found');

      if (!userData.fullName && !isBanking)
        return k422ErrorMessage('User name not found');

      if (reqData?.pan === panNumber && isBanking) {
        // Update kyc data
        const updatedData: any = {
          panStatus: '1',
          panResponse: JSON.stringify({ source: reqData?.consentMode }),
          panVerifiedAdmin: SYSTEM_ADMIN_ID,
        };
        let updateResult = await this.repository.updateRowData(
          updatedData,
          userData.kycId,
        );
        if (updateResult === k500Error) return kInternalError;
        //delete kyc data from redis
        await this.redisService.del(`KYC_DATA_${userId}`);
        await this.redisService.del(`APP_USER_PROFILE_DATA_${userId}`);

        // Update master data
        statusData.pan = 1;
        dates.pan = new Date().getTime();
        const updatedMasterData = { status: statusData, dates };
        updateResult = await this.masterRepo.updateRowData(
          updatedMasterData,
          userData.masterId,
        );
        if (updateResult === k500Error) return kInternalError;
        return { needUserInfo: true, statusData, dates };
      }

      // -1 = Not Possible (Need to enter PAN first)
      // 0 = Not Possible (Goes directly for final approval)
      // 2 = Re-Upload PAN through App
      // 5 = Not verified by other source
      // 6 = Error from zoop (need to retry)
      if (!panResponse || ![-1, 0].includes(statusData.pan)) {
        const options = {
          where: {
            hashPan,
            type: 'ZOOP_PAN_VALIDATION',
          },
          useMaster: false,
        };

        const fetchPanData = await this.repoManager.getRowWhereData(
          FetchDataEntity,
          ['id', 'response', 'createdAt'],
          options,
        );
        if (fetchPanData === k500Error) return kInternalError;
        if (fetchPanData?.response)
          panResponse = JSON.parse(fetchPanData.response);

        const oneHourAgo = new Date(Date.now() - 1 * 60 * 60 * 1000);
        if (
          !panResponse ||
          (panResponse.message == 'No Record Found' &&
            fetchPanData.createdAt < oneHourAgo)
        ) {
          panResponse = await this.zoopService.checkPanCard(panNumber);
          const logTrackData = {
            phone: userData?.phone,
            hashPhone: userData?.hashPhone,
            result: null,
            hashPan,
            requestData: JSON.stringify({
              panNumber: hashPan,
              reqBody: { ...reqData, isBanking },
            }),
            response: JSON.stringify(panResponse),
            type: 'ZOOP_PAN_VALIDATION',
          };
          await this.repoManager.createRowData(FetchDataEntity, logTrackData);
        }
      } else panResponse = JSON.parse(panResponse);
      // Invalid pan number
      if (
        panResponse == k500Error ||
        panResponse.message == 'No Record Found'
      ) {
        // Update kyc data
        let updatedData: any = { panStatus: '6' };
        let updateResult = await this.repository.updateRowData(
          updatedData,
          userData.kycId,
        );
        if (updateResult == k500Error) return kInternalError;
        //delete kyc data from redis
        await this.redisService.del(`KYC_DATA_${userId}`);
        await this.redisService.del(`APP_USER_PROFILE_DATA_${userId}`);

        // Update master data
        statusData.pan = 6;
        updatedData = { status: statusData };
        updateResult = await this.masterRepo.updateRowData(
          updatedData,
          userData.masterId,
        );

        if (isReAttempt)
          return k422ErrorMessage('Please enter correct pan number');
      }
      // Validate response
      else {
        // Fine tune data from response
        const secondPanRes =
          panResponse?.document_status?.length > 0
            ? panResponse.document_status[0]
            : null;
        const resultData = panResponse.result ?? secondPanRes ?? panResponse;
        if (!resultData) return kInternalError;
        const extractedData = resultData.extracted_data ?? {};
        const validatedData = resultData.validated_data ?? {};
        const panName =
          resultData?.user_full_name ??
          extractedData.name ??
          extractedData.Name ??
          validatedData.full_name;

        let panStatus = 1;

        // Compare name as per aadhaar
        const isNameMissMatch: any = await this.validation.nameMatch(
          userData.fullName,
          panName,
          appType,
        );

        // Check if pan already exists and verified
        const isPanVerified: any = await this.isPanExists(userId, panNumber);
        if (isPanVerified?.message) return isPanVerified;
        if (isPanVerified == 6) panStatus = isPanVerified;

        const kycData: any = { panVerifiedAdmin: SYSTEM_ADMIN_ID };
        let isValidName: any = false;
        kycData.nameSimilarity = isNameMissMatch?.data ?? 0;
        if (isNameMissMatch?.valid) {
          if (isNameMissMatch.data >= NAME_MISS_MATCH_PER) isValidName = true;
          else isValidName = false;
        } else {
          isValidName = await this.validation.compareName(
            userData.fullName,
            panName,
          );
        }

        if (!isValidName && panStatus != 6) {
          const data = {
            type: 'KYC',
            userId,
            name1: userData.fullName,
            name2: panName,
          };
          await this.misMatchRepo.create(data);
          panStatus = 0;

          const text = `*PAN NAME MISMATCH*`;
          const body = {
            isNameMissMatch,
            isValidName,
            statusData,
            panStatus,
            data,
          };
          const threads = [`Body details -> ${JSON.stringify(body)}`];
          this.slackService.sendMsg({ text, threads });
        }
        kycData.panCardNumber = panNumber;
        kycData.maskedPan =
          panNumber.substring(0, 2) +
          'xxx' +
          panNumber[5] +
          'xx' +
          panNumber.substring(8, 10);
        kycData.hashPan = this.cryptService.getMD5Hash(panNumber);
        const response = JSON.stringify(panResponse);
        kycData.panResponse = response;
        kycData.panStatus = panStatus.toString();

        // Update kyc data
        let updateResult = await this.repository.updateRowData(
          kycData,
          userData.kycId,
        );
        if (updateResult == k500Error) return kInternalError;
        //delete kyc data from redis
        await this.redisService.del(`KYC_DATA_${userId}`);
        await this.redisService.del(`APP_USER_PROFILE_DATA_${userId}`);

        // Update master data
        statusData.pan = panStatus;
        const approved = [1, 3, 4];
        if (
          approved.includes(statusData.pan) &&
          approved.includes(statusData.selfie) &&
          [1, 3].includes(statusData.bank) &&
          approved.includes(statusData.workMail) &&
          approved.includes(statusData.salarySlip) &&
          userData?.masterData?.loanId
        ) {
          statusData.loan = 0;
          statusData.eligibility = 0;
        }
        const updateData = {
          status: statusData,
          dates: userData.masterData?.dates,
        };
        if (panStatus == 1) updateData.dates.pan = new Date().getTime();

        updateResult = await this.masterRepo.updateRowData(
          updateData,
          userData.masterId,
        );
        if (updateResult == k500Error) return kInternalError;

        /// check final approval when loan status is 0
        if (
          approved.includes(statusData.pan) &&
          statusData?.loan == 0 &&
          userData?.masterData?.loanId
        ) {
          const finalData = { loanId: userData.masterData.loanId, userId };
          await this.eligibilitySharedService.finalApproval(finalData);
        }
      }

      if (statusData.pan == 6 && isReAttempt) {
        await this.userService.routeDetails({ id: userId });
        return k422ErrorMessage('Please enter correct pan number');
      }

      return { statusData, dates, needUserInfo: true };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async aadhaarOtpVerify(data) {
    try {
      const userId = data?.userId;
      const reference_id = data?.reference_id;
      const transaction_id = data?.transaction_id;
      const aadhaar_number = data?.aadhaarNumber;
      const otp = data?.otp;

      // Testing purposes
      if (!gIsPROD && otp == '111111') return MockResponses.hardikUIDAISuccess;
      // Automation purpose
      if (userId) {
        const mockResponse =
          QAAutomationUserList[SERVER_MODE]?.[userId]?.[aadhaar_number]?.[otp]
            ?.UIDAISuccess;
        // return if userId MockResponse exists
        if (mockResponse) return mockResponse;
      }
      if (!reference_id || !otp || !aadhaar_number) return kParamMissing();
      // Normal flow
      let response;
      const aadhaar_service = await this.commonSharedService.getServiceName(
        kAadhaarService,
      );
      let aadhaarServiceMode = 'ZOOP';
      if (aadhaar_service == kSigndesk) {
        aadhaarServiceMode = kSigndesk;
        if (!transaction_id) return kParamMissing();
        const otpJson = { reference_id, transaction_id, otp: otp.toString() };
        response = await this.sigDeskservice.validateAadhaarOTP(otpJson);
        if (response?.message) return response;
      } else {
        response = await this.zoopService.verifyAadhaarOTP(reference_id, otp);
        if (response?.message) return response;
      }
      if (!response) return kInternalError;
      const prePareData = {
        status: 'Success',
        responseData: {
          state: '',
          valid: true,
          eid: '',
          informationSharingConsent: true,
          localResName: '',
          localCareof: '',
          localBuilding: '',
          email: '',
          dob: '',
          mobile: '',
          gender: 'MALE',
          landmark: null,
          street: '',
          locality: '',
          district: '',
          vtc: '',
          building: '',
          districtName: '',
          vtcName: '',
          stateName: '',
          poName: '',
          careof: '',
          poNameLocal: '',
          localVtc: '',
          localState: '',
          localDistrict: '',
          pincode: '',
          uid: aadhaar_number,
          localStreet: '',
          localLocality: '',
          localLandmark: null,
          refId: null,
          langCode: '',
          relationInfo: null,
          biometricFlag: false,
          dobStatus: '',
          enrolmentDate: '',
          enrolmentNumber: '',
          enrolmentType: '',
          exceptionPhoto: null,
          isCurrent: false,
          isNRI: 'false',
          isdCode: '+91',
          poType: null,
          poa: null,
          poi: null,
          residentPhoto: '',
          subDistrict: '',
          subDistrictLocalName: '',
          subDistrictName: '',
          updatedEIds: [],
          updatedEIdsCount: 0,
          updatedRefIds: [],
          updatedRefIdsCount: 0,
          name: '',
          aadhaar_service: aadhaarServiceMode,
        },
      };

      if (aadhaar_service == 'SIGNDESK') {
        prePareData.responseData.localResName = response?.full_name ?? '';
        prePareData.responseData.email = response?.email_hash ?? '';
        prePareData.responseData.dob = response?.dob ?? '';
        prePareData.responseData.mobile = response?.mobile_hash ?? '';
        const gender =
          (response?.gender ?? 'M').toLowerCase() == 'm' ? 'MALE' : 'FEMALE';
        prePareData.responseData.gender = gender;
        prePareData.responseData.building = response?.address?.house ?? '';
        prePareData.responseData.street = response?.address?.street ?? '';
        prePareData.responseData.locality = response?.address?.loc ?? '';
        prePareData.responseData.districtName = response?.address?.dist ?? '';
        prePareData.responseData.vtcName = response?.address?.vtc ?? '';
        prePareData.responseData.stateName = response?.address?.state ?? '';
        prePareData.responseData.poName = response?.address?.po ?? '';
        prePareData.responseData.careof = response?.care_of ?? '';
        prePareData.responseData.pincode = response?.zip;
        prePareData.responseData.residentPhoto = response?.profile_image ?? '';
        prePareData.responseData.subDistrictName =
          response?.address?.subdist ?? '';
        prePareData.responseData.name = response?.full_name;
      } else {
        const zoopData = response?.result;
        prePareData.responseData.localResName = zoopData?.user_full_name ?? '';
        let dob = zoopData?.user_dob;
        try {
          if (zoopData?.user_dob) {
            const date = this.typeService.dateTimeToDate(
              zoopData?.user_dob,
              'DD/MM/YYYY',
            );
            let month: any = date.getMonth();
            month += 1;
            if (month < 10) month = '0' + month;
            let day: any = date.getDate();
            if (day < 10) day = '0' + day;
            dob = date.getFullYear() + '-' + month + '-' + day;
          }
        } catch (error) {}
        prePareData.responseData.dob = dob ?? '';
        const gender =
          (zoopData?.user_gender ?? 'M').toLowerCase() == 'm'
            ? 'MALE'
            : 'FEMALE';
        prePareData.responseData.gender = gender;
        prePareData.responseData.building = zoopData?.user_address?.house ?? '';
        prePareData.responseData.street = zoopData?.user_address?.street ?? '';
        prePareData.responseData.locality = zoopData?.user_address?.loc ?? '';
        prePareData.responseData.districtName =
          zoopData?.user_address?.dist ?? '';
        prePareData.responseData.vtcName = zoopData?.user_address?.vtc ?? '';
        prePareData.responseData.stateName =
          zoopData?.user_address?.state ?? '';
        prePareData.responseData.subDistrictName =
          zoopData?.user_address?.subdist ?? '';
        prePareData.responseData.poName = zoopData?.user_address?.po ?? '';
        prePareData.responseData.careof = zoopData?.user_parent_name ?? '';
        prePareData.responseData.pincode = zoopData?.address_zip;
        prePareData.responseData.residentPhoto =
          zoopData?.user_profile_image ?? '';
        prePareData.responseData.name = zoopData?.user_full_name;
      }
      if (!prePareData?.responseData?.name) return k500Error;
      return JSON.stringify(prePareData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async submitPanDetails(reqData) {
    try {
      // Params validation
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      const fileURL = reqData.fileURL;
      if (!fileURL) return kParamMissing('fileURL');
      const panNumber = reqData.panNumber;
      if (!panNumber) return kParamMissing('panNumber');
      if (!regPanCard(panNumber))
        return k422ErrorMessage('Please and valid pan number');

      // Get user data
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = ['status'];
      const include = [masterInclude];
      const attributes = ['kycId', 'masterId'];
      const options = { include, where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);
      const statusData = userData.masterData?.status ?? {};
      if (statusData.pan != 6 && statusData.pan != 2 && statusData.pan != 5)
        return k422ErrorMessage('Pan submission failed');

      // Update kyc data
      const maskedPan =
        panNumber.substring(0, 2) +
        'xxx' +
        panNumber[5] +
        'xx' +
        panNumber.substring(8, 10);
      const hashPan = this.cryptService.getMD5Hash(panNumber);
      let updatedData: any = {
        panStatus: '5',
        maskedPan,
        pan: fileURL,
        panCardNumber: panNumber,
        hashPan,
      };
      const createdData = await this.repository.createRowDataWithCopy(
        updatedData,
        userData.kycId,
      );
      if (createdData == k500Error) return kInternalError;
      if (createdData) {
        const key = `${userId}_USER_PROFILE`;
        await this.redisService.del(key);
      }
      // Update user data
      updatedData = { kycId: createdData.id };
      let updatedResult = await this.userRepo.updateRowData(
        updatedData,
        userId,
      );
      if (updatedResult == k500Error) return kInternalError;
      // Update master data
      statusData.pan = 5;
      updatedData = { status: statusData };
      updatedResult = await this.masterRepo.updateRowData(
        updatedData,
        userData.masterId,
      );

      return await this.validatePan({ userId, isReAttempt: true });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Checks whether Pan number exists in another user or not
  private async isPanExists(userId, pan) {
    try {
      const hashPan = this.cryptService.getMD5Hash(pan);
      const options = {
        where: {
          hashPan,
          userId: { [Op.ne]: userId },
          panStatus: { [Op.or]: ['1', '3'] },
        },
      };
      const kycList = await this.repository.getTableWhereData(
        ['panCardNumber'],
        options,
      );
      if (kycList == k500Error) return kInternalError;

      // Pan exist validation
      for (let index = 0; index < kycList.length; index++) {
        try {
          const kycData = kycList[index];
          // Works in PRODUCTION and UAT except mock pan number
          if (
            gIsPROD ||
            (isUAT &&
              !EnvConfig.mock.panNumbers.includes(kycData?.panCardNumber))
          ) {
            if (kycData?.panCardNumber == pan) return 6;
          }
        } catch (error) {}
      }

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async changeAadhaarService(body, routeDetails) {
    const userData = routeDetails.userData;
    if (userData.currentStepTitle != kKYCInfo.title) return routeDetails;
    let aadhaar_service = await this.commonSharedService.getServiceName(
      'AADHAAR_SERVICE',
    );

    aadhaar_service =
      body.aadhaar_service == EnvConfig.nbfc.appName
        ? ZOOP
        : body.aadhaar_service == ZOOP
        ? SIGNDESK
        : EnvConfig.nbfc.appName;
    userData['aadhaar_service'] = aadhaar_service;
    if (aadhaar_service == EnvConfig.nbfc.appName) {
      userData.webviewData = getmAadhaarData(userData.id);
      return { userData, continueRoute: kWebviewRoute };
    } else {
      return { userData, continueRoute: kAddAadhareNumberRoute };
    }
  }

  async profileImage(reqData) {
    try {
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      const kycData = await this.commonSharedService.getKycData({
        userId,
        callFnFrom: '3',
      });
      if (kycData == k500Error) return kInternalError;
      if (!kycData?.profileImage) return k422ErrorMessage(kNoDataFound);

      return { aadharProfile: kycData.profileImage };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async validateStuckPanUsers() {
    try {
      const userInclude: { model; attributes?; where? } = {
        model: registeredUsers,
      };
      userInclude.attributes = ['id'];
      userInclude.where = { isBlacklist: { [Op.ne]: '1' } };
      const include = [userInclude];
      const attributes = ['userId'];
      const options = {
        include,
        limit: 10,
        where: {
          status: {
            contact: { [Op.in]: [1, 3, 4] },
            loan: 4,
            pan: 5,
            reference: { [Op.in]: [1, 3, 4] },
          },
        },
      };

      // Query
      const masterList = await this.masterRepo.getTableWhereData(
        attributes,
        options,
      );
      if (masterList == k500Error) return kInternalError;

      const userIds = [];
      for (let index = 0; index < masterList.length; index++) {
        try {
          const masterData = masterList[index];
          const userId = masterData.userId;
          await this.validatePan({ userId });
          userIds.push(userId);
        } catch (error) {}
      }

      return { userIds };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region check user aadhaar already approved or not
  private async checkUserAadhaarAlreadyApproved(userId, aadhaarNumber) {
    try {
      const result = await this.commonSharedService.getKycData({
        userId,
        callFnFrom: '4',
      });
      if (result === k500Error) return kInternalError;
      if (
        !result ||
        result?.aadhaarStatus != '1' ||
        (result?.aadhaarNo ?? '').trim() ===
          this.cryptService.getMD5Hash(aadhaarNumber)
      )
        return;
      return k422ErrorMessage(kEnterValidAadhaarNumber);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region migrate kyc address to lat log
  // if null aadhaarLatLong  it update in Kyc
  async updateLatLngkycData() {
    try {
      // find null and empty aadhaarLatLong
      const where = {
        aadhaarAddress: { [Op.ne]: null },
        [Op.or]: [
          { aadhaarLatLong: { [Op.eq]: null } },
          { aadhaarLatLong: { [Op.eq]: '' } },
        ],
      };
      const kycData = await this.repository.getTableWhereData(
        ['id', 'aadhaarLatLong', 'aadhaarAddress'],
        { where },
      );
      for (let i = 0; i < kycData.length; i++) {
        const element = kycData[i];
        const aadhaarAddress = element?.aadhaarAddress ?? '-';
        const addressLatLng = await this.getAadhaarCoordinates(
          JSON.parse(aadhaarAddress),
        );
        if (addressLatLng) {
          const latAndLng = { aadhaarLatLong: addressLatLng };
          // update aadhaarLatLong
          await this.repository.updateRowData(latAndLng, element.id, true);
        }
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async zoopDigiRepoPrep(reqData) {
    const kycOptions = {
      order: [['id', 'DESC']],
      where: { customerReqId: reqData.request_id },
    };
    const kycData = await this.repoManager.getRowWhereData(
      KYCEntity,
      ['maskedAadhaar', 'userId', 'id'],
      kycOptions,
    );
    if (kycData === k500Error) throw new Error();
    const aadhaarNumber: any = kycData.maskedAadhaar;

    const data: any = this.digiLocker.xmlToJsonObject(
      reqData.result[0].data_xml,
    );

    const rawJsonList = data.elements[0].elements[0].elements[0].elements[0];
    const aadhaarResponseList = rawJsonList.elements;

    const poiData = aadhaarResponseList.find(
      (el) => el.name == 'Poi',
    ).attributes;
    const poaData = aadhaarResponseList.find(
      (el) => el.name == 'Poa',
    ).attributes;
    const phtData = aadhaarResponseList.find((el) => el.name == 'Pht')
      .elements[0];
    // Validate aadhaar number with digilocker details
    const uidData = rawJsonList?.attributes?.uid;
    if (uidData) {
      const maskedUid = uidData.replace(/x/g, '').trim();
      if (!aadhaarNumber.includes(maskedUid)) {
        return k422ErrorMessage(
          'Aadhaar number does not match as per digilocker aadhaar details',
        );
      }
    }

    // Prepare dob
    const dobSpans = poiData.dob.split('-');
    const dob = dobSpans.reverse().join('-');

    //rawJsonList.attributes.uid
    const aadhaarResponse = {
      status: 'Success',
      responseData: {
        digiReresponse: { reqData, xmlStr: reqData.result[0].data_xml },
        state: '',
        valid: true,
        eid: '',
        informationSharingConsent: true,
        localResName: poiData.name,
        localCareof: '',
        localBuilding: '',
        email: '',
        dob,
        gender: poiData.gender[0] == 'M' ? 'MALE' : 'FEMALE',
        landmark: null,
        street: poaData.house ?? '',
        locality: '',
        district: '',
        vtc: '',
        building: '',
        districtName: poaData.dist ?? '',
        vtcName: poaData.vtc ?? '',
        stateName: poaData.state ?? '',
        poName: poaData.vtc ?? '',
        careof: '',
        poNameLocal: '',
        localVtc: '',
        localState: '',
        localDistrict: '',
        pincode: poaData.pc ?? '',
        uid: aadhaarNumber,
        localStreet: '',
        localLocality: '',
        localLandmark: null,
        refId: null,
        langCode: '',
        relationInfo: null,
        biometricFlag: false,
        dobStatus: '',
        enrolmentDate: '',
        enrolmentNumber: '',
        enrolmentType: '',
        exceptionPhoto: null,
        isCurrent: false,
        isNRI: 'false',
        isdCode: '+91',
        poType: null,
        poa: null,
        poi: null,
        residentPhoto: phtData.text,
        subDistrict: '',
        subDistrictLocalName: '',
        subDistrictName: '',
        updatedEIds: [],
        updatedEIdsCount: 0,
        updatedRefIds: [],
        updatedRefIdsCount: 0,
        name: poiData.name,
        aadhaar_service: 'ZOOP_DIGILOCKER',
      },
    };
    return await this.validatemAadhaar({
      aadhaarNumber,
      userId: kycData.userId,
      aadhaarResponse,
    });
  }
  async checkAdhaarStatus(reqData) {
    const userId = reqData.userId;
    const rawQuery = `SELECT kyc."userId", kyc.id, kyc."aadhaarStatus", "user".stage
    FROM public."KYCEntities" AS kyc
    INNER JOIN public."registeredUsers" AS "user" ON kyc."userId" = "user".id
    WHERE kyc."userId" = '${userId}';`;
    const data = await this.repoManager.injectRawQuery(KYCEntity, rawQuery);
    const count = await this.redisService.incrementKeyValue(
      `CHECK_AADHAARSTATUS_${userId}`,
      NUMBERS.ONE_DAY_IN_SECONDS,
    );
    if (count > 5) return { needUserInfo: true, userId: reqData.userId };
    if (data === k500Error) throw new Error();
    const state = { isProcessing: false };
    if (data[0].stage == 4)
      return { needUserInfo: true, state, userId: reqData.userId };
    if (data[0].aadhaarStatus == '1') {
      const state = { isProcessing: false };
      return { needUserInfo: true, state, userId: reqData.userId };
    } else {
      await this.typeService.delay(5000);
      return await this.checkAdhaarStatus(reqData);
    }
  }
  isMaskedAadhaar(aadhaar) {
    return aadhaar === 'xxxx xxxx ' + aadhaar.slice(-4);
  }

  async validateAadhaarKycInWA(reqData) {
    const { otp, aadhaarNumber, phoneNumber } = reqData;

    const phone = this.commonService.removePrefixFromMobile(
      phoneNumber.toString(),
    );
    const option = {
      where: {},
    };
    this.cryptService.getPhoneQuery(option.where, phone);
    const attr = ['id', 'phone', 'fullName'];

    const userData = await this.userRepo.getRowWhereData(attr, option);

    if (userData === k500Error) throw new Error();
    if (!userData || Object.keys(userData)?.length === 0) return false;

    const kycData = await this.commonSharedService.getKycData({
      userId: userData?.id,
      callFnFrom: '8',
    });
    if (kycData == k500Error) throw new Error();
    if (!kycData || Object.keys(kycData)?.length === 0) return false;

    const data = {
      aadhaarNumber,
      otp,
      userId: userData.id,
      reference_id: kycData.aadhaarReferenceId,
    };

    const res = await this.validatemAadhaar(data);
    console.log('res: valida', res);

    const masterAttr = ['id', 'status'];
    const masterOption = {
      where: {
        userId: userData.id,
      },
      order: [['id', 'DESC']],
    };
    const masterData = await this.masterRepo.getRowWhereData(
      masterAttr,
      masterOption,
    );
    if (masterData == k500Error) throw new Error();

    // Sync -> User details
    await this.userService.routeDetails({ id: userData.id });

    if (masterData.status.aadhaar == 1) return true;
    else return false;
  }

  async aadhaarOtpReqInWA(reqData) {
    try {
      const { aadhaarNumber, phoneNumber } = reqData;

      const phone = this.commonService.removePrefixFromMobile(
        phoneNumber.toString(),
      );

      const option = {
        where: {
          stage: UserStage.AADHAAR,
          stageStatus: -1,
        },
      };
      this.cryptService.getPhoneQuery(option.where, phone);

      const attr = ['id', 'phone', 'fullName'];

      const userData = await this.userRepo.getRowWhereData(attr, option);
      if (userData === k500Error) return false;
      if (!userData || Object.keys(userData)?.length === 0) return false;

      const data = {
        aadhaarNumber,
        userId: userData.id,
      };

      const res = await this.aadhaarOtpRequest(data);
      if (res.reference_id) {
        return true;
      }
      return false;
    } catch (err) {
      return false;
    }
  }

  async panToName(body) {
    const panList = body.pan;
    const length = panList.length;
    const finalData = [];
    for (let index = 0; index < length; index++) {
      try {
        const pan = panList[index];
        if (!pan) continue;
        console.log({ index, pan });
        const panData = await this.zoopService.checkPanCard(pan);
        if (panData == k500Error || panData.message == 'No Record Found')
          continue;
        const result = panData?.result;
        const panName = result?.user_full_name;
        const panStatus = result?.pan_status;
        if (!panName || panStatus != 'VALID') continue;
        console.log({ pan, panName });
        finalData.push({ pan, panName });
      } catch (error) {}
    }
    const rawExcelData = {
      sheets: ['panToPaneName'],
      data: [finalData],
      sheetName: 'panToPaneName.xlsx',
      needFindTuneKey: false,
    };
    const filePath = await this.fileService.objectToExcelURL(rawExcelData);
    console.log({ filePath });
    return filePath;
  }
}
