<!doctype html>
<html>
  <head>
    <meta http-equiv='Content-Type' content='text/html; charset=UTF-8' />
    <link rel='preconnect' href='https://fonts.googleapis.com' />
    <link rel='preconnect' href='https://fonts.gstatic.com' />
    <link
      href='https://fonts.googleapis.com/css2?family="Manrope"&family="Manrope"+Deca:wght@200&display=swap'
      rel='stylesheet'
    />
    <link href="https://fonts.googleapis.com/css2?family=Space+Mono&display=swap" rel="stylesheet">
    <style>
      body { font-family: "Manrope"; } .wrap-normal { word-wrap: normal; }
      .wrap-nowrap-cell { white-space: nowrap; } .statement-table {
  width: 100%;
  max-width: 800px;
  margin: auto;
  font-family: "Manrope";
  font-size: 12px;
  border-collapse: collapse;
  table-layout: fixed; /* key */
}


.statement-table th,
.statement-table td {
  padding: 6px 10px 6px 10px;
  border: 1px solid #ccc;
  text-align: left;
  vertical-align: top;
  word-wrap: break-word; /* for wrapping */
} th { padding: 10px; text-align: left; width:
      max-content; background-color: #f4f4f4; color: #666666; font-size: 12px;
      font-weight: normal; } td { padding: 10px; text-align: left; font-size:
      12px; } td > ul { list-style: none; padding-inline-start: 0px; } td ul ul
      { color: gray; } .custom-list { list-style-type: none; /* Remove default
      list styles */ padding-left: 0; } .disbursal-summary-table {
      border-collapse: collapse; width: 100%; max-width: 800px; margin: auto;
      font-size: 12px; font-family: "Manrope"; } /* Header row */
      .disbursal-summary-table thead th { padding: 10px; color: #000;
      font-weight: 600; font-size: 12px; border-top: 2px solid #f4f4f4;
      border-bottom: 2px solid #f4f4f4; background-color: transparent;
      text-align: left; /* default */ /* Special alignment overrides below */ }
      /* Right-align specific header columns by matching their text */
      .disbursal-summary-table thead th:nth-child(3), /* UTR */
      .disbursal-summary-table thead th:nth-child(4), /* Amount */
      .disbursal-summary-table thead th:nth-child(5) { /* Balance */ text-align:
      right; } /* Regular data cells */ .disbursal-summary-table td { padding:
      4px 10px; font-size: 12px; color: #000; border: none; text-align: left; /*
      default */ } /* UTR should be normal weight */ .disbursal-summary-table
      .utr-bold { font-weight: normal; } /* Right-align amount and balance data
      */ .disbursal-summary-table .right-align { text-align: right; } /* Total
      row styling */ .disbursal-summary-table .total-row td { background-color:
      #F4F4F4 !important; font-weight: 600;  } 
      .with-dot::before {
        content: "•"; /* Add bullet point */
        margin-right: 5px;
        margin-left: 5px;
        color: gray; /* Change color to match the text */
        font-size: 1.2em;
        /* Adjust size if needed */
      }
      
      /* Footer styling - removed fixed positioning */
      .footer-section {
        width: 100%;
        text-align: center;
        font-family: "Manrope";
        margin-top: 20px;
      }
     
.monospace {
   font-family: "Manrope" !important;
    font-size: 12px;
}

    </style>
  </head>
  <body
    style='
      margin: 0;
      padding: 0px 0 0 0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      border: 1px;
    '
  >
    <table
      style='
        max-width: 800px;
        width: 100%;
        margin: auto;
        border-collapse: collapse;
        font-family: "Manrope";
        font-size: 12px;
        margin-bottom:-10px;
        position:relative;
        z-index: 10;
        background-color: #FFF;'
    >
      <tbody>
        <tr>
          <td style="padding: 0 10px 10px 10px ;">
            <table
              style='
                max-width: 800px;
                width: 100%;
                margin: auto;
                border-collapse: collapse;
                font-family: "Manrope";
              '
            >
              <tbody>
                <tr>
                  <td style='height: 80px; padding-left: 20px'>
                    <div style='margin-top: 18px; margin-bottom: 8px'>
                      <img width='165px' height='40px' src='{{nbfcLogo}}' />
                    </div>
                  </td>
                  <td
                    style='
                      padding: 0 0px 0 0;
                      width: 400px;
                      padding-right: 20px;
                      text-align: right;
                    '
                  >
                    <div
                      style='
                        color: #666666;
                        font-size: 12px;
                        margin-bottom: 5px;
                      '
                    >
                      RBI NBFC Registered No.:
                      <span
                        style='font-weight: 600; color: #000000'
                      >{{rbiRegisterationNo}}</span>
                    </div>
                    <div style='color: #666666; font-size: 12px'>
                      CIN No.:
                      <span
                        style='font-weight: 600; color: #000000'
                      >{{cinNo}}</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr>
          <td style='background-color: #d8d8d8; height: 1px; padding: 0px'></td>
        </tr>
        <tr>
          <td
            style='
              background-color: #e02d42;
              width: 400px;
              justify-content: end;
              height: 2px;
              float: right;
              padding: 0px;
              margin-top: -2.5px;
            '
          ></td>
        </tr>
        <tr>
          <td>
            <table
              style='
                padding: 0px 10px 10px 0px;
                padding-left: 0;
                width: 100%;
                max-width: 800px;
                margin: auto;
              '
            >
              <tbody>
                <!-- sub header  -->
                <tr>
                  <td style='padding: 0px 10px 0px 0px'>
                    <span
                      style='   
                        font-weight: 600;
                        font-size: 16px;
                        letter-spacing: 0.3px;
                        vertical-align: middle;
                      '
                    >Account Statement of Loan ID:
                      <span class="monospace"
                        style='color: #e02d42; font-size: 14px;'
                      >{{basicDetails.id}}</span></span>
                  </td>
                  <td
                    style='
                      color: #666666;
                      font-size: 14px;
                      text-align: end;
                      margin: 5px 0 0 0;
                      padding: 0px 0px 0px 10px;
                    '
                  >
                  <span style="font-family: Manrope;">Generated on :</span>
                    <span class="monospace" style="font-size: 12px;">
                    {{generatedDate}}</span>
                  </td>
                </tr>
              </tbody>
            </table>
            <table class='statement-table' >
                <colgroup>
                    <col style="width: 20%">
                    <col style="width: 30%">
                    <col style="width: 20%">
                    <col style="width: 30%">
                  </colgroup>
                  <tr>
                    <th>A/C Name</th>
                    <td>{{basicDetails.name}}</td>
                    <th>First EMI Date</th>
                    <td class="monospace">{{firstEMIDate}}</td>
                  </tr>
                  <tr>
                    <th>Contact No.</th>
                    <td class="monospace">+91 {{basicDetails.phone}}</td>
                    
                {{#if hasEqualPI}}
              <th>EMI Amount(₹) & No. Of Installments </th>
              <td class="monospace">{{numberWithComma oneEmiAmount}} Per Month & {{emiCount}} EMIS</td>
           {{else}}
           <th>No. Of Installments </th>
          <td class="monospace">{{emiCount}} EMIS</td>
              {{/if}}

                  </tr>
                  <tr>
                    <th>Email</th>
                    <td>{{basicDetails.email}}</td>
                    <th>Loan Amount To Borrower (₹)</th>
                    <td class="monospace"> {{basicDetails.loanAmount}}</td>
                  </tr>
                  <tr>
                    <th>Product</th>
                    <td>{{basicDetails.productDescription}}</td>
                    <th>Disbursal Amount (₹)</th>
                    <td class="monospace"> {{basicDetails.disbursedAmount}}</td>
                  </tr>
                  <tr>
                    <th >Tenure (Days)</th>
                    <td class="monospace">{{basicDetails.tenure}}</td>
                    <th>Charges (₹)</th>
                    <td class="monospace"> {{basicDetails.loanCharges}}</td>
                  </tr>
                  <tr>
                    <th>Interest Rate</th>
                    <td class="monospace">{{basicDetails.interestRate}} <span style="font-family: Manrope;">P.A.</span></td>
                    <th>Scheduled Repayment (₹)</th>
                    <td class="monospace"> {{numberWithComma  basicDetails.emiAmount}}</td>
                  </tr>
                  <tr>
                    <th>Disbursement Date</th>
                    <td class="monospace">{{basicDetails.accountOpen}}</td>
                    <th>Installments Overdue</th>
                    <td  class="monospace">{{basicDetails.overdue}}</td>
                  </tr>
                  <tr>
                    <th>Loan Status</th>
                    <td>
                      {{basicDetails.loanDueStatus}}
                    </td>
                    <th>Overdue Amount (₹)</th>
                    <td class="monospace"> {{numberWithComma overdueAmount}}</td>
                  </tr>
                  <tr>
                    <th rowspan='2'>Address</th>
                    <td rowspan='2'>{{basicDetails.address}}</td>
                    <th>UnAccounted Charges (₹)</th>
                    <td class="monospace"> {{numberWithComma overdueCharge}}</td>
                  </tr>
                  <tr>
                    <th>Total Outstanding (₹)</th>
                    <td class="monospace"> {{numberWithComma totalRemaining}}</td>
                  </tr>
                  
            </table>

            <table
              style='
                background-color: #ffffff;
                margin: auto;
                width: 100%;
                max-width: 800px;
                font-family: "Manrope";
              '
            >
              <tr>
                <td colspan='2' style='text-align: start; padding: 10px 0px'>
                  <div
                    style='
                      font-weight: 600;
                      font-size: 16px;
                      letter-spacing: 0.3px;
                    '
                  >
                    Disbursal Summary
                  </div>
                </td>
              </tr>
            </table>

            <table
              class='disbursal-summary-table'
              style='padding-bottom: 100px'
            >
              <thead>
                <tr>
                  {{#each ledgerDisbFields}}
                    <th>{{this}}</th>
                  {{/each}}
                </tr>
              </thead>
              <tbody>
                {{#each disDetails as |disData index|}}
                  {{#each disData.Particulars as |item i|}}
                    {{#if disData.Title}}
                      {{#if (eq i 0)}}
                        <tr>
                          <td></td>
                          <td><b>{{disData.Title}}</b></td>
                          <td></td>
                          <td></td>
                          <td></td>
                        </tr>
                      {{/if}}
                    {{/if}}               
<tr>
                      {{#if (eq i 0)}}
                        <td class="monospace" 
                          rowspan='{{disData.Particulars.length}}'
                        >{{disData.Date}}</td>
                      {{/if}}
                      <td >{{item}}</td>
                      <td class='right-align monospace' >  {{numberWithComma
                          (lookup disData.Debit i)
                        }}</td>
                      <td class='right-align monospace'>{{lookup disData.Credit i}}</td>
                      {{#if (eq i 0)}}
                        {{!-- <td
                          rowspan='{{disData.Particulars.length}}'
                          class='right-align monospace' style="font-size: 10px;"
                        >{{disData.Balance}}</td> --}}
                      {{/if}}
                    </tr>
                  {{/each}}
                  {{#if (eq disData.Particulars.length 0)}}
                    <tr class="total-row"   style="background-color: #f4f4f4; font-weight: 600;">
                      <td  style="font-size: 12px;">{{disData.Date}}</td>
                      <td></td>
                      <td class='right-align monospace' ">{{disData.Debit}}</td>
                      <td class='right-align monospace' ">{{disData.Credit}}</td>
                      {{!-- <td class='right-align monospace' style="font-size: 10px;">{{disData.Balance}}</td> --}}
                    </tr>
                  {{/if}}
                {{/each}}
              </tbody>
            </table>

 {{#if (gt repayDetails.length 0)}}
            <table
            
              style='
              background-color: #ffffff;
              margin: auto;
              width: 100%;
              max-width: 800px;
              font-family: "Manrope";
            '
            >
              <tr>
                <td colspan='2' style='text-align: start; padding: 10px 0px 0px 0px'>
                  <div
                    style='
                      font-weight: 600;
                      font-size: 16px;
                      letter-spacing: 0.3px;
                    '
                  >
                  <span style="font-family: Manrope;"> Statement for the period of</span> 
                    <span class="monospace" style='font-weight: 600; font-size: 14px; color: #000000'>
                      {{startDate}}</span>
                    to
                    <span class="monospace" style='font-weight: 600; font-size: 14px; color: #000000'>
                      {{endDate}}</span>
                  </div>
                </td>
              </tr>
            </table>
 {{/if}}
   {{#if (gt repayDetails.length 0)}}
            <table
              class='disbursal-summary-table'
              style='padding-bottom: 100px; margin-top: 10px;'
            >
              <thead>
                <tr>
                  {{#each ledgerFields}}
                    <th>{{this}}</th>
                  {{/each}}
                </tr>
              </thead>
              <tbody>
                {{#each repayDetails as |disData index|}}
                  {{#each disData.Particulars as |item i|}}
                    {{#if disData.Title}}
                      {{#if (eq i 0)}}
                        <tr>
                          <td class="monospace" >{{disData.Date}}</td>
                          <td><b>{{disData.Title}}</b><Span
                            >{{disData.UTR}}</Span></td>
                          <td></td>
                          <td></td>
                          <td></td>
                        </tr>
                      {{/if}}
                    {{/if}}

                    <tr>
                      {{#if (eq i 0)}}
                        <td
                          rowspan='{{disData.Particulars.length}}'
                        ></td>
                      {{/if}}
                      <td class="with-dot" style=" padding:0 10px">{{item}}</td>
                      <td class='right-align monospace'>
                        {{#if (gt (lookup disData.Debit i) 0)}}
                           {{numberWithComma (lookup disData.Debit i)}}
                        {{else}}
                          {{numberWithComma (lookup disData.Debit i)}}
                        {{/if}}
                      </td>
                      <td class='right-align monospace'>
                        {{#if (gt (lookup disData.Credit i) 0)}}
                           {{numberWithComma (lookup disData.Credit i)}}
                        {{else}}
                          {{numberWithComma (lookup disData.Credit i)}}
                        {{/if}}
                      </td>

                      {{#if (eq i 0)}}
                        <td
                          rowspan='{{disData.Particulars.length}}'
                          class='right-align monospace' 
                        >{{disData.Balance}}</td>
                      {{/if}}
                    </tr>

                  {{/each}}
                 {{#if (eq disData.Particulars.length 0)}}
  <tr
    {{#if (eq disData.isLastPage 0)}}
      class="total-row"
      style="background-color: #f4f4f4; font-weight: 600;"
    {{/if}}
  >
   {{#if disData.Title }}
    <td class="monospace" > {{disData.Date}}</td>
   {{else}}
    <td  style="font-size: 12px;"> {{disData.Date}}</td>
    {{/if}}
    <td>
      <b>{{disData.Title}}</b>
      <span>{{disData.UTR}}</span>
    </td>
    <td class='right-align monospace' >{{disData.Debit}}</td>
    <td class='right-align monospace'>{{ disData.Credit}}</td>
    <td class='right-align monospace' >{{disData.Balance}}</td>
  </tr>
{{/if}}
                {{/each}}
              </tbody>
            </table>
              {{/if}}

          {{#if (gt repayDetails.length 0)}}
          {{#if (gt gstTotal 0)}}
            {{#if hasIGST}}
            <div style="padding-top: 10px ; font-weight: 600;">
              Total GST 18% (<span class="monospace"><span style='font-family: "Manrope"; '>₹</span>{{numberWithComma gstTotal}}</span>) =
              IGST 18% (<span class="monospace"> <span style='font-family: "Manrope";'>₹</span>{{numberWithComma igst}}</span>)
            </div>
            {{else}}
            <div style="padding-top: 10px ; font-weight: 600;">
              Total GST 18% (<span class="monospace"><span style='font-family: "Manrope"; '>₹</span>{{numberWithComma gstTotal}}</span>) =
              CGST 9% (<span class="monospace"><span style='font-family: "Manrope"; '>₹</span>{{numberWithComma cgst}}</span>) +
              SGST 9% (<span class="monospace"><span style='font-family: "Manrope"; '>₹</span>{{numberWithComma sgst}}</span>)
            </div>
            {{/if}}
            {{/if}}
            {{/if}}
          </td>
        </tr>
      </tbody>
    </table>
    
    <!-- Notes Section -->
    <table
      style='
    max-width: 800px;
        width: 100%;
        margin: auto;
        border-collapse: collapse;
        font-family: "Manrope";
        font-size: 12px;
       margin-bottom: -30px
        position:relative;
        z-index: 10;
        background-color: #FFF;'
    >
      <tr>
        <td style="padding: 10px 10px 25px 10px;">
          <b>Note:</b>
          <ol style='padding-left: 18px; margin-top: 10px'>
            <li> GST on charges is accounted for on a receipt basis.</li>
            <li>
              The statement outlines the charges due every month on the EMI
              date. However, charges are accruing daily, due to which the amount
              of charges may differ while making the payments.
            </li>
            <li>
              <b>
                Penal Charges (Only applicable on the past due EMI principal
                amount)</b>
              <ul style='margin: 5px 0 0 -24px; list-style: disc'>
                <li><b>Days Past Due 1 to 3 Days:</b>
                  If repayment of your EMI gets delay by 1 to 3 days you will be
                  charged 5% on your delayed EMI principal amount.</li>
                <li><b>Days Past Due 4 to 14 Days:</b>
                  If repayment of your EMI gets delay by 4 to 14 days you will
                  be charged 10% on your delayed EMI principal amount</li>
                <li><b>Days Past Due 15 to 30 Days:</b>
                  If repayment of your EMI gets delay by 15 to 30 days you
                  will be charged 15% on your delayed EMI principal amount</li>
                <li><b>Days Past Due 31 to 60 Days:</b>
                  If repayment of your EMI gets delay by 31 to 60 days you will
                  be charged 20% on your delayed EMI principal amount.
                </li>
                <li><b>Days Past Due 60+ Days:</b>
                  If repayment of your EMI gets delay by 60+ days you will be
                  charged 25% onn your delayed EMI principal amount</li>
              </ul>
            </li>
            <li>
              <b>A Deferred interest of {{basicDetails.interestRate}} per annum,</b>
              will be levied for non-payment, delayed payment, breach,
              violation, or non-compliance with any terms and conditions of the
              sanction on the entire outstanding amount for the overdue period.

            </li>
            <li>
              <b> NACH e-Mandate bounce charges:</b>
              ₹500
            </li>
            <li>
              <b> Legal Charges:</b>
              In the event where the Borrower fails to make any of the monthly
              installment payments (EMI) on or before the due date as specified
              in the payment schedule of this Agreement, the Borrower shall be
              in default of this Agreement. Upon such default, and in addition
              to any other rights and remedies available to the Lender under
              this Agreement or under applicable law, the Lender shall have the
              right to levy and add ₹ 5000/- +GST as legal charges incurred to
              recover the overdue amount to the total outstanding balance owed
              by the Borrower. These legal charges include, but are not limited
              to. attorney fees, court costs, and other related expenses. The
              added legal charges shall become immediately due and payable, and
              shall be considered part of the total outstanding debt owed by the
              Borrower under the terms of this Agreement.
            </li>
           
            <li>
              <b> Foreclosure charges, if applicable: </b>
              {{foreclosureCharge}}% of outstanding Principal Amount + GST as applicable
            </li>
          </ol>
        </td>
      </tr>
    </table>

    <!-- Footer Section - Now only appears after the notes table -->
    {{!-- <div class="footer-section">
      <table
        style='
          max-width: 800px;
          margin: 0 auto;
          width: 100%;
          font-family: "Manrope";
          bottom: 0;
          position: fixed;
          z-index: 1;
        '
      >
        <tr>
          <td
            style='
              color: #2f1518;
              width: 100%;
              padding: 10px 0;
              background-color: #ffffff;
              text-align: center;
              font-size: 12px;
            '
          >
            Contact No:
            {{helpContact}}
            <span style='padding: 0 5px'>/</span>
            Email:
            {{infoEmail}}
          </td>
        </tr>
        <tr>
          <td
            style='
              color: #ffffff;
              width: 100%;
              padding: 10px 0;
              background-color: #2f1518;
              text-align: center;
              font-size: 12px;
            '
          >
            {{nbfcAddress}}
          </td>
        </tr>
      </table>
    </div> --}}
  </body>
  </html>
