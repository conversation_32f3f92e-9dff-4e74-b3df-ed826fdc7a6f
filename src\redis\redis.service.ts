// Imports
import { createClient } from 'redis';
import { RedisModule } from './redis.module';
import { REDIS_PREFIX } from 'src/constants/globals';
import { TypeService } from 'src/utils/type.service';
import { kParamMissing } from 'src/constants/responses';
import { Inject, Injectable, Logger } from '@nestjs/common';

@Injectable()
export class RedisService {
  private readonly logger = new Logger(RedisModule.name);
  private client: any;
  public prefix: string = REDIS_PREFIX ? REDIS_PREFIX : '';
  constructor(
    @Inject('REDIS_CONNECTION_CONFIG_OPTIONS') private options,
    private readonly typeService: TypeService,
  ) {
    this.initialize(options);
  }

  async initialize(options) {
    this.client = createClient({
      url: `redis://${
        options.auth_user == undefined ? '' : options.auth_user
      }:${options.auth_pass}@${options.host}:${options.port}`,
    });
    this.client.on('error', (err) => this.logger.error('Redis -> ' + err));
    this.client.on('ready', () => this.logger.log('Redis client is connected'));
    this.client.connect();
  }

  async del(key) {
    try {
      return this.client.del(this.prefix + key);
    } catch (error) {
      return null;
    }
  }

  // GET retrieves a string value.
  async get(key: string) {
    try {
      return await this.client.get(this.prefix + key);
    } catch (error) {
      return null;
    }
  }

  // SET stores a string value.
  async set(key, value, time = -1) {
    try {
      await this.client.set(this.prefix + key, value);
      if (time == -1) return true;
      await this.client.expire(this.prefix + key, time);
      return true;
    } catch (error) {
      return null;
    }
  }

  // HGETALL get all fields on a hash.
  async hGetAll(key: string) {
    try {
      return await this.client.hGetAll(this.prefix + key);
    } catch (error) {
      return null;
    }
  }

  // HSET sets the value of one or more fields on a hash.
  // eslint-disable-next-line @typescript-eslint/ban-types
  async hSet(key, values: Object, time = -1): Promise<any> {
    try {
      await this.client.hSet(this.prefix + key, values);
      if (time == -1) return true;
      await this.client.expire(this.prefix + key, time);
      return true;
    } catch (error) {
      return null;
    }
  }

  async mGet(...keys) {
    try {
      return this.client.mGet(keys.map((key) => this.prefix + key).join(','));
    } catch (error) {
      return null;
    }
  }

  // Returns the details as per environment mode
  async getKeyDetails(key: string) {
    return await this.get(key);
  }

  // Update the details as per environment mode
  async updateKeyDetails(key: string, data: any, time?: number) {
    return await this.set(key, data, time);
  }

  //#region
  async redisKeyLists(reqData) {
    const keys = reqData?.keys;
    if (!keys) return kParamMissing('keys');
    const filter = `*${this.prefix + keys}*`;
    return await this.client.sendCommand(['KEYS', filter]); // 'OK'
  }
  //#endregion

  //#region
  async redisKeyDelete(reqData) {
    const searchKey = reqData.searchKey;
    if (!searchKey) return kParamMissing('searchKey');
    const filter = `*${this.prefix + searchKey}*`;
    let keysList = await this.client.sendCommand(['KEYS', filter]);
    if (keysList?.length)
      return await this.client.sendCommand(['DEL', ...keysList]);
    return 0;
  }
  //#endregion

  //#region
  async redisDeleteByKey(key) {
    if (!key) return kParamMissing('key');
    return await this.client.del(this.prefix + key);
  }
  //#endregion

  //#region
  async redisKeyValue(reqData) {
    const key = reqData?.key;
    if (!key) return kParamMissing('key');
    let value = await this.client.sendCommand(['GET', this.prefix + key]);
    if (typeof value != 'string') {
      value = JSON.parse(value);
    }
    return value;
  }
  //#endregion

  async setRedisKeyValue(reqData) {
    let key = reqData?.key;
    if (!key) return kParamMissing('key');
    let newValue = reqData?.newValue;
    if (!newValue) return kParamMissing('newValue');
    const jsonValue =
      typeof newValue === 'string' ? newValue : JSON.stringify(newValue);
    return await this.set(key, jsonValue);
  }

  async incrementValue(reqData: {
    durationType?: string;
    key: string;
    refId: string;
  }) {
    let key = reqData.key;
    if (!key) return kParamMissing('key');
    const refId = reqData.refId;
    if (!refId) return kParamMissing('refId');
    if (reqData.durationType) {
      if (reqData.durationType == 'DATE') {
        const today_date = this.typeService
          .getGlobalDate(new Date())
          .toJSON()
          .substring(0, 10);
        key = `${key}_${today_date}`;
      } else if (reqData.durationType == 'MONTH') {
        const current_month = this.typeService
          .getGlobalDate(new Date())
          .toJSON()
          .substring(0, 7);
        key = `${key}_${current_month}`;
      }
    }

    const targetRefKey = this.prefix + `${key}_${refId}`;
    const isInserted = await this.client.setNX(targetRefKey, 'Exists !');
    let incrementedValue = null;
    if (isInserted === true) {
      incrementedValue = await this.client.incr(this.prefix + key);
      // Expire the reference key once the incremnet operation is over ( Default is 1 day )
      await this.client.expire(targetRefKey, 84000);
    }

    return {
      incrementedValue,
      success: isInserted ? true : false,
      message: isInserted
        ? 'Incremented successfully !'
        : 'Duplicate attempt found !',
    };
  }

  async incrementKeyValue(key: string, expireTime?: number) {
    if (!key) return kParamMissing('key');
    const incrementedValue = await this.client.incr(this.prefix + key);
    if (incrementedValue == 1 && expireTime)
      await this.client.expire(this.prefix + key, expireTime);
    return incrementedValue;
  }

  async setIfNotExistsWithNX(key, expireTime?: number) {
    const targetRefKey = this.prefix + `${key}`;
    const isSet = await this.client.setNX(targetRefKey, 'Exists !');
    if (isSet && expireTime) await this.client.expire(targetRefKey, expireTime);
    return isSet;
  }

  async getSortedSetData(key: string): Promise<string[]> {
    const target_key = `${this.prefix}${key}`;

    return await this.client.zRange(target_key, 0, -1);
  }

  async updateSortedSetMember(key: string, value: string, ttl?): Promise<void> {
    const target_key = `${this.prefix}${key}`;

    // Track the TTL in a sorted set
    if (ttl) {
      const now = Date.now();
      const expiryTime = now + ttl * 1000; // Convert TTL to a timestamp
      await this.client.zAdd(target_key, {
        score: expiryTime,
        value,
      });

      await this.removeExpiredSortedMember(key);
    }
  }

  async removeExpiredSortedMember(key: string): Promise<void> {
    const target_key = `${this.prefix}${key}`;
    const now = Date.now();

    const expiredFields = await this.client.zRangeByScore(target_key, 0, now);
    if (expiredFields.length > 0) {
      await this.client.zRemRangeByScore(target_key, 0, now); // Remove expired fields from the sorted set
    }
  }

  async incrSortedSetMember(
    key: string,
    member: string,
    incrementBy: number = 1,
  ): Promise<number> {
    return await this.client.zIncrBy(this.prefix + key, incrementBy, member);
  }

  async setAdd(key: string, members: string[]): Promise<number> {
    return await this.client.sAdd(this.prefix + key, members);
  }

  async setRem(key: string, members: string[]): Promise<number> {
    return await this.client.sRem(this.prefix + key, members);
  }

  async setIsMember(key: string, members: string): Promise<boolean> {
    return await this.client.sIsMember(this.prefix + key, members);
  }

  async setGetMembers(key: string): Promise<string[]> {
    return await this.client.sMembers(this.prefix + key);
  }
}
