// Imports
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import {
  CAMS_FIU_ID,
  CAMS_REDIRECTION_KEY,
  CAMS_USER_ID,
} from 'src/constants/globals';
import { v4 as uuidv4 } from 'uuid';
import { k500Error } from 'src/constants/misc';
import {
  aaURL,
  kCAMSConsentStatus,
  kCamsDownloadData,
  kCAMSGetAuthToken,
  kCAMSGetConsentData,
  kCAMSGetConsentDetails,
  kCAMSInvitation,
  kManualFetch,
  nSyncAuthAiTransactions,
} from 'src/constants/network';
import {
  k422ErrorMessage,
  kCAMSDataNotFound,
  kInternalError,
  kParamMissing,
  kUserAndDevError,
} from 'src/constants/responses';
import { APIService } from 'src/utils/api.service';
import { TypeService } from 'src/utils/type.service';
import { kCAMS, kGlobalTrail, kNoDataFound } from 'src/constants/strings';
import { ResponseRepository } from 'src/repositories/response.repository';
import { FileService } from 'src/utils/file.service';
import { CLOUD_FOLDER_PATH, kBankingProHeaders } from 'src/constants/objects';
import { BankingServiceV4 } from 'src/v4/banking/banking.service.v4';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import { BankingRepository } from 'src/repositories/banking.repository';
import { employmentDetails } from 'src/entities/employment.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { EnvConfig } from 'src/configs/env.config';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { PeriodicEntity } from 'src/entities/periodic.entity';
import { FinancialSummaryServiceV4 } from 'src/v4/banking/financialSummary.service.v4';
import { UserRepository } from 'src/repositories/user.repository';
import { Op } from 'sequelize';
import { RedisService } from 'src/redis/redis.service';
import { NUMBERS } from 'src/constants/numbers';
import { BankingEntity } from 'src/entities/banking.entity';
import { collectionDashboardService } from 'src/admin/collectionDashboard/collectionDashboard.service';
import { CryptService } from 'src/utils/crypt.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { SlackService } from '../slack/slack.service';

@Injectable()
export class CamsServiceThirdParty {
  constructor(
    // Admin
    // Repositories
    private readonly bankingRepo: BankingRepository,
    private readonly responseRepo: ResponseRepository,
    // Shared
    private readonly sharedNotification: SharedNotificationService,
    // Utils
    private readonly apiService: APIService,
    private readonly fileService: FileService,
    private readonly typeService: TypeService,
    @Inject(forwardRef(() => BankingServiceV4))
    private readonly bankingService: BankingServiceV4,
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userService: UserServiceV4,
    private readonly repoManger: RepositoryManager,
    private readonly financialSummaryService: FinancialSummaryServiceV4,
    private readonly userRepo: UserRepository,
    private readonly redisService: RedisService,
    private readonly repoManager: RepositoryManager,
    private readonly collectionService: collectionDashboardService,
    private readonly cryptService: CryptService,
    private readonly errorContextService: ErrorContextService,
    private readonly slackService: SlackService,
  ) {}

  async getAuthToken() {
    const url = kCAMSGetAuthToken;
    const body = {
      fiuID: CAMS_FIU_ID,
      redirection_key: CAMS_REDIRECTION_KEY,
      userId: CAMS_USER_ID,
    };
    const response = await this.apiService.post(url, body);
    if (response == k500Error) throw new Error();
    return {
      sessionId: response.sessionId,
      token: 'Bearer ' + response.token,
    };
  }

  async inviteForAA(phoneNumber, bankName) {
    const sessionData: any = await this.getAuthToken();
    if (sessionData['message']) return sessionData;

    const url = kCAMSInvitation;
    const body = {
      clienttrnxid: this.getUUID(),
      fiuID: CAMS_FIU_ID,
      userId: CAMS_USER_ID,
      aaCustomerHandleId: phoneNumber + `@CAMSAA`,
      aaCustomerMobile: phoneNumber,
      sessionId: sessionData.sessionId,
      useCaseid: '7',
      addfip: 'False',
      fipid: bankName,
      redirect: aaURL,
    };

    const response = await this.apiService.post(url, body, null, null, {
      headers: { Authorization: sessionData.token },
    });

    // Setting Client Transaction Id in Redis
    if (response.clienttxnid)
      await this.redisService.set(
        response.clienttxnid,
        'BANKING',
        NUMBERS.ONE_HOURS_IN_SECONDS,
      );

    if (response == k500Error) return kInternalError;
    return {
      consentTxnId: response.clienttxnid,
      url: response.redirectionurl,
      consentHandle: response.consentHandle,
    };
  }

  //#region inviteForAaForFinSummary
  async inviteForAaForFinSummary(reqData) {
    // Params validation
    const phoneNumber = reqData?.decryptedPhone;
    const bankName = reqData?.bankCode;
    const sessionData: any = await this.getAuthToken();
    if (sessionData['message']) return sessionData;
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    const bankCode = reqData.bankCode;
    if (!bankCode) return kParamMissing('bankCode');

    const url = kCAMSInvitation;
    const body = {
      clienttrnxid: this.getUUID(),
      fiuID: CAMS_FIU_ID,
      userId: CAMS_USER_ID,
      aaCustomerHandleId: phoneNumber + `@CAMSAA`,
      aaCustomerMobile: phoneNumber,
      sessionId: sessionData.sessionId,
      useCaseid: '7',
      addfip: 'False',
      fipid: reqData.fipName,
      redirect: aaURL,
    };

    const response = await this.apiService.post(url, body, null, null, {
      headers: { Authorization: sessionData.token },
    });
    if (response == k500Error)
      return k422ErrorMessage(
        'Sorry, We are enable to fetch your balance. Please try after sometime.',
      );

    // Create Entry in Periodic Entity for Tracking
    const trackingEntry = await this.repoManger.createRowData(PeriodicEntity, {
      sessionId: response.sessionId,
      type: 6,
      userId,
      source: 2,
      data: {
        bankCode,
        purpose: 'FINANCIAL_SUMMARY',
        response,
        consentId: response.clienttxnid,
      },
      status: 0,
      consentId: response.clienttxnid,
    });
    if (trackingEntry == k500Error)
      throw new Error(
        'Error in Creating Entry in PeriodicEntity While CAMS Journey',
      );

    // Updating User Data
    let currentFinancialSummaryData = reqData.financialSummaryData;
    if (currentFinancialSummaryData.hasOwnProperty(reqData.bankCode)) {
      currentFinancialSummaryData[reqData.bankCode].push({
        consentHandleId: response.consentHandle,
        consentMode: kCAMS,
        clientTxnId: response.clienttxnid,
        createdAt: new Date(),
      });
    } else {
      currentFinancialSummaryData[reqData.bankCode] = [
        {
          consentHandleId: response.consentHandle,
          consentMode: kCAMS,
          clientTxnId: response.clienttxnid,
          createdAt: new Date(),
        },
      ];
    }
    let financialSummaryData = currentFinancialSummaryData;
    let updateUser = await this.userRepo.updateRowData(
      { financialSummaryData },
      reqData.userId,
    );
    if (updateUser == k500Error)
      throw new Error(
        'Error in Updating User financialSummaryData While inviteForAaForFinSummary',
      );

    // Setting Client Transaction Id in Redis
    if (response.clienttxnid)
      await this.redisService.set(
        response.clienttxnid,
        'FINANCIAL_SUMMARY',
        NUMBERS.ONE_HOURS_IN_SECONDS,
      );

    return {
      consentTxnId: response.clienttxnid,
      url: response.redirectionurl,
      response,
    };
  }
  //#endregion

  async getConsentDetails(consentId) {
    const yesterDay = new Date();
    yesterDay.setDate(yesterDay.getDate() - 1);
    const authData: any = await this.getAuthToken();

    const url = kCAMSGetConsentDetails;
    const body = {
      browser: 'chrome',
      clientIp: '0.0.0.0',
      consentStatus: 'ALL',
      fiuId: CAMS_FIU_ID,
      userId: CAMS_USER_ID,
      endDate: this.typeService.dateToJsonStr(new Date(), 'YYYY-MM-DD'),
      sessionId: authData.sessionId,
      startDate: this.typeService.dateToJsonStr(yesterDay, 'YYYY-MM-DD'),
      txnId: this.getUUID(),
    };

    const response = await this.apiService.post(url, body);
    if (response == k500Error) return kInternalError;

    const consentData =
      response.lst.find((el) => el.consentId == consentId) ?? {};
    if (!consentData) return { status: 'INITIALIZED' };
    return {
      response: JSON.stringify(consentData),
      status: consentData.status,
    };
  }

  async fetchData(consentId) {
    await this.typeService.delay(100);
    const url = kManualFetch;
    const body = {
      sessionId: await this.getSessionToken(),
      txnId: this.getUUID(),
      consentId,
      fiuID: CAMS_FIU_ID,
    };
    const response = await this.apiService.post(url, body, {}, {}, {}, true);
    console.log({ response });

    if (response.message) return response;
    if (response === k500Error || !response?.txnId) throw new Error();

    return response?.txnId;
  }
  async fetchDataForFinancialSummary(consentId, clientTxnId) {
    try {
      await this.typeService.delay(100);
      const url = kManualFetch;
      const body = {
        sessionId: await this.getSessionToken(),
        txnId: this.getUUID(),
        consentId,
        fiuID: CAMS_FIU_ID,
      };
      const response = await this.apiService.post(url, body);
      if (response == k500Error) return kInternalError;

      if (response?.dataSessionId) {
        if (clientTxnId) {
          await this.redisService.set(
            clientTxnId,
            'FINANCIAL_SUMMARY_BALANCE_REFRESH',
          );
        }
        return response?.dataSessionId;
      } else return false;
    } catch (error) {
      return kInternalError;
    }
  }

  async downloadTransData(consentId, bankCode) {
    try {
      const authData: any = await this.getAuthToken();
      const url = kCamsDownloadData;
      const body = {
        browser: 'chrome',
        clientIp: '0.0.0.0',
        fiuId: CAMS_FIU_ID,
        userId: CAMS_USER_ID,
        sessionId: authData.sessionId,
        txnId: this.getUUID(),
      };

      const response: any = await this.apiService.post(url, body);
      if (response == k500Error) return kInternalError;
      const decryptedData = response.decryptData;
      if (!decryptedData) return kInternalError;
      const rawData = JSON.parse(decryptedData);
      const accData = rawData.Account;
      if (!accData) return kInternalError;
      return this.getAccSummary(accData, bankCode);
    } catch (error) {
      return kInternalError;
    }
  }

  private async getSessionToken() {
    try {
      const sessionData: any = await this.getAuthToken();
      if (sessionData['message']) return sessionData;

      return sessionData.sessionId;
    } catch (error) {}
  }

  private getAccSummary(accData: any, bankCode: string) {
    try {
      const profileData = accData.Profile?.Holders?.Holder ?? {};
      const maskAccNumber = bankCode.toLowerCase() + accData.maskedAccNumber;
      const summaryData = accData.Summary ?? {};
      profileData.accountNumber = maskAccNumber;
      profileData.ifscode = summaryData.ifscCode;
      profileData.accStatus = summaryData.status;
      profileData.accOpenedOn = summaryData.openingDate + kGlobalTrail;
      profileData.bankCode = bankCode;
      profileData.aaService = true;

      const rawTransactions = (accData.Transactions ?? {}).Transaction ?? [];
      const transactions = [];
      for (let index = 0; index < rawTransactions.length; index++) {
        try {
          const rawData = rawTransactions[index];
          let transId = rawData.txnId ?? '0';
          if (transId.length <= 4) transId = new Date().getTime().toString();
          const transaction: any = {};
          transaction.accountId = maskAccNumber;
          transaction.amount = +rawData.amount;
          transaction.balanceAfterTransaction = +rawData.currentBalance;
          transaction.dateTime = rawData.valueDate + kGlobalTrail;
          transaction.description = rawData.narration;
          transaction.orderNumber = index;
          transaction.type = rawData.type;
          transaction.isFraud = false;
          transaction.transactionId = 'CAMSAA' + transId;
          transaction.transTime = rawData.transactionTimestamp;
          transaction.source = 'CAMSAA';
          transaction.bank = bankCode;
          transactions.push(transaction);
        } catch (error) {}
      }
      return { profileData, transactions };
    } catch (error) {
      return kInternalError;
    }
  }

  private getUUID() {
    return uuidv4();
  }

  async syncData(data) {
    try {
      if (
        data &&
        data?.maskedAccountNumber &&
        data?.maskedAccountNumber.toString().length === 4
      ) {
        data.maskedAccountNumber = 'XXXXXXXX' + data.maskedAccountNumber;
      }

      const maskAcc =
        data?.dataDetail?.jsonData?.Account?.maskedAccNumber.toString();

      if (
        data &&
        data?.dataDetail?.jsonData?.Account &&
        maskAcc &&
        maskAcc.length === 4
      ) {
        data.dataDetail.jsonData.Account.maskedAccNumber =
          'XXXXXXXX' + data?.dataDetail?.jsonData?.Account?.maskedAccNumber;
      }

      const callbackData = JSON.parse(JSON.stringify(data));
      const res: any = await this.saveWebhookResponse(JSON.stringify(data));
      if (res?.message) return res;
      const purpose = data.purpose;

      // Checking for Webhook Purpose: Banking or Financial Summary
      const find = await this.redisService.get(data?.clienttxnid);
      let periodicData: any = false;

      // Find periodicData Only When Webhook Came for Financial Summary
      if (
        find &&
        (find == 'FINANCIAL_SUMMARY' ||
          find == 'FINANCIAL_SUMMARY_BALANCE_REFRESH')
      ) {
        let consentId =
          purpose == 'ConsentStatusNotification'
            ? data?.clienttxnid
            : data?.consentid;
        // If We Have Client Txn Id and Purpose is Push Data
        periodicData =
          consentId &&
          (purpose == 'Push_Data' || purpose == 'ConsentStatusNotification')
            ? await this.repoManger.getRowWhereData(
                PeriodicEntity,
                ['consentId', 'userId', 'data', 'sessionId'],
                {
                  where: {
                    source: 2,
                    type: 6,
                    status: { [Op.or]: [0, 3] },
                    consentId,
                  },
                  order: [['createdAt', 'DESC']],
                },
              )
            : false;
        if (periodicData == k500Error)
          throw new Error('Error in DB Query for Periodic Entity');
      }

      let finalizedResponse: any = {};
      if (purpose == 'ConsentStatusNotification') {
        finalizedResponse = data;

        // Sending Webhook to UAT
        if (!find) {
          if (EnvConfig.isProd) {
            const url = EnvConfig.network.uatURL + 'thirdParty/cams/syncData';
            await this.apiService.post(url, callbackData);
            return null;
          } else return {};
        }
        if (periodicData) await this.addPeriodicEntry(data, periodicData);
      } else if (purpose == 'Push_Data') {
        const consentId = data?.consentid;
        const redisKey = `AA_DONE_${consentId}`;
        const check = await this.redisService.getKeyDetails(redisKey);
        if (!check) {
          const secondKey = `AA_SECOND_${consentId}`;
          await this.redisService.set(
            secondKey,
            'CAMS_Webhhok',
            NUMBERS.ONE_HOURS_IN_SECONDS,
          );
        } else await this.redisService.del(redisKey);
        data.pdfUrl = res?.pdfUrl;
        return await this.processAndValidateResponse(data, periodicData);
      }

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kUserAndDevError(JSON.stringify(error.message));
    }
  }

  private async saveWebhookResponse(data: any) {
    try {
      const reqData = JSON.parse(data);
      const type = reqData.purpose ?? 'UNKNOWN';
      const source = 'CAMS';
      let consentId = reqData.consentid;
      const clientTxnId = reqData.clienttxnid;
      if (clientTxnId) delete reqData.clienttxnid;
      const custId = reqData.customerId;
      if (consentId) delete reqData.consentid;
      if (custId) delete reqData.customerId;
      if (reqData.purpose) delete reqData.purpose;
      if (reqData.ver) delete reqData.ver;
      if (reqData.encryption == false) delete reqData.encryption;
      if (reqData.pan) delete reqData.pan;
      if (reqData.fipname) delete reqData.fipname;
      if (reqData.datarequested) delete reqData.datarequested;
      let pdfUrl;
      if (reqData.dataDetail) {
        if (reqData.dataDetail.pdfbase64) {
          pdfUrl = await this.fileService.base64ToFileURL(
            reqData.dataDetail.pdfbase64,
            'pdf',
            CLOUD_FOLDER_PATH.cams,
          );
          reqData.pdfUrl = pdfUrl;
          delete reqData.dataDetail.pdfbase64;
        }
        if (reqData.dataDetail.pdfbinary == '')
          delete reqData.dataDetail.pdfbinary;
        if (reqData.dataDetail.xmlData == '') delete reqData.dataDetail.xmlData;
        if (reqData.dataDetail.jsonData?.Account?.Transactions) {
          const transactionsUrl = await this.fileService.storeJsObjectToCloud(
            reqData.dataDetail.jsonData?.Account?.Transactions,
            clientTxnId,
            'json',
            CLOUD_FOLDER_PATH.camsTransactions,
          );
          reqData.transactionsUrl = transactionsUrl;
          delete reqData.dataDetail.jsonData?.Account?.Transactions;
        }
        if (reqData.dataDetail.jsonData?.Account?.['xsi:schemaLocation'])
          delete reqData.dataDetail.jsonData?.Account?.['xsi:schemaLocation'];
        if (reqData.dataDetail.jsonData?.Account?.['xmlns:xsi'])
          delete reqData.dataDetail.jsonData?.Account?.['xmlns:xsi'];
        if (reqData.dataDetail.jsonData?.Account?.['xmlns'])
          delete reqData.dataDetail.jsonData?.Account?.['xmlns'];
        if (reqData.dataDetail.jsonData?.['?xml'])
          delete reqData.dataDetail.jsonData?.['?xml'];
      }
      if (reqData.accRefNumber) delete reqData.accRefNumber;
      if (reqData.txnid) delete reqData.txnid;
      if (reqData.ConsentStatusNotification) {
        if (reqData.ConsentStatusNotification.consentId) {
          consentId = reqData.ConsentStatusNotification.consentId;
          delete reqData.ConsentStatusNotification.consentId;
        }
      }
      reqData.response = JSON.stringify(reqData);
      if (reqData.maskedAccountNumber) delete reqData.maskedAccountNumber;
      if (reqData.dataDetail) delete reqData.dataDetail;
      if (reqData.timestamp) delete reqData.timestamp;
      reqData.consentId = consentId;
      reqData.clientTxnId = clientTxnId;
      reqData.type = type;
      reqData.source = source;
      reqData.custId = custId;

      await this.responseRepo.createRowData(reqData);

      return { custId, pdfUrl };
    } catch (error) {
      return kInternalError;
    }
  }

  private async getAccDetails(consentTxnId, callbackData) {
    const attributes = ['accountNumber', 'bank', 'ifsCode'];
    const options = {
      useMaster: false,
      order: [['id', 'DESC']],
      where: { consentTxnId },
    };

    const bankingData = await this.bankingRepo.getRowWhereData(
      attributes,
      options,
    );
    if (bankingData == k500Error) throw new Error();
    if (!bankingData) {
      // Hit -> API (UAT callback)
      if (EnvConfig.isProd) {
        const url = EnvConfig.network.uatURL + 'thirdParty/cams/syncData';
        await this.apiService.post(url, callbackData);

        return null;
      } else return k422ErrorMessage(kNoDataFound);
    }
    return bankingData;
  }

  private async getEmpDetails(consentTxnId) {
    const empInclude: any = { model: employmentDetails };
    empInclude.attributes = ['companyName', 'salary'];
    const userInclude: any = { model: registeredUsers };
    userInclude.attributes = ['id'];
    userInclude.include = [empInclude];
    const include = [userInclude];
    const attributes = ['id'];
    const options = { include, where: { consentTxnId } };

    const bankingData = await this.bankingRepo.getRowWhereData(
      attributes,
      options,
    );
    if (bankingData == k500Error) return kInternalError;
    if (!bankingData) return k422ErrorMessage(kNoDataFound);

    return bankingData.user?.employmentData ?? {};
  }

  private getBankCode(ifscCode) {
    try {
      let receivedCode = '';
      switch (ifscCode.substring(0, 4).toUpperCase()) {
        // AU small finance bank
        case 'AUBL':
          receivedCode = 'AU_SMALL_FINANCE_BANK';
          break;

        case 'FINF':
          receivedCode = 'AU_SMALL_FINANCE_BANK';
          break;

        // AXIS Bank
        case 'UTIB':
          receivedCode = 'AXIS';
          break;

        // Bank of baroda bank
        case 'BARB':
          receivedCode = 'BANK_OF_BARODA';
          break;

        // Canara bank
        case 'CNRB':
          receivedCode = 'CANARA';
          break;

        // City union bank
        case 'CIUB':
          receivedCode = 'CITY_UNION';
          break;

        // Federal bank
        case 'FDRL':
          receivedCode = 'FEDERAL';
          break;

        // ICICI Bank
        case 'ICIC':
          receivedCode = 'ICICI';
          break;

        // IndusInd Bank
        case 'INDB':
          receivedCode = 'INDUSIND';
          break;

        // IDFC Bank
        case 'IDFB':
          receivedCode = 'IDFC';
          break;

        // Indian overseas bank
        case 'IOBA':
          receivedCode = 'INDIAN_OVERSEAS';
          break;

        // HDFC Bank
        case 'HDFC':
          receivedCode = 'HDFC';
          break;

        // Punjab national bank
        case 'PUNB':
          receivedCode = 'PNB';
          break;

        // State bank of india
        case 'SBIN':
          receivedCode = 'SBI';
          break;

        // Union bank of india
        case 'UBIN':
          receivedCode = 'UNION_BANK';
          break;

        // YES Bank
        case 'YESB':
          receivedCode = 'YES';
          break;

        // KARNATAKA Bank
        case 'KARB':
          receivedCode = 'KARNATAKA';
          break;

        // Bank of India
        case 'BKID':
          receivedCode = 'BOI';
          break;

        // Indian Bank
        case 'IDIB':
          receivedCode = 'INDIAN_BANK';
          break;

        // Karnataka Vikas Grameena Bank
        case 'KVGB':
          receivedCode = 'KARNATAKA_VG';
          break;

        // Andhra Pragathi Grameena Bank
        case 'APGB':
          receivedCode = 'ANDHRA_PRAGATHI_GRAMEENA';
          break;

        // NSDL Payments Bank
        case 'NSPB':
          receivedCode = 'NSDL';
          break;

        // HSBC Bank
        case 'HSBC':
          receivedCode = 'HSBC';
          break;

        case 'BDBL':
          receivedCode = 'BANDHAN';
          break;

        case 'SURY':
          receivedCode = 'SURYODAY_SMALL_FINANCE';
          break;

        case 'ESMF':
          receivedCode = 'ESAF_SMALL_FINANCE_BANK';
          break;

        case 'TMBL':
          receivedCode = 'TAMILNAD_MERCANTILE';
          break;

        case 'CSBK':
          receivedCode = 'CSB';
          break;

        case 'DLXB':
          receivedCode = 'DHANLAXMI';
          break;

        case 'KKBK':
          receivedCode = 'KOTAK';
          break;

        case 'KVBL':
          receivedCode = 'KARUR_VYASA';
          break;

        case 'JSFB':
          receivedCode = 'JANA_SMALL_FINANCE_BANK';
          break;

        case 'UCBA':
          receivedCode = 'UCO_BANK';
          break;

        case 'IBKL':
          receivedCode = 'IDBI';
          break;

        case 'UTKS':
          receivedCode = 'UTKARSH_SMALL_FINANCE';
          break;

        case 'CLBL':
          receivedCode = 'CAPITAL_SMALL_FINANCE';
          break;

        case 'MAHB':
          receivedCode = 'BANK_OF_MAHARASHTRA';
          break;

        default:
          break;
      }

      return receivedCode;
    } catch (error) {
      return kInternalError;
    }
  }

  async checkConsentStatus(consentHandleId, consentId, count = 0) {
    const url = kCAMSConsentStatus;
    const sessionData = await this.getAuthToken();
    const body = {
      consentHandle: consentHandleId,
      sessionId: sessionData.sessionId,
      fiuID: CAMS_FIU_ID,
    };
    const response = await this.apiService.post(url, body, null, null, {
      headers: { Authorization: sessionData.token },
    });
    if (response == k500Error) throw new Error();

    const consentDetails = response?.consentDetails;
    if (!consentDetails) return response;

    // Re-check in case of status is requested
    if (consentDetails?.consentStatus === 'PENDING' && count < 10) {
      count++;
      await this.typeService.delay(1000);
      return await this.checkConsentStatus(consentHandleId, consentId, count);
    }

    if (consentDetails?.consentStatus !== 'ACTIVE')
      return {
        consentDetails,
        consentExpired: false,
      };

    const currDate = new Date();
    const FIExpiry = new Date(consentDetails?.fIRequestTo);
    const consentExpiry = new Date(consentDetails?.consentEndDate);

    const consentExpired = currDate > FIExpiry || currDate > consentExpiry;
    if (consentExpired)
      return {
        consentDetails,
        consentExpired: true,
      };

    // Check if 5 attempts have been made in a month or not
    const consentLimit = await this.isConsentLimitExceeded(consentId);
    if (consentLimit)
      return {
        consentDetails,
        consentExpired: true,
      };
    return {
      consentDetails,
      consentExpired: false,
    };
  }

  // check consent limit per month
  async isConsentLimitExceeded(consentId) {
    const dates: any = await this.collectionService.startEndDate(
      'M',
      false,
      null,
    );
    let dateRange = this.typeService.getUTCDateRange(
      dates.startDate.toString(),
      dates.endDate.toString(),
    );
    const bankData = await this.repoManager.getCountsWhere(BankingEntity, {
      where: {
        consentId,
        createdAt: {
          [Op.gte]: dateRange.fromDate,
          [Op.lte]: dateRange.endDate,
        },
      },
    });
    return bankData >= 5;
  }

  // right now not using due to fetch data request's response issue
  async getConsentData(reqData) {
    const consentHandle = reqData?.consentHandleId;
    const url = kCAMSGetConsentData;
    const sessionData = await this.getAuthToken();
    const body = {
      consentHandle,
      sessionId: sessionData.sessionId,
      fiuID: CAMS_FIU_ID,
    };
    const response = await this.apiService.post(url, body, null, null, {
      headers: { Authorization: sessionData.token },
    });
    if (response == k500Error) throw new Error();
    if (response?.ConsentStatusNotification?.consentStatus != 'ACTIVE')
      return {};
    return await this.processAndValidateResponse(response);
  }

  async processAndValidateResponse(response, periodicData?) {
    let data =
      Array.isArray(response?.data) && response.data.length > 0
        ? response.data[0]
        : response;

    const accountSummary = data?.dataDetail?.jsonData?.Account?.Summary;

    if (
      data &&
      data?.dataDetail?.jsonData?.Account?.Summary &&
      accountSummary
    ) {
      data.dataDetail.jsonData.Account.Summary['ifscCode'] =
        data?.dataDetail?.jsonData?.Account?.Summary?.ifsc ??
        data?.dataDetail?.jsonData?.Account?.Summary?.ifscCode;
    }

    let profileData: any = { aaService: true };
    let finalizedResponse: any = {};

    if (data?.pdfUrl) {
      finalizedResponse = await this.processTransactions(
        data,
        profileData,
        data.pdfUrl,
        periodicData,
      );
      if (finalizedResponse?.forFinancialSummary == true) return true;
      if (finalizedResponse?.message) return finalizedResponse;
      // Validate finalized data
      return await this.validateFinalizedData(finalizedResponse);
    }
  }

  async processTransactions(data, profileData, pdfUrl, periodicData) {
    const accData = data?.dataDetail?.jsonData?.Account ?? {};
    const summaryData = accData.Summary;
    let userData = accData.Profile?.Holders?.Holder ?? {};
    if (userData && Array.isArray(userData) && userData.length)
      userData = userData[0] ?? [];

    // For Financial Summary Only
    if (periodicData) {
      const forFinancialSummary = await this.financialSummaryDataPreparation(
        periodicData,
        accData,
        data,
        summaryData,
        userData,
      );
      if (forFinancialSummary === true) return { forFinancialSummary };
    }

    // Fetch account details
    const userAccData: any = await this.getAccDetails(data?.clienttxnid, data);
    if (!userAccData) return {};
    if (userAccData.message) return userAccData;

    // Process profile details
    profileData = this.processProfileDetails(
      profileData,
      summaryData,
      data,
      userAccData,
      userData,
    );

    // Process transactions & extract recent transaction details
    const { transactions, recentData } = this.extractTransactionsAndRecentData(
      accData,
      accData.maskedAccNumber,
      pdfUrl,
      summaryData,
    );
    if (transactions && transactions.length === 0) {
      return kCAMSDataNotFound;
    }
    // Sync with BankingPro and finalize response
    return await this.syncAndFinalizeTransactions(
      data,
      transactions,
      profileData,
      userAccData,
      recentData,
    );
  }

  // Function to process profile details
  processProfileDetails(profileData, summaryData, data, userAccData, userData) {
    profileData.ifscCode = summaryData.ifscCode ?? '';
    profileData.accountNumber =
      data?.dataDetail?.jsonData?.Account?.maskedAccNumber.toLowerCase();
    profileData.isPreAccDetails = false;
    profileData.bankCode = this.getBankCode(profileData.ifscCode);

    if (userAccData && userAccData != k500Error) {
      const targetA = profileData.accountNumber.toLowerCase().replace(/x/g, '');
      const targetB = userAccData.accountNumber.toLowerCase();

      if (targetB.includes(targetA)) {
        profileData.ifscCode = userAccData.ifsCode;
        profileData.accountNumber = userAccData.accountNumber;
        profileData.isPreAccDetails = true;
      } else {
        profileData.isAccMismatch = true;
      }
    }

    return { ...profileData, ...userData };
  }

  // Function to extract transactions and recent transaction data
  extractTransactionsAndRecentData(
    accData,
    accountNumber,
    pdfUrl,
    summaryData,
  ) {
    const rawTransactions = (accData.Transactions ?? {}).Transaction ?? [];
    const transactions = [];
    let recentData = {
      balanceOfLastTrans: 0,
      recentBalance: 0,
      recentDate: 0,
      dateOfLastTrans: '',
      pdfUrl: '',
    };

    for (const rawData of rawTransactions) {
      try {
        let transId = rawData.txnId ?? '0';
        if (transId.length <= 4) transId = new Date().getTime().toString();

        let amount = rawData?.amount?.replace(/[^0-9.]/g, '').trim();
        let balance = rawData?.currentBalance?.replace(/[^0-9.-]/g, '').trim();
        let transactionDate = rawData.valueDate + kGlobalTrail;
        if (rawData?.valueDate?.includes('T')) {
          transactionDate = rawData.valueDate;
        }
        const transaction = {
          accountId: accountNumber,
          amount: Number(amount),
          balanceAfterTransaction: Number(balance),
          dateTime: transactionDate,
          description: rawData.narration,
          type: rawData.type,
          isFraud: false,
          transactionId: 'CAMSAA' + transId,
          transTime: rawData.transactionTimestamp,
          source: 'CAMSAA',
        };

        transactions.push(transaction);
      } catch (error) {}
    }

    if (transactions.length > 0) {
      const lastTransaction = transactions[transactions.length - 1];
      recentData = {
        balanceOfLastTrans: lastTransaction.balanceAfterTransaction ?? 0,
        dateOfLastTrans: lastTransaction.dateTime ?? '',
        pdfUrl: pdfUrl,
        recentDate: summaryData.balanceDateTime,
        recentBalance: +summaryData.currentBalance,
      };
    }

    return { transactions, recentData };
  }

  // Function to sync transactions and finalize response
  async syncAndFinalizeTransactions(
    data,
    transactions,
    profileData,
    userAccData,
    recentData,
  ) {
    profileData.ifscode = profileData?.ifscCode ?? '0';
    profileData.mobileNumber = profileData?.mobile ?? '';
    const body: any = { transactions, profileData };

    // Get salary and company details
    const empData: any = await this.getEmpDetails(data.clienttxnid);
    if (empData.message) return empData;

    body.companyName = empData.companyName ?? '';
    body.salary = empData.salary ?? '0';

    // Sync transactions with BankingPro
    const headers = kBankingProHeaders;
    const result = await this.apiService.requestPost(
      nSyncAuthAiTransactions,
      body,
      headers,
    );
    if (result == k500Error || !result?.data || !result.data.valid) {
      // add bot
      const text = '*Sync Transactions - syncAndFinalizeTransactions*';
      const threads = [
        `Body -> ${JSON.stringify(body)}`,
        `Result -> ${JSON.stringify(result)}`,
      ];
      this.slackService.sendMsg({ text, threads });
      return kInternalError;
    }

    // Finalize response
    data.transactions = null;
    const finalizedResponse = {
      ...data,
      accountDetails: result.data.accountDetails,
      recentData,
    };

    // Check account mismatch
    const authAiAccNo = result?.data?.accountDetails?.accountNumber;
    const targetA = authAiAccNo.toLowerCase().replace(/x/g, '');
    const targetB = userAccData.accountNumber.toLowerCase();

    finalizedResponse.accountDetails.isAccMismatch = !targetB.includes(targetA);

    return finalizedResponse;
  }

  async addAADataToBankingPro(body) {
    const headers = kBankingProHeaders;
    const response = await this.apiService.requestPost(
      nSyncAuthAiTransactions,
      body,
      headers,
    );
    const authAiAccNo = response?.data?.accountDetails?.accountNumber;
    if (response == k500Error) return kInternalError;
    if (!response.data) return kInternalError;
    if (!response.data.valid) return kInternalError;
  }

  // Function to validate finalized data
  async validateFinalizedData(finalizedResponse) {
    const responseData: any = await this.bankingService.syncAAData(
      finalizedResponse,
    );

    if (responseData?.message) return responseData;

    if (responseData.needUserInfo) {
      const profileData = await this.userService.routeDetails({
        id: responseData.userId,
      });
      if (profileData?.message) return profileData;

      if (responseData?.notifyUser) {
        const notificationData = {
          userId: responseData.userId,
          userList: [responseData.userId],
          title: profileData.userData?.currentStepTitle ?? '',
          content: (profileData.userData?.currentStepInfo ?? '')
            .replace(/\#/g, '')
            .replace(/\*/g, ''),
        };
        await this.sharedNotification.sendNotificationToUser(notificationData);
      }
      return responseData;
    }
    return {};
  }

  //#region financialSummaryDataPreparation
  async financialSummaryDataPreparation(
    periodicData,
    accData,
    data,
    summaryData,
    userData,
  ) {
    const userId = periodicData?.userId;
    const bankCode = periodicData?.data?.bankCode;
    const purpose = periodicData?.data?.purpose;
    const consentId = data?.consentid;

    // User Data
    let getUser = await this.userRepo.getRowWhereData(
      ['financialSummaryData', 'phone', 'fcmToken'],
      { where: { id: userId } },
    );
    if (getUser == k500Error) throw new Error();
    getUser.phone = this.cryptService.decryptPhone(getUser?.phone);

    const rawTransactions = (accData.Transactions ?? {}).Transaction ?? [];
    const transactions = [];
    for (let index = 0; index < rawTransactions.length; index++) {
      try {
        const rawData = rawTransactions[index];
        let transId = rawData.txnId ?? '0';
        if (transId.length <= 4) transId = new Date().getTime().toString();
        let amount = rawData?.amount?.replace(/[^0-9.]/g, '').trim();
        let balance = rawData?.currentBalance?.replace(/[^0-9.-]/g, '').trim();
        let transactionDate: any = rawData.valueDate + kGlobalTrail;
        if (rawData?.valueDate?.includes('T')) {
          transactionDate = rawData.valueDate;
        }
        const transaction: any = {};
        transaction.accountId = data.maskedAccountNumber;
        transaction.amount = Number(amount);
        transaction.balanceAfterTransaction = Number(balance);
        transaction.dateTime = transactionDate;
        transaction.description = rawData.narration;
        transaction.type = rawData.type;
        transaction.isFraud = false;
        transaction.transactionId = 'CAMSAA' + transId;
        transaction.transTime = rawData.transactionTimestamp;
        transaction.source = 'CAMSAA';
        transactions.push(transaction);
      } catch (error) {}
    }

    let profileData: any = { aaService: true };

    profileData.ifscCode = summaryData.ifscCode ?? '';
    profileData.accountNumber = getUser.phone + '_' + data.maskedAccountNumber;
    profileData.isPreAccDetails = false;
    profileData.bankCode = this.getBankCode(profileData.ifscCode);
    profileData = { ...profileData, ...userData };

    // Adding Data to Banking Pro
    await this.addAADataToBankingPro({
      transactions,
      profileData,
    });

    // Updating Periodic Table When Response Received
    await this.repoManger.updateRowWhereData(
      PeriodicEntity,
      { status: 1, consentId },
      { where: { sessionId: periodicData.sessionId } },
    );

    let financialSummaryData = getUser?.financialSummaryData;
    let fcmToken = getUser?.fcmToken;
    let accounts = financialSummaryData[bankCode];
    let checkForMultipleAccounts =
      purpose == 'FINANCIAL_SUMMARY_BALANCE_REFRESH'
        ? accounts.filter(
            (acc) =>
              consentId == acc?.consentId &&
              data?.maskedAccountNumber == acc.accountNumber,
          )
        : accounts.filter((acc) => consentId == acc?.consentId);
    let checkForMultipleConsents = accounts.find(
      (acc) =>
        acc?.consentStatus == 'ACTIVE' &&
        data?.maskedAccountNumber == acc?.accountNumber,
    );
    // Expiring Old Data When New Consent Added For Same A/c And Bank
    if (checkForMultipleConsents)
      checkForMultipleConsents.consentStatus = 'EXPIRED';

    // Preparing Transactions
    const updatedData =
      await this.financialSummaryService.getTransactionForFinancialSummary({
        userId,
        accountNumber: data.maskedAccountNumber.toLowerCase(),
        bank: bankCode,
        fromCallback: true,
      });

    delete checkForMultipleAccounts[0]?.dataFetched;
    delete checkForMultipleAccounts[0]?.consentStatus;
    accounts.push({
      ...checkForMultipleAccounts[0],
      accountNumber: data?.maskedAccountNumber,
      consentId,
      accountBalance: summaryData?.currentBalance,
      updatedAt: new Date().toJSON(),
      dataFetched: true,
      consentStatus: 'ACTIVE',
      lastBalance: updatedData?.lastBalance,
      salaryAmount: updatedData?.salaryAmount,
      salaryCount: updatedData?.salaryCount,
      ecsCharge: updatedData?.ecsCharge,
    });
    delete checkForMultipleAccounts[0]?.consentId;

    delete financialSummaryData[bankCode];
    financialSummaryData[bankCode] = accounts;

    let updateUser = await this.userRepo.updateRowData(
      { financialSummaryData },
      userId,
    );
    if (updateUser == k500Error)
      throw new Error('Error While Updating User Data in CAMS Callback');

    // Preparaing Financial Summary Data
    await this.financialSummaryService.getDataForFinancialSummary({
      userId,
      isRefresh: true,
    });

    await this.userService.routeDetails({ id: userId });

    // Notification to User
    let title =
      purpose == 'FINANCIAL_SUMMARY_BALANCE_REFRESH'
        ? 'Bank Balance Updated'
        : 'Spend Analytics Data Updated';
    let subTitle =
      purpose == 'FINANCIAL_SUMMARY_BALANCE_REFRESH'
        ? 'Your bank balance is fetched successfully'
        : 'Spend analytics data updated successfully';
    await this.sharedNotification.sendPushNotification(
      fcmToken,
      title,
      subTitle,
    );
    return true;
  }
  //#endregion

  //#region addPeriodicEntry
  async addPeriodicEntry(data, periodicData) {
    const userId = periodicData?.userId;
    const bankCode = periodicData?.data?.bankCode;
    const consentStatusNotification = data?.ConsentStatusNotification;
    const consentStatus = consentStatusNotification?.consentStatus;
    const consentHandleId = consentStatusNotification?.consentHandle;
    const consentId = consentStatusNotification?.consentId;
    const sessionId = periodicData?.sessionId;

    // Fetch Data from User
    let findUser = await this.userRepo.getRowWhereData(
      ['financialSummaryData'],
      {
        where: { id: userId },
      },
    );
    if (findUser == k500Error) throw new Error();

    // -->> When Consent is Accepted
    if (consentStatus === 'ACCEPTED' || consentStatus == 'ACTIVE') {
      const updateTrackingEntry = await this.repoManger.updateData(
        PeriodicEntity,
        {
          status: 3,
          consentId,
        },
        { sessionId },
      );
      if (updateTrackingEntry == k500Error) throw new Error();

      // Preparing Updated financialSummaryData
      let financialSummaryData = findUser.financialSummaryData;
      financialSummaryData[bankCode].forEach((ele) => {
        if (ele.consentHandleId == consentHandleId) {
          ele.consentStatus = 'ACCEPTED';
          ele.consentId = consentId;
          ele.createdAt = new Date();
        }
      });
      const updateUser = await this.userRepo.updateRowData(
        { financialSummaryData },
        userId,
      );
      if (updateUser == k500Error) throw new Error();
      return {};
    }

    // -->> When Consent is Rejected
    else if (consentStatus === 'REJECTED') {
      const updateTrackingEntry = await this.repoManger.updateData(
        PeriodicEntity,
        {
          status: 2,
          consentId,
        },
        { sessionId },
      );
      if (updateTrackingEntry == k500Error) throw new Error();

      let financialSummaryData = findUser.financialSummaryData;
      financialSummaryData[bankCode].forEach((ele) => {
        if (ele.consentHandleId == consentHandleId)
          ele.consentStatus = 'REJECTED';
      });
      const updateUser = await this.userRepo.updateRowData(
        { financialSummaryData },
        userId,
      );
      if (updateUser == k500Error) throw new Error();
      return {};
    }

    // -->> When Cosent is Revoked
    else if (consentStatus === 'REVOKED') {
      let financialSummaryData = findUser.financialSummaryData;
      financialSummaryData[bankCode].forEach((ele) => {
        if (ele.consentHandleId == consentHandleId)
          ele.consentStatus = 'REVOKED';
      });
      const updateUser = await this.userRepo.updateRowData(
        { financialSummaryData },
        userId,
      );
      if (updateUser == k500Error) throw new Error();
      return {};
    }
  }
  //#endregion
}
