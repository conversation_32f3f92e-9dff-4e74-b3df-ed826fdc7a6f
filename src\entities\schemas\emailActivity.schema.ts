// Imports
import { Document } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type EmailConversationsDocument = emailConversations & Document;

@Schema({ timestamps: true })
export class emailConversations {
  @Prop({ type: String, required: true })
  messageId: string;

  @Prop({ type: String, required: true })
  threadId: string;

  @Prop({ type: String, required: true })
  subject: string;

  @Prop({ type: String, required: true })
  fromEmail: number;

  @Prop({ type: String, required: false })
  toEmail: number;

  @Prop({ type: String, required: false })
  assigneeEmail: string;

  @Prop({ type: String, required: false })
  ccEmail: string;

  @Prop({ type: Date, required: true })
  date: Date;

  @Prop({ type: String, required: false })
  bodyText: string;

  @Prop({ type: String, required: true })
  rawBody: string;

  @Prop({ type: String, required: true })
  content: string;

  @Prop({ required: true })
  isIncoming: boolean;

  @Prop({ type: String, required: false })
  labels: string;

  @Prop({ type: String, required: false })
  replyTo: string;

  @Prop({ type: String, required: false })
  sentimentLabel: string;

  @Prop({ type: String, required: false })
  zohoSentiment: string;

  @Prop({ type: String, required: false })
  zohoSubCategory: string;

  @Prop({ type: String, required: false })
  emailReplySuggession: string;

  @Prop({ type: String, required: false })
  department: string;

  @Prop({ type: String, required: false })
  userId: string;

  @Prop({ type: String, required: false })
  ticketNumber: string;

  @Prop({ type: String, required: false })
  ticketStatus: string;

  @Prop({ type: Date, required: true })
  createdAt: Date;

  @Prop({ type: Date, required: true })
  updatedAt: Date;
}

export const EmailConversations =
  SchemaFactory.createForClass(emailConversations);
