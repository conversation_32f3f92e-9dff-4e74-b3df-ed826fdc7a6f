import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import {
  COLLECTION_EXECUTIVE_ROLE_ID,
  COLLECTION_PERFORMANCE_CYCLE_DATE,
  kCollectionTeam,
  SYSTEM_ADMIN_ID,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import { kCollectionTeamRedisKeys, REDIS_KEY } from 'src/constants/objects';
import {
  k422ErrorMessage,
  kInvalidParamValue,
  kParamMissing,
  kParamsMissing,
  kSUCCESSMessage,
  kNoDataFound,
  kWrongDetails,
} from 'src/constants/responses';
import { ChangeLogsEntity } from 'src/entities/change.logs.entity';
import { CollectionTeamPlanEntity } from 'src/entities/colletionTeamPlan.entity';
import { RedisService } from 'src/redis/redis.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { DateService } from 'src/utils/date.service';
import { TypeService } from 'src/utils/type.service';
import { AdminRedisSyncService } from '../admin/AdminRedisSync.service';
import { CollectionPerformanceReport } from 'src/entities/collectionPerformanceReport.entity';
import { NUMBERS } from 'src/constants/numbers';
import { EnvConfig } from 'src/configs/env.config';
import { FileService } from 'src/utils/file.service';
import { ReportHistoryEntity } from 'src/entities/reportHistory.entity';

@Injectable()
export class CollectionTeamPlanService {
  constructor(
    private readonly commonSharedService: CommonSharedService,
    private readonly adminRedisSyncService: AdminRedisSyncService,
    private readonly dateService: DateService,
    private readonly typeService: TypeService,
    private readonly redisService: RedisService,
    private readonly fileService: FileService,

    //Repository
    private readonly repoManager: RepositoryManager,
  ) {}

  async createTeam(reqData) {
    let name = reqData?.name;
    const updatedBy = reqData?.updatedBy;
    const employees = reqData?.employees;
    const minDelay = parseInt(reqData?.minDelay);
    const maxDelay = parseInt(reqData?.maxDelay);
    if (!name || !updatedBy || !employees) return kParamsMissing;
    if (
      typeof name != 'string' ||
      !name?.trim() ||
      isNaN(minDelay) ||
      isNaN(maxDelay) ||
      minDelay > maxDelay ||
      minDelay < -1 ||
      maxDelay < -1 ||
      (maxDelay == -1 && minDelay == -1) ||
      !Array.isArray(employees) ||
      !employees?.length
    )
      return kWrongDetails;
    name = name?.trim();

    const data = {
      name,
      minDelay,
      maxDelay,
      employees,
      updatedBy,
      isActive: true,
    };

    const isTeamExist: any = await this.checkTeamExistWithGivenPeriod(data);
    if (isTeamExist?.message) return isTeamExist;

    const isEmployeeExist: any = await this.checkEmployeeExistInOtherTeam(data);
    if (isEmployeeExist?.message) return isEmployeeExist;

    const createdData = await this.repoManager.createRowData(
      CollectionTeamPlanEntity,
      data,
    );
    if (createdData == k500Error) throw new Error();

    const logsData = {
      type: 'Collection Team Plan',
      subType: 'Create Collection Team',
      oldData: '',
      newData: JSON.stringify(createdData),
      adminId: updatedBy,
    };
    await this.repoManager.createRowData(ChangeLogsEntity, logsData);
    return true;
  }

  async updateTeam(reqData) {
    const teamId = reqData?.teamId;
    const updatedBy = reqData?.updatedBy;
    const employees = reqData?.employees ?? [];
    const isActive = reqData?.isActive === true;
    if (!teamId || !updatedBy || !employees) return kParamsMissing;
    if (isActive && (!Array.isArray(employees) || !employees?.length))
      return kWrongDetails;
    const teamData = await this.repoManager.getRowWhereData(
      CollectionTeamPlanEntity,
      ['id', 'minDelay', 'maxDelay', 'employees', 'isActive'],
      { where: { id: teamId } },
    );
    if (!teamData || teamData == k500Error) throw new Error();

    if (isActive) {
      const isTeamExist: any = await this.checkTeamExistWithGivenPeriod({
        minDelay: teamData?.minDelay,
        maxDelay: teamData?.maxDelay,
        teamId: teamData?.id,
      });
      if (isTeamExist?.message) return isTeamExist;

      const isEmployeeExist: any = await this.checkEmployeeExistInOtherTeam({
        teamId,
        employees: employees?.length ? employees : teamData?.employees,
      });
      if (isEmployeeExist?.message) return isEmployeeExist;
    }

    const updateData: any = { updatedBy };
    if (isActive && employees?.length) updateData.employees = employees;
    if (isActive != teamData.isActive) updateData.isActive = isActive;

    const updatedDetails = await this.repoManager.updateRowData(
      CollectionTeamPlanEntity,
      updateData,
      teamId,
    );

    if (updatedDetails == k500Error) throw new Error();

    const logsData = {
      type: 'Collection Team Plan',
      subType: 'Update Collection Team',
      oldData: JSON.stringify(teamData),
      newData: JSON.stringify(updateData),
      adminId: updatedBy,
    };
    await this.repoManager.createRowData(ChangeLogsEntity, logsData);
    return true;
  }

  async checkTeamExistWithGivenPeriod(reqData) {
    const { minDelay, maxDelay, teamId } = reqData;

    const where: any = {
      minDelay,
      maxDelay,
      isActive: true,
    };
    if (teamId) where.id = { [Op.ne]: teamId };
    const existData = await this.repoManager.getRowWhereData(
      CollectionTeamPlanEntity,
      ['id'],
      { where },
    );
    if (existData == k500Error) throw new Error();
    if (existData)
      return k422ErrorMessage(
        'Team already exist with given minDelay and maxDelay',
      );
    return {};
  }

  async checkEmployeeExistInOtherTeam(reqData) {
    const { employees, teamId } = reqData;
    const where: any = {
      employees: {
        [Op.overlap]: employees,
      },
      isActive: true,
    };
    if (teamId) where.id = { [Op.ne]: teamId };
    const isEmployeeExistInOtherTeam = await this.repoManager.getRowWhereData(
      CollectionTeamPlanEntity,
      ['id'],
      { where },
    );

    if (isEmployeeExistInOtherTeam == k500Error) throw new Error();
    if (isEmployeeExistInOtherTeam)
      return k422ErrorMessage(
        'Any of the selected employees already exist in another team, remove them first',
      );
    return {};
  }

  async getAllTeam() {
    let allTeamData = await this.redisService.get(
      kCollectionTeamRedisKeys.teamPlanData,
    );
    if (!allTeamData) allTeamData = [];
    else allTeamData = JSON.parse(allTeamData);

    const formattedData = [];
    let tempDataObj = {};
    for (const ele of allTeamData) {
      let teamName = ele?.name;
      let startPoint = '';
      let endPoint = '';
      if (ele?.minDelay == -1) startPoint = 'Upcoming';
      else if (ele?.minDelay == 0) startPoint = 'OnTime';
      else startPoint = `${ele?.minDelay}`;

      if (ele?.maxDelay == -1) endPoint = 'Upcoming';
      else if (ele?.maxDelay == 0) endPoint = 'OnTime';
      else endPoint = `${ele?.maxDelay}`;

      if (
        ele?.minDelay == ele?.maxDelay &&
        (ele?.minDelay == 0 || ele?.minDelay == -1)
      )
        teamName = teamName + ` (${startPoint})`;
      else if (
        ele?.maxDelay == ele?.minDelay &&
        ele?.minDelay != -1 &&
        ele?.minDelay != 0
      )
        teamName = teamName + ` (${+startPoint} DPD)`;
      else if (ele?.maxDelay == -1 && ele?.minDelay != -1 && ele?.minDelay != 0)
        teamName = teamName + ` (${+startPoint - 1}+ DPD)`;
      else teamName = teamName + ` (${startPoint} - ${endPoint} DPD)`;
      const currentMember = [];
      for (const employee of ele?.employees) {
        const adminData = await this.commonSharedService.getAdminData(employee);
        if (adminData?.fullName) currentMember.push(adminData?.fullName);
      }

      tempDataObj = {
        id: ele?.id,
        'Team Name': teamName,
        'Current Member': currentMember,
        'Last Updated By':
          (await this.commonSharedService.getAdminData(ele?.updatedBy))
            ?.fullName ?? '-',
        'Last Updated At':
          (await this.dateService.formatDate(ele?.updatedAt)) ?? '-',
      };
      formattedData.push(tempDataObj);
    }
    return formattedData;
  }

  async syncTeamData(reqData?) {
    const isForcefully = reqData?.isForcefully?.toString() == 'true';
    const needReport = reqData?.needReport?.toString() == 'true';
    const todayDate = new Date();
    const nextDate = new Date();
    nextDate.setDate(nextDate.getDate() + 1);
    let cycleStartDate = await this.redisService.get(
      kCollectionTeamRedisKeys.cycleDate,
    );
    if (!cycleStartDate) cycleStartDate = COLLECTION_PERFORMANCE_CYCLE_DATE;

    //if isForcefully true or next Date is cycleStartDate date
    if (isForcefully || cycleStartDate == nextDate.getDate()) {
      await this.redisService.set(
        kCollectionTeamRedisKeys.needToSetGoal,
        'YES',
      );
      await this.getCollectionTeamFilter({ isRefresh: true });
    }

    const isTodayIsCycleStartDate =
      +cycleStartDate == this.typeService.getGlobalDate(todayDate).getDate();
    if (!isTodayIsCycleStartDate && !isForcefully) return true;

    if (needReport)
      await this.generateTeamGoalDetailsReport(cycleStartDate, isForcefully);

    const allTeamData = await this.repoManager.getTableWhereData(
      CollectionTeamPlanEntity,
      [
        'id',
        'minDelay',
        'maxDelay',
        'updatedBy',
        'name',
        'employees',
        'updatedAt',
        'goalPercentage',
      ],
      {
        where: { isActive: true },
        order: [['id', 'ASC']],
      },
    );
    if (allTeamData == k500Error) throw new Error();

    await this.redisService.set(
      kCollectionTeamRedisKeys.teamPlanData,
      JSON.stringify(allTeamData),
    );

    await this.getCollectionTeamFilter({ isRefresh: true });

    await this.getCollectionData(
      this.typeService.getGlobalDate(new Date()).toJSON(),
      true,
    );

    return true;
  }

  async getCollectionAdminWithTeam() {
    let adminRoleData = await this.redisService.get(REDIS_KEY.ADMIN_ROLES);
    if (!adminRoleData) {
      await this.adminRedisSyncService.storeAllAdminsOnRedis();
      adminRoleData = await this.redisService.get(REDIS_KEY.ADMIN_ROLES);
    }
    if (adminRoleData) adminRoleData = JSON.parse(adminRoleData);
    else throw new Error();
    const collectionRole = adminRoleData.find(
      (ele) => ele.id == COLLECTION_EXECUTIVE_ROLE_ID && ele.isActive == '1',
    );
    if (!collectionRole) throw new Error();
    let allAdmins = await this.redisService.get(REDIS_KEY.ADMINS_DATA);
    if (!allAdmins) {
      await this.adminRedisSyncService.storeAllAdminsOnRedis();
      allAdmins = await this.redisService.get(REDIS_KEY.ADMINS_DATA);
    }
    if (allAdmins) allAdmins = JSON.parse(allAdmins);
    else throw new Error();

    const collectionAdmins = allAdmins.filter(
      (ele) => ele.roleId == collectionRole?.id && ele.isActive == '1',
    );
    if (!collectionAdmins?.length) return [];

    const allTeamData = await this.repoManager.getTableWhereData(
      CollectionTeamPlanEntity,
      ['id', 'name', 'employees'],
      {
        where: { isActive: true },
        order: [['id', 'ASC']],
      },
    );
    if (allTeamData == k500Error) throw new Error();

    const workingEmployees = allTeamData?.map((ele) => ele?.employees)?.flat();
    const adminWithTeam = [];
    let tempObj = {};
    for (const ele of allTeamData) {
      const employees = ele?.employees;
      for (let idx = 0; idx < employees?.length; idx++) {
        tempObj = {
          id: employees[idx],
          fullName:
            (await this.commonSharedService.getAdminData(employees[idx]))
              ?.fullName ?? '-',
          teamId: ele?.id ?? '',
          teamName: ele?.name ?? '',
        };

        adminWithTeam.push(tempObj);
      }
    }

    for (const ele of collectionAdmins) {
      if (workingEmployees.includes(ele?.id)) continue;
      tempObj = {
        id: ele?.id,
        fullName: ele?.fullName ?? '-',
        teamId: '',
        teamName: '',
      };
      adminWithTeam.push(tempObj);
    }
    return adminWithTeam;
  }

  async getCollectionTeamFilter(reqData?) {
    const isRefresh = reqData?.isRefresh?.toString() == 'true' ? true : false;
    const needOnlyEmployees =
      reqData?.needOnlyEmployees?.toString() == 'true' ? true : false;
    const needOnlyTeams =
      reqData?.needOnlyTeams?.toString() == 'true' ? true : false;
    let filterOptions: any;
    if (!isRefresh) {
      filterOptions = await this.redisService.get(
        kCollectionTeamRedisKeys.filterOptions,
      );
      if (filterOptions) filterOptions = JSON.parse(filterOptions);
    }
    if (!filterOptions) {
      let allTeamData = await this.redisService.get(
        kCollectionTeamRedisKeys.teamPlanData,
      );
      if (allTeamData) allTeamData = JSON.parse(allTeamData);
      else throw new Error();
      const teamDetails = allTeamData.map((ele) => ({
        id: ele?.id ?? 0,
        name: ele?.name?.trim() ?? '-',
        employees: ele?.employees ?? [],
      }));
      const employeesDetails = [];
      for (const team of teamDetails) {
        const employees = team.employees;
        for (let idx = 0; idx < employees?.length; idx++) {
          employeesDetails.push({
            id: employees[idx] ?? 0,
            fullName:
              (await this.commonSharedService.getAdminData(employees[idx]))
                ?.fullName ?? '-',
            teamId: team?.id ?? 0,
          });
        }
      }

      let needToSetGoal = await this.redisService.get(
        kCollectionTeamRedisKeys.needToSetGoal,
      );
      if (needToSetGoal === 'YES') needToSetGoal = 'YES';
      else needToSetGoal = 'NO';
      filterOptions = { teamDetails, employeesDetails, needToSetGoal };
      await this.redisService.set(
        kCollectionTeamRedisKeys.filterOptions,
        JSON.stringify(filterOptions),
        NUMBERS.SEVEN_DAYS_IN_SECONDS,
      );
    }

    if (!(needOnlyEmployees || needOnlyTeams)) return filterOptions;
    else if (needOnlyEmployees) {
      return filterOptions?.employeesDetails?.map((ele) => ({
        id: ele.id,
        fullName: ele.fullName,
      }));
    } else
      return filterOptions?.teamDetails?.map((ele) => ({
        id: ele.id,
        name: ele.name,
      }));
  }

  async setGoalForMonth(reqData) {
    const goalData = reqData?.goalData;
    if (!goalData) return kParamMissing('goalData');
    const needToSetGoal = await this.redisService.get(
      kCollectionTeamRedisKeys.needToSetGoal,
    );
    if (needToSetGoal != 'YES')
      return k422ErrorMessage('Goal already set for this month');
    if (typeof goalData != 'object' || !Object.keys(goalData)?.length)
      return kInvalidParamValue('goalData');
    const teamIds = Object.keys(goalData)
      ?.map((ele) => Number(ele))
      ?.sort();
    const allTeam = await this.repoManager.getTableWhereData(
      CollectionTeamPlanEntity,
      ['id'],
      {
        where: { isActive: true },
        order: [['id', 'ASC']],
      },
    );
    if (allTeam == k500Error) throw new Error();
    if (!allTeam?.length) return kNoDataFound;
    const allTeamIds = allTeam.map((ele) => ele.id);
    if (JSON.stringify(teamIds) != JSON.stringify(allTeamIds))
      return k422ErrorMessage('Please provide all valid teamIds');

    let isInValidGoals = false;
    for (const teamId in goalData) {
      if (!goalData[teamId] || typeof goalData[teamId] != 'number') {
        isInValidGoals = true;
        break;
      }
      const target = parseFloat(goalData[teamId].toFixed(2));
      if (target <= 0 || target > 100) {
        isInValidGoals = true;
        break;
      }
      goalData[teamId] = target;
    }
    if (isInValidGoals)
      return k422ErrorMessage('Please provide all valid targets');

    for (const teamId in goalData) {
      const errorInUpdate = await this.repoManager.updateRowData(
        CollectionTeamPlanEntity,
        { goalPercentage: goalData[teamId] },
        teamId,
      );
      if (errorInUpdate == k500Error) throw new Error();
    }
    await this.redisService.set(kCollectionTeamRedisKeys.needToSetGoal, 'NO');
    await this.getCollectionTeamFilter({ isRefresh: true });
    return kSUCCESSMessage('Goals Successfully set for all teams');
  }

  private async getTeamAndPoolData(date: string) {
    let allTeamData = await this.redisService.get(
      kCollectionTeamRedisKeys.teamPlanData,
    );
    if (allTeamData) allTeamData = JSON.parse(allTeamData);
    else throw new Error();

    const workingEmployees = allTeamData?.map((ele) => ele?.employees)?.flat();

    const poolData = await this.repoManager.getTableWhereData(
      CollectionPerformanceReport,
      [
        'id',
        'dataDate',
        'adminId',
        'pool1to30',
        'pool31to60',
        'pool61to90',
        'pool91to120',
        'pool121to150',
        'pool151to180',
        'collection1to30',
        'collection31to60',
        'collection61to90',
        'collection91to120',
        'collection121to150',
        'collection151to180',
      ],
      {
        where: {
          dataDate: date,
          adminId: workingEmployees,
        },
      },
    );
    if (poolData == k500Error) throw new Error();

    return { poolData, allTeamData };
  }

  private async prepareTeamGoalDetails(allData) {
    const { poolData, allTeamData } = allData;

    const teamGoalDetails = [];
    for (const team of allTeamData) {
      const employees = team.employees;
      for (let idx = 0; idx < employees.length; idx++) {
        const adminPoolData = poolData.find(
          (ele) => ele.adminId == employees[idx],
        );
        const tempObj = {
          'Agent Id': employees[idx] ?? 0,
          'Agent Name':
            (await this.commonSharedService.getAdminData(employees[idx]))
              ?.fullName ?? '-',
          'Team Id': team.id ?? 0,
          Team: team?.name?.trim() ?? '-',
          Target: +(team?.goalPercentage ?? 0)?.toFixed(2),
          Pool: 0,
          Collection: 0,
          Conversion: 0,
          'Pending Amount': 0,
          'Pending Conversion': 0,
        };
        let pool, collection, pendingAmount, conversion, pendingConversion;
        if (tempObj['Team Id'] == kCollectionTeam.sma0.id) {
          pool = adminPoolData?.pool1to30 ?? 0;
          collection = pool > 0 ? adminPoolData?.collection1to30 ?? 0 : 0;
        } else if (tempObj['Team Id'] == kCollectionTeam.sma1.id) {
          pool = adminPoolData?.pool31to60 ?? 0;
          collection = pool > 0 ? adminPoolData?.collection31to60 ?? 0 : 0;
        } else if (tempObj['Team Id'] == kCollectionTeam.npa.id) {
          pool =
            (adminPoolData?.pool61to90 ?? 0) +
            (adminPoolData?.pool91to120 ?? 0) +
            (adminPoolData?.pool121to150 ?? 0) +
            (adminPoolData?.pool151to180 ?? 0);
          collection =
            pool > 0
              ? (adminPoolData?.collection61to90 ?? 0) +
                (adminPoolData?.collection91to120 ?? 0) +
                (adminPoolData?.collection121to150 ?? 0) +
                (adminPoolData?.collection151to180 ?? 0)
              : 0;
        } else continue;
        pendingAmount = pool - collection > 0 ? pool - collection : 0;
        conversion = +((pool > 0 ? collection / pool : 0) * 100)?.toFixed(2);
        pendingConversion = +(
          (pool > 0 ? pendingAmount / pool : 0) * 100
        )?.toFixed(2);
        tempObj.Pool = pool;
        tempObj.Collection = collection;
        tempObj.Conversion = conversion;
        tempObj['Pending Amount'] = pendingAmount;
        tempObj['Pending Conversion'] = pendingConversion;
        teamGoalDetails.push(tempObj);
      }
    }
    //sort by Agent Name
    teamGoalDetails.sort((a, b) =>
      a['Agent Name'].localeCompare(b['Agent Name']),
    );
    return teamGoalDetails;
  }

  private preparePerformanceData(teamGoalDetails) {
    const employeePerformance = {};
    const teamPerformanceTempObj = {
      id: 0,
      name: '-',
      pool: 0,
      goalAmount: 0,
      goalPercent: 0,
      remainingAmount: 0,
      remainingPercent: 0,
      collectedAmount: 0,
      collectedPercent: 0,
      isGoalAchieved: false,
    };
    const teamPerformance = {
      [kCollectionTeam.sma0.id]: {
        ...teamPerformanceTempObj,
        id: kCollectionTeam.sma0.id,
        name: kCollectionTeam.sma0.name,
      },
      [kCollectionTeam.sma1.id]: {
        ...teamPerformanceTempObj,
        id: kCollectionTeam.sma1.id,
        name: kCollectionTeam.sma1.name,
      },
      [kCollectionTeam.npa.id]: {
        ...teamPerformanceTempObj,
        id: kCollectionTeam.npa.id,
        name: kCollectionTeam.npa.name,
      },
    };
    const topPerformerTempObj = {
      1: { id: 0, fullName: '-', conversion: 0 },
      2: { id: 0, fullName: '-', conversion: 0 },
      3: { id: 0, fullName: '-', conversion: 0 },
    };

    const topPerformer = {
      [kCollectionTeam.sma0.id]: {
        ...kCollectionTeam.sma0,
        performers: structuredClone(topPerformerTempObj),
      },
      [kCollectionTeam.sma1.id]: {
        ...kCollectionTeam.sma1,
        performers: structuredClone(topPerformerTempObj),
      },
      [kCollectionTeam.npa.id]: {
        ...kCollectionTeam.npa,
        performers: structuredClone(topPerformerTempObj),
      },
    };

    for (const employee of teamGoalDetails) {
      let teamId = 0;
      if (
        ![
          kCollectionTeam.sma0.id,
          kCollectionTeam.sma1.id,
          kCollectionTeam.npa.id,
        ].includes(employee['Team Id'])
      )
        continue;
      teamId = employee['Team Id'];

      const tempObj = {
        id: employee['Agent Id'],
        fullName: employee['Agent Name'],
        teamId: employee['Team Id'],
        teamName: employee['Team'],
        pool: employee.Pool,
        goalAmount: 0,
        goalPercent: +employee.Target?.toFixed(2),
        collectedAmount: 0,
        collectedPercent: 0,
        remainingAmount: 0,
        remainingPercent: 0,
        isGoalAchieved: false,
      };
      const goalAmount = +((employee.Pool * employee.Target) / 100)?.toFixed();
      const collectedAmount = employee.Pool > 0 ? employee.Collection : 0;
      const collectedPercent = +(
        (employee.Pool > 0 ? employee.Collection / employee.Pool : 0) * 100
      ).toFixed();
      const remainingAmount =
        goalAmount > collectedAmount ? goalAmount - collectedAmount : 0;
      const remainingPercent = +(
        (goalAmount > 0 ? remainingAmount / goalAmount : 0) * 100
      )?.toFixed();

      tempObj.goalAmount = goalAmount;
      tempObj.collectedAmount = collectedAmount;
      tempObj.collectedPercent = collectedPercent;
      tempObj.remainingAmount = remainingAmount;
      tempObj.remainingPercent = remainingPercent;
      tempObj.isGoalAchieved =
        remainingAmount <= 0 && tempObj.pool > 0 ? true : false;
      employeePerformance[tempObj.id] = tempObj;

      //add pool and collectedAmount in teamPerformance
      teamPerformance[teamId].pool += tempObj.pool;
      teamPerformance[teamId].collectedAmount += tempObj.collectedAmount;
      teamPerformance[teamId].goalPercent = +employee.Target;

      //prepare topPerformer
      const conversion = employee.Conversion;
      if (conversion >= topPerformer[teamId]['performers'][1].conversion) {
        topPerformer[teamId]['performers'][3] = {
          ...topPerformer[teamId]['performers'][2],
        };
        topPerformer[teamId]['performers'][2] = {
          ...topPerformer[teamId]['performers'][1],
        };
        topPerformer[teamId]['performers'][1] = {
          id: tempObj.id,
          fullName: tempObj.fullName,
          conversion,
        };
      } else if (
        conversion >= topPerformer[teamId]['performers'][2].conversion
      ) {
        topPerformer[teamId]['performers'][3] = {
          ...topPerformer[teamId]['performers'][2],
        };
        topPerformer[teamId]['performers'][2] = {
          id: tempObj.id,
          fullName: tempObj.fullName,
          conversion,
        };
      } else if (
        conversion >= topPerformer[teamId]['performers'][3].conversion
      ) {
        topPerformer[teamId]['performers'][3] = {
          id: tempObj.id,
          fullName: tempObj.fullName,
          conversion,
        };
      }
    }

    //prepare teamPerformance
    for (const teamId in teamPerformance) {
      const goalAmount = +(
        (teamPerformance[teamId].pool * teamPerformance[teamId].goalPercent) /
        100
      )?.toFixed();
      const collectedAmount =
        teamPerformance[teamId].pool > 0
          ? teamPerformance[teamId].collectedAmount
          : 0;
      const collectedPercent = +(
        (teamPerformance[teamId].pool > 0
          ? teamPerformance[teamId].collectedAmount /
            teamPerformance[teamId].pool
          : 0) * 100
      ).toFixed();
      const remainingAmount =
        goalAmount > collectedAmount ? goalAmount - collectedAmount : 0;
      const remainingPercent = +(
        (goalAmount > 0 ? remainingAmount / goalAmount : 0) * 100
      )?.toFixed();

      teamPerformance[teamId].goalAmount = goalAmount;
      teamPerformance[teamId].collectedAmount = collectedAmount;
      teamPerformance[teamId].collectedPercent = collectedPercent;
      teamPerformance[teamId].remainingAmount = remainingAmount;
      teamPerformance[teamId].remainingPercent = remainingPercent;
      teamPerformance[teamId].isGoalAchieved =
        remainingAmount <= 0 && teamPerformance[teamId].pool > 0 ? true : false;
    }

    return { employeePerformance, teamPerformance, topPerformer };
  }

  async getCollectionData(date?: string, needToReset = false) {
    const dataDate = date
      ? date
      : this.typeService.getGlobalDate(new Date()).toJSON();
    const allData = await this.getTeamAndPoolData(dataDate);

    const teamGoalDetails = await this.prepareTeamGoalDetails(allData);

    const performanceData = this.preparePerformanceData(teamGoalDetails);

    if (needToReset) {
      await this.redisService.set(
        kCollectionTeamRedisKeys.teamPerformance,
        JSON.stringify(performanceData),
        NUMBERS.ONE_HOURS_IN_SECONDS,
      );
      await this.redisService.set(
        kCollectionTeamRedisKeys.teamGoalDetails,
        JSON.stringify(teamGoalDetails),
        NUMBERS.ONE_HOURS_IN_SECONDS,
      );
    }

    return { teamGoalDetails, performanceData };
  }

  async generateTeamGoalDetailsReport(cycleStartDate, isForcefully) {
    let todayDate: any = new Date();
    let startDate: any = new Date();
    let endDate: any = new Date();

    startDate.setDate(cycleStartDate);
    if (!isForcefully || cycleStartDate > todayDate.getDate())
      startDate.setMonth(startDate.getMonth() - 1);
    if (!isForcefully) endDate.setDate(cycleStartDate - 1);

    startDate = this.typeService.getGlobalDate(startDate).toJSON();
    endDate = this.typeService.getGlobalDate(endDate).toJSON();

    const { teamGoalDetails } = await this.getCollectionData(endDate);
    if (!teamGoalDetails.length) return {};
    teamGoalDetails.forEach((ele) => {
      delete ele['Agent Id'];
      delete ele['Team Id'];
    });
    const rawExcelData = {
      sheets: ['Collection Team Goals'],
      data: [teamGoalDetails],
      sheetName: 'Collection Team Goals Report.xlsx',
    };
    const fileUrl = await this.fileService.objectToExcelURL(rawExcelData, true);
    if (fileUrl?.message) throw new Error();
    const reportData = {
      adminId: SYSTEM_ADMIN_ID,
      fromDate: startDate,
      toDate: endDate,
      apiUrl: 'admin/CollectionTeamPlan/syncTeamData',
      reportName: 'Collection Team Goals',
      downloadUrl: fileUrl,
      status: '1',
    };
    await this.repoManager.createRowData(ReportHistoryEntity, reportData);
  }
}
