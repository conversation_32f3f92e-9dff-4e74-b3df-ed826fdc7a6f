import { EnvConfig } from 'src/configs/env.config';
import { HOST_URL } from 'src/constants/globals';

export function kBasicDetailsScreen(
  communicationLanguage: boolean,
  purposeList,
  bankList,
) {
  return {
    showAppBar: true,
    appBarColor: '0xffF1F1F1',
    appBarElevation: 0,
    appBarLeading: {
      type: 9,
      key: 'back',
      iconCodePoint: '62832',
      color: '0x7f040404',
      size: 18,
      clickEvent: { route: 'dashboardRoute', routingType: 3 },
    },
    appBarAction: [
      {
        type: 10,
        key: 'helpButton',
        padding: { right: 16 },
        svgURL:
          'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/May-2025/*************.svg',
        clickEvent: { route: 'helpAndSupportRoute', routingType: 1 },
      },
    ],
    surfaceTintColor: '0xffF1F1F1',
    backgroundColor: '0xffF1F1F1',
    body: {
      type: 17,
      key: 'body',
      padding: { left: 16, right: 16, bottom: 15 },
      child: {
        type: 4,
        key: 'view',
        crossAxisAlignment: 2,
        children: [
          {
            type: 19,
            key: 'title',
            padding: { top: 15 },
            texts: [
              {
                text: 'Basic details',
                style: { color: '0xff000000', fontSize: 18, fontWeight: 5 },
              },
            ],
          },
          {
            type: 19,
            key: 'description',
            padding: { top: 8 },
            textAlign: 1,
            texts: [
              {
                text: 'Provide the required basic details.',
                style: { color: '0x99000000', fontSize: 12, fontWeight: 4 },
              },
            ],
          },
          {
            type: 5,
            key: 'fields',
            color: '0xffffffff',
            borderRadius: {
              bottomLeft: 15,
              bottomRight: 15,
              topLeft: 15,
              topRight: 15,
            },
            padding: { left: 15, top: 15, right: 15, bottom: 15 },
            margin: { top: 25 },
            child: {
              type: 4,
              key: 'widgets',
              children: [
                {
                  type: 11,
                  key: 'email',
                  isEmailPicker: true,
                  labelText: 'Email ID',
                  hintText: 'Enter your valid email address',
                  formatters: [2],
                  keyboardType: 2,
                  infoMsg: 'Verification link will be sent on this email',
                  validations: {
                    regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$',
                    error: 'Please enter a valid email ID',
                  },
                },
                {
                  type: 11,
                  key: 'pan',
                  padding: { top: 8, bottom: 8 },
                  labelText: 'PAN number',
                  hintText: 'Enter your PAN number',
                  maxLength: 10,
                  formatters: [4],
                  keyboardType: 9,
                  textCapitalization: 1,
                  validations: {
                    regex: '^[A-Z]{5}[0-9]{4}[A-Z]{1}',
                    error: 'Please enter a valid PAN number',
                  },
                },
                {
                  type: 7,
                  key: 'purposeId',
                  labelText: 'Loan purpose',
                  options: purposeList,
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select a loan purpose',
                  },
                },
                ...(!communicationLanguage
                  ? [
                      {
                        type: 7,
                        key: 'communicationLanguage',
                        padding: { top: 8 },
                        labelText: 'Mode of communication',
                        options: [
                          { id: 1, value: 'English' },
                          { id: 2, value: 'Hindi' },
                        ],
                        validations: {
                          regex: '^(?!s*$).+',
                          error: 'Please select your communication language',
                        },
                      },
                    ]
                  : []),
                {
                  type: 7,
                  key: 'politicallyExposed',
                  padding: { top: 8 },
                  labelText: 'Politically exposed',
                  options: [
                    { id: 0, value: 'No' },
                    { id: 1, value: 'Yes' },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select your political exposition status',
                  },
                },
                {
                  type: 7,
                  key: 'empInfo',
                  padding: { top: 8 },
                  labelText: 'Employment status',
                  options: [
                    { id: 'Salaried', value: 'Salaried' },
                    { id: 'Consultant', value: 'Consultant' },
                    { id: 'Self-Employed', value: 'Self-Employed' },
                    { id: 'Retired', value: 'Retired' },
                    { id: 'Student', value: 'Student' },
                    { id: 'Homemaker', value: 'Homemaker' },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select an employment status',
                  },
                },
                {
                  type: 7,
                  key: 'maritalInfo',
                  padding: { top: 8 },
                  labelText: 'Marital status',
                  options: [
                    {
                      id: 'Single',
                      value: 'Single',
                      subFieldKey: 'motherName',
                      subField: {
                        type: 11,
                        key: 'motherName',
                        padding: { top: 8 },
                        labelText: 'Mother name',
                        hintText: 'Enter your mother name',
                        formatters: [7],
                        keyboardType: 4,
                        textCapitalization: 4,
                        validations: { error: 'Please enter mother name' },
                      },
                    },
                    {
                      id: 'Married',
                      value: 'Married',
                      subFieldKey: 'spouseName',
                      subField: {
                        type: 11,
                        key: 'spouseName',
                        padding: { top: 8 },
                        labelText: 'Spouse name',
                        hintText: 'Enter your spouse name',
                        formatters: [7],
                        keyboardType: 4,
                        textCapitalization: 4,
                        validations: { error: 'Please enter spouse name' },
                      },
                    },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select your marital status',
                  },
                },
                {
                  type: 7,
                  key: 'vehicleInfo',
                  padding: { top: 8 },
                  labelText: 'Vehicle',
                  options: [
                    { id: 'Two wheeler', value: 'Two wheeler' },
                    { id: 'Four wheeler', value: 'Four wheeler' },
                    {
                      id: 'Two wheeler, Four wheeler',
                      value: 'I own both a 2-wheeler and a 4-wheeler',
                    },
                    { id: 'None', value: 'None' },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select a vehicle',
                  },
                },
                {
                  type: 7,
                  key: 'educationInfo',
                  padding: { top: 8 },
                  labelText: 'Education',
                  options: [
                    {
                      id: 'Less than High School',
                      value: 'Less than High School',
                    },
                    {
                      id: 'Completed High School',
                      value: 'Completed High School',
                    },
                    { id: 'Diploma', value: 'Diploma' },
                    { id: 'Graduate Degree', value: 'Graduate Degree' },
                    { id: 'Postgraduate Degree', value: 'Postgraduate Degree' },
                    { id: 'Masters Degree', value: 'Masters Degree' },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select your education',
                  },
                },
                {
                  type: 7,
                  key: 'residentialInfo',
                  padding: { top: 8 },
                  labelText: 'Residential status',
                  options: [
                    { id: 'Owned', value: 'Owned' },
                    { id: 'Parental', value: 'Parental' },
                    { id: 'Company provided', value: 'Company provided' },
                    { id: 'Rented', value: 'Rented' },
                  ],
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select your education',
                  },
                },
                {
                  type: 1,
                  key: 'bankId',
                  isSearch: true,
                  padding: { top: 8 },
                  labelText: 'Salary bank',
                  title: 'Salary bank account',
                  description:
                    'Select bank where your salary is being deposited',
                  options: bankList,
                  validations: {
                    regex: '^(?!s*$).+',
                    error: 'Please select a bank',
                  },
                },
              ],
            },
          },
          {
            type: 2,
            key: 'continue',
            title: 'Continue',
            padding: { top: 20 },
            apiData: {
              endpoint: `${HOST_URL}v4/user/submitNewUserDetails`,
              requestType: 'POST',
              ansKeys: [
                {
                  key: 'email',
                  validations: {
                    regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$',
                    error: 'Please enter a valid email ID',
                  },
                },
                {
                  key: 'pan',
                  validations: {
                    regex: '^[A-Z]{5}[0-9]{4}[A-Z]{1}',
                    error: 'Please enter a valid PAN number',
                  },
                },
                { key: 'purposeId' },
                ...(!communicationLanguage
                  ? [{ key: 'communicationLanguage' }]
                  : []),
                { key: 'politicallyExposed' },
                { key: 'empInfo' },
                {
                  key: 'maritalInfo',
                  hasSubKey: true,
                  subKeys: [
                    {
                      key: 'motherName',
                      validations: { error: 'Please enter mother name' },
                    },
                    {
                      key: 'spouseName',
                      validations: { error: 'Please enter spouse name' },
                    },
                  ],
                },
                { key: 'vehicleInfo' },
                { key: 'educationInfo' },
                { key: 'residentialInfo' },
                { key: 'bankId' },
                {
                  key: 'bankId',
                  hasSubKey: true,
                  subKeys: [{ key: 'bankStatement' }],
                },
              ],
              syncUserData: true,
              submitCurrentQuestion: true,
              isRedirect: true,
              canClearState: true,
            },
          },
        ],
      },
    },
  };
}

export function kLspBasicDetailsScreen(
  communicationLanguage,
  purposeList,
  bankList,
) {
  return {
    showAppBar: true,
    appBarActions: [
      {
        type: 17,
        key: 'helpButton',
        padding: { right: 16 },
        texts: [
          {
            text: 'HELP',
            style: { color: '0xff128391', fontSize: 14, fontWeight: 5 },
            clickEvent: { route: 'helpAndSupportRoute', routingType: 1 },
          },
        ],
      },
    ],
    appBarColor: '0xffFFFFFF',
    appBarElevation: 0,
    appBarLeading: {
      type: 7,
      key: 'back',
      iconCodePoint: '62834',
      color: '0xff020C0D',
      size: 28,
      clickEvent: { route: 'dashboardRoute', routingType: 3 },
    },
    surfaceTintColor: '0xffFFFFFF',
    backgroundColor: '0xffFFFFFF',
    body: {
      type: 15,
      key: 'body',
      physics: 3,
      padding: { left: 16, right: 16, bottom: 15 },
      child: {
        type: 3,
        key: 'view',
        crossAxisAlignment: 4,
        children: [
          {
            type: 17,
            key: 'title',
            texts: [
              {
                text: 'Basic details',
                style: { color: '0xff020C0D', fontSize: 26, fontWeight: 6 },
              },
            ],
          },
          {
            type: 17,
            key: 'description',
            texts: [
              {
                text: 'To proceed with your loan application, we need some basic details about you.',
                style: { color: '0xff718589', fontSize: 14, fontWeight: 5 },
              },
            ],
          },
          {
            type: 18,
            key: 'separator',
            padding: { top: 30 },
            physics: 5,
            fields: [
              {
                type: 9,
                key: 'email',
                isEmailPicker: true,
                labelText: 'Email ID',
                hintText: 'Enter your valid email address',
                formatters: [2],
                keyboardType: 2,
                infoMsg:
                  'To confirm your email address, we will send a verification link to this email',
                validations: {
                  regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$',
                  error: 'Please enter a valid email ID',
                },
              },
              {
                type: 9,
                key: 'pan',
                padding: { top: 15 },
                labelText: 'PAN number',
                hintText: 'Enter your PAN number',
                maxLength: 10,
                formatters: [4],
                keyboardType: 9,
                textCapitalization: 1,
                infoField: {
                  type: 8,
                  key: 'panInfo',
                  svgURL:
                    'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Feb-2025/1738732609779.svg',
                  rWidth: 1.0,
                },
                validations: {
                  regex: '^[A-Z]{5}[0-9]{4}[A-Z]{1}',
                  error: 'Please enter a valid PAN number',
                },
              },
              {
                type: 10,
                key: 'purposeId',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/loan_purpose.svg',
                labelText: 'Loan purpose',
                title: 'Loan purpose',
                description: 'Select your loan purpose',
                options: purposeList,
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select a loan purpose',
                },
              },
              ...(!communicationLanguage
                ? [
                    {
                      type: 10,
                      key: 'communicationLanguage',
                      padding: { top: 15 },
                      svgIcon:
                        'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/mode_of_communication(1).svg',
                      labelText: 'Mode of communication',
                      title: 'Mode of communication',
                      description: 'Language comfortable for you',
                      options: [
                        { id: 1, value: 'English' },
                        { id: 2, value: 'Hindi' },
                      ],
                      validations: {
                        regex: '^(?!s*$).+',
                        error: 'Please select your communication language',
                      },
                    },
                  ]
                : []),
              {
                type: 10,
                key: 'politicallyExposed',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/politically_exposed.svg',
                labelText: 'Politically exposed',
                title: 'Politically exposed?',
                description: 'Are you a politically exposed person?',
                options: [
                  { id: 0, value: 'No' },
                  { id: 1, value: 'Yes' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select your political exposition status',
                },
              },
              {
                type: 10,
                key: 'empInfo',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/employment_status.svg',
                labelText: 'Employment status',
                title: 'Your employment status',
                description: 'Little info about your work!',
                options: [
                  { id: 'Salaried', value: 'Salaried' },
                  { id: 'Consultant', value: 'Consultant' },
                  { id: 'Self-Employed', value: 'Self-Employed' },
                  { id: 'Retired', value: 'Retired' },
                  { id: 'Student', value: 'Student' },
                  { id: 'Homemaker', value: 'Homemaker' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select an employment status',
                },
              },
              {
                type: 10,
                key: 'maritalInfo',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/marital_status.svg',
                labelText: 'Marital status',
                title: 'Your marital status',
                description: 'Select your current relationship status',
                options: [
                  {
                    id: 'Single',
                    value: 'Single',
                    subFieldKey: 'motherName',
                    subField: {
                      type: 9,
                      key: 'motherName',
                      padding: { top: 15 },
                      labelText: 'Mother name',
                      hintText: 'Enter your mother name',
                      formatters: [7],
                      keyboardType: 4,
                      textCapitalization: 4,
                      validations: { error: 'Please enter mother name' },
                    },
                  },
                  {
                    id: 'Married',
                    value: 'Married',
                    subFieldKey: 'spouseName',
                    subField: {
                      type: 9,
                      key: 'spouseName',
                      padding: { top: 15 },
                      labelText: 'Spouse name',
                      hintText: 'Enter your spouse name',
                      formatters: [7],
                      keyboardType: 4,
                      textCapitalization: 4,
                      validations: { error: 'Please enter spouse name' },
                    },
                  },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select your marital status',
                },
              },
              {
                type: 10,
                key: 'vehicleInfo',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/vehical_ownership.svg',
                labelText: 'Vehicle',
                title: 'Vehicle ownership',
                description: 'Please select your vehicle type',
                options: [
                  { id: 'Two wheeler', value: 'Two wheeler' },
                  { id: 'Four wheeler', value: 'Four wheeler' },
                  {
                    id: 'Two wheeler, Four wheeler',
                    value: 'I own both a 2-wheeler and a 4-wheeler',
                  },
                  { id: 'None', value: 'None' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select a vehicle',
                },
              },
              {
                type: 10,
                key: 'educationInfo',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/education_deatils.svg',
                labelText: 'Education',
                title: 'Educational background',
                description: 'What is your highest level of education?',
                options: [
                  {
                    id: 'Less than High School',
                    value: 'Less than High School',
                  },
                  {
                    id: 'Completed High School',
                    value: 'Completed High School',
                  },
                  { id: 'Diploma', value: 'Diploma' },
                  { id: 'Graduate Degree', value: 'Graduate Degree' },
                  { id: 'Postgraduate Degree', value: 'Postgraduate Degree' },
                  { id: 'Masters Degree', value: 'Masters Degree' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select your education',
                },
              },
              {
                type: 10,
                key: 'residentialInfo',
                padding: { top: 15 },
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/residential_status.svg',
                labelText: 'Residential status',
                title: 'Your residential status',
                description: 'Your primary housing arrangement',
                options: [
                  { id: 'Owned', value: 'Owned' },
                  { id: 'Parental', value: 'Parental' },
                  { id: 'Company provided', value: 'Company provided' },
                  { id: 'Rented', value: 'Rented' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select your education',
                },
              },
              {
                type: 10,
                key: 'bankId',
                padding: { top: 15 },
                isSearch: true,
                svgIcon:
                  'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/salary_bank(2).svg',
                labelText: 'Salary bank',
                title: 'Salary bank account',
                description: 'Select bank where your salary is being deposited',
                options: bankList,
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select a bank',
                },
              },
            ],
          },
        ],
      },
    },
    bottomNavigationBar: {
      type: 4,
      key: 'bottomBar',
      height: 85,
      rWidth: 1.0,
      color: '0xffFFFFFF',
      border: {
        top: { color: '0x8095B0B5', width: 0.5 },
      },
      child: {
        type: 1,
        key: 'continue',
        padding: { top: 16, left: 16, right: 16, bottom: 16 },
        title: 'Continue',
        apiData: {
          endpoint: `${EnvConfig.url.lspBaseLink}/v4/user/submitNewUserDetails`,
          requestType: 'POST',
          ansKeys: [
            {
              key: 'email',
              validations: {
                regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$',
                error: 'Please enter a valid email ID',
              },
            },
            {
              key: 'pan',
              validations: {
                regex: '^[A-Z]{5}[0-9]{4}[A-Z]{1}',
                error: 'Please enter a valid PAN number',
              },
            },
            { key: 'purposeId' },
            ...(!communicationLanguage
              ? [{ key: 'communicationLanguage' }]
              : []),
            { key: 'politicallyExposed' },
            { key: 'empInfo' },
            {
              key: 'maritalInfo',
              hasSubKey: true,
              subKeys: [
                {
                  key: 'motherName',
                  validations: { error: 'Please enter mother name' },
                },
                {
                  key: 'spouseName',
                  validations: { error: 'Please enter spouse name' },
                },
              ],
            },
            { key: 'vehicleInfo' },
            { key: 'educationInfo' },
            { key: 'residentialInfo' },
            { key: 'bankId' },
            {
              key: 'bankId',
              hasSubKey: true,
              subKeys: [{ key: 'bankStatement' }],
            },
          ],
          syncUserData: true,
          submitCurrentQuestion: true,
          isRedirect: true,
          canClearState: true,
          loaderData: {
            icon: '',
            title: 'Setting Up Your Account',
            description:
              "We're saving your details and preparing your account for the next step",
          },
        },
      },
    },
  };
}

export const kReapplyBasicDetailsScreen: any = {
  showAppBar: true,
  appBarColor: '0xffF1F1F1',
  appBarElevation: 0,
  appBarLeading: {
    type: 9,
    key: 'back',
    iconCodePoint: '62832',
    color: '0x7f040404',
    size: 18,
    clickEvent: { route: 'dashboardRoute', routingType: 3 },
  },
  surfaceTintColor: '0xffF1F1F1',
  backgroundColor: '0xffF1F1F1',
  body: {
    type: 17,
    key: 'body',
    padding: { left: 16, top: 15, right: 16, bottom: 15 },
    child: {
      type: 4,
      key: 'view',
      crossAxisAlignment: 2,
      children: [
        {
          type: 10,
          key: 'picture',
          svgURL:
            'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jan-2025/*************.svg',
        },
        {
          type: 19,
          key: 'title',
          padding: { top: 15 },
          texts: [
            {
              text: 'Welcome back',
              style: { color: '0xff000000', fontSize: 18, fontWeight: 5 },
            },
          ],
        },
        {
          type: 19,
          key: 'description',
          padding: { top: 8 },
          textAlign: 1,
          texts: [
            {
              text: 'Fill the below mentioned details to get a loan',
              style: { color: '0x99000000', fontSize: 12, fontWeight: 4 },
            },
          ],
        },
        {
          type: 5,
          key: 'basicDetails',
          color: '0xffffffff',
          borderRadius: {
            bottomLeft: 12,
            bottomRight: 12,
            topLeft: 12,
            topRight: 12,
          },
          margin: { top: 25 },
          boxShadow: {
            color: '0x66000000',
            blurRadius: 0.0,
            spreadRadius: 0.0,
            x: 1.0,
            y: 1.0,
          },
          child: {
            type: 4,
            key: 'widgets',
            children: [
              {
                type: 15,
                key: 'detailRow',
                padding: { left: 25, top: 15, right: 25, bottom: 15 },
                crossAxisAlignment: 2,
                mainAxisAlignment: 6,
                children: [
                  {
                    type: 19,
                    key: 'number',
                    texts: [
                      {
                        text: '1',
                        style: {
                          color: '0xff000000',
                          fontSize: 20,
                          fontWeight: 5,
                        },
                      },
                    ],
                  },
                  {
                    type: 4,
                    key: 'detailsColumn',
                    padding: { left: 25 },
                    crossAxisAlignment: 4,
                    children: [
                      {
                        type: 19,
                        key: 'title',
                        texts: [
                          {
                            text: 'Basic details',
                            style: {
                              color: '0xff000000',
                              fontSize: 14,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                      {
                        type: 19,
                        key: 'subtitle',
                        texts: [
                          {
                            text: 'Status: Pending',
                            style: {
                              color: '0xffFF0000',
                              fontSize: 12,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                type: 5,
                key: 'divider',
                height: 1.5,
                rWidth: 1.0,
                color: '0xffEBEBEB',
              },
              {
                type: 7,
                key: 'purposeId',
                padding: { left: 10, top: 10, right: 10 },
                labelText: 'Loan purpose',
                options: [
                  { id: 1, value: 'Fulfill your business needs' },
                  { id: 2, value: 'Want to gift loved ones' },
                  { id: 3, value: 'Travel deal which can’t wait' },
                  { id: 4, value: 'Upgrade your Mobile Phone' },
                  { id: 5, value: 'Education' },
                  { id: 6, value: 'Home Improvement Needs' },
                  { id: 7, value: 'Two Wheeler Loan' },
                  { id: 8, value: 'Medical' },
                  { id: 10, value: 'Personal Loan' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select a loan purpose',
                },
              },
              {
                type: 7,
                key: 'politicallyExposed',
                padding: { left: 10, top: 10, right: 10, bottom: 10 },
                labelText: 'Politically exposed',
                options: [
                  { id: 0, value: 'No' },
                  { id: 1, value: 'Yes' },
                ],
                validations: {
                  regex: '^(?!s*$).+',
                  error: 'Please select your political exposition status',
                },
              },
            ],
          },
        },
        {
          type: 5,
          key: 'employment',
          color: '0xffffffff',
          borderRadius: {
            bottomLeft: 12,
            bottomRight: 12,
            topLeft: 12,
            topRight: 12,
          },
          margin: { top: 15 },
          boxShadow: {
            color: '0x66000000',
            blurRadius: 0.0,
            spreadRadius: 0.0,
            x: 1.0,
            y: 1.0,
          },
          child: {
            type: 4,
            key: 'widgets',
            children: [
              {
                type: 15,
                key: 'detailRow',
                padding: { left: 25, top: 15, right: 25, bottom: 15 },
                crossAxisAlignment: 2,
                mainAxisAlignment: 6,
                children: [
                  {
                    type: 19,
                    key: 'number',
                    texts: [
                      {
                        text: '2',
                        style: {
                          color: '0xff000000',
                          fontSize: 20,
                          fontWeight: 5,
                        },
                      },
                    ],
                  },
                  {
                    type: 4,
                    key: 'detailsColumn',
                    padding: { left: 25 },
                    crossAxisAlignment: 4,
                    children: [
                      {
                        type: 19,
                        key: 'title',
                        texts: [
                          {
                            text: 'Employment',
                            style: {
                              color: '0xff000000',
                              fontSize: 14,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                      {
                        type: 19,
                        key: 'subtitle',
                        texts: [
                          {
                            text: 'Status: Pending',
                            style: {
                              color: '0xffFF0000',
                              fontSize: 12,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        },
      ],
    },
  },
  bottomNavigationBar: {
    type: 5,
    key: 'bottomBar',
    height: 90,
    rWidth: 1.0,
    color: '0xffF1F1F1',
    child: {
      type: 2,
      key: 'continue',
      padding: { top: 16, left: 16, right: 16, bottom: 16 },
      title: 'Continue',
      apiData: {
        endpoint: `${HOST_URL}v4/user/submitBasicDetails`,
        requestType: 'POST',
        ansKeys: [{ key: 'purposeId' }, { key: 'politicallyExposed' }],
        syncUserData: true,
        submitCurrentQuestion: true,
        isRedirect: true,
        canClearState: true,
      },
    },
  },
};
